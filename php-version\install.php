<?php
/**
 * GPA Calculator Installation Script
 * تثبيت حاسبة المعدل التراكمي
 *
 * Developer: <PERSON>
 * LinkedIn: https://www.linkedin.com/in/mohamed-elharany/
 * Message: صُنع بكل حب من الطلاب للطلاب
 */

session_start();

// Installation configuration
$config = [
    'db_host' => 'localhost',
    'db_name' => 'topstsbu_gpa',
    'db_user' => 'root',
    'db_pass' => '',
    'admin_username' => 'admin',
    'admin_password' => 'admin123',
    'admin_email' => '<EMAIL>'
];

$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // Database configuration
            $config['db_host'] = $_POST['db_host'] ?? 'localhost';
            $config['db_name'] = $_POST['db_name'] ?? 'topstsbu_gpa';
            $config['db_user'] = $_POST['db_user'] ?? 'root';
            $config['db_pass'] = $_POST['db_pass'] ?? '';

            // Test database connection
            try {
                $pdo = new PDO("mysql:host={$config['db_host']};charset=utf8mb4", $config['db_user'], $config['db_pass']);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Create database if not exists
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

                $_SESSION['install_config'] = $config;
                $success[] = 'تم الاتصال بقاعدة البيانات بنجاح';
                header('Location: install.php?step=2');
                exit;
            } catch (PDOException $e) {
                $errors[] = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
            }
            break;

        case 2:
            // Admin configuration
            $config = $_SESSION['install_config'] ?? $config;
            $config['admin_username'] = $_POST['admin_username'] ?? 'admin';
            $config['admin_password'] = $_POST['admin_password'] ?? '';
            $config['admin_email'] = $_POST['admin_email'] ?? '';

            if (empty($config['admin_password']) || strlen($config['admin_password']) < 6) {
                $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
            } else {
                $_SESSION['install_config'] = $config;
                header('Location: install.php?step=3');
                exit;
            }
            break;

        case 3:
            // Install database and create admin
            $config = $_SESSION['install_config'] ?? $config;

            try {
                $pdo = new PDO("mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4",
                              $config['db_user'], $config['db_pass']);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Create tables
                createTables($pdo);

                // Insert default data
                insertDefaultData($pdo, $config);

                // Create config file
                createConfigFile($config);

                $_SESSION['installation_complete'] = true;
                header('Location: install.php?step=4');
                exit;

            } catch (Exception $e) {
                $errors[] = 'خطأ في التثبيت: ' . $e->getMessage();
            }
            break;
    }
}

function createTables($pdo) {
    $tables = [
        // Universities table
        "CREATE TABLE IF NOT EXISTS universities (
            id VARCHAR(50) PRIMARY KEY,
            name_ar VARCHAR(255) NOT NULL,
            name_en VARCHAR(255) NOT NULL,
            country VARCHAR(100) DEFAULT 'Saudi Arabia',
            grading_system VARCHAR(50) DEFAULT 'saudi',
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Grading systems table
        "CREATE TABLE IF NOT EXISTS grading_systems (
            id VARCHAR(50) PRIMARY KEY,
            name_ar VARCHAR(255) NOT NULL,
            name_en VARCHAR(255) NOT NULL,
            description_ar TEXT,
            description_en TEXT,
            max_points DECIMAL(3,2) DEFAULT 4.00,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Grading scales table
        "CREATE TABLE IF NOT EXISTS grading_scales (
            id INT AUTO_INCREMENT PRIMARY KEY,
            grading_system_id VARCHAR(50) NOT NULL,
            grade VARCHAR(10) NOT NULL,
            points DECIMAL(3,2) NOT NULL,
            description_ar VARCHAR(100) NOT NULL,
            description_en VARCHAR(100) NOT NULL,
            min_percentage DECIMAL(5,2) DEFAULT 0,
            max_percentage DECIMAL(5,2) DEFAULT 100,
            color_code VARCHAR(7) DEFAULT '#3B82F6',
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_grade (grading_system_id, grade),
            FOREIGN KEY (grading_system_id) REFERENCES grading_systems(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // University grades table
        "CREATE TABLE IF NOT EXISTS university_grades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            university_id VARCHAR(50) NOT NULL,
            grade VARCHAR(10) NOT NULL,
            points DECIMAL(3,2) NOT NULL,
            description_ar VARCHAR(100) NOT NULL,
            description_en VARCHAR(100) NOT NULL,
            min_percentage DECIMAL(5,2) DEFAULT 0,
            max_percentage DECIMAL(5,2) DEFAULT 100,
            color_code VARCHAR(7) DEFAULT '#3B82F6',
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_university_grade (university_id, grade),
            INDEX idx_university_id (university_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // University GPA classifications table
        "CREATE TABLE IF NOT EXISTS university_gpa_classifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            university_id VARCHAR(50) NOT NULL,
            min_gpa DECIMAL(3,2) NOT NULL,
            max_gpa DECIMAL(3,2) NOT NULL,
            classification_ar VARCHAR(100) NOT NULL,
            classification_en VARCHAR(100) NOT NULL,
            color_code VARCHAR(7) DEFAULT '#3B82F6',
            display_order INT DEFAULT 0,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_university_id (university_id),
            INDEX idx_gpa_range (min_gpa, max_gpa),
            INDEX idx_display_order (display_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Students table
        "CREATE TABLE IF NOT EXISTS students (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            email VARCHAR(255),
            university VARCHAR(50) DEFAULT 'aou',
            grading_system VARCHAR(50) DEFAULT 'saudi',
            semester_gpa DECIMAL(3,2) DEFAULT 0,
            cumulative_gpa DECIMAL(3,2) DEFAULT 0,
            total_hours INT DEFAULT 0,
            classification VARCHAR(100),
            ip_address VARCHAR(45),
            user_agent TEXT,
            share_link VARCHAR(100) UNIQUE,
            link_expires_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_share_link (share_link),
            INDEX idx_university (university),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Courses table
        "CREATE TABLE IF NOT EXISTS courses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_code VARCHAR(20) NOT NULL,
            course_title_ar VARCHAR(255) NOT NULL,
            course_title_en VARCHAR(255) NOT NULL,
            credit_hours INT NOT NULL DEFAULT 3,
            university_id VARCHAR(50) DEFAULT 'aou',
            department VARCHAR(100),
            level VARCHAR(50),
            prerequisites TEXT,
            description_ar TEXT,
            description_en TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_course (course_code, university_id),
            INDEX idx_university (university_id),
            INDEX idx_department (department),
            INDEX idx_level (level)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Admin users table
        "CREATE TABLE IF NOT EXISTS admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            full_name VARCHAR(255),
            role ENUM('admin', 'moderator') DEFAULT 'admin',
            is_active TINYINT(1) DEFAULT 1,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Settings table
        "CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
            description_ar TEXT,
            description_en TEXT,
            is_public TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // Notifications table
        "CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title_ar VARCHAR(255) NOT NULL,
            title_en VARCHAR(255) NOT NULL,
            message_ar TEXT NOT NULL,
            message_en TEXT NOT NULL,
            type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
            target_audience ENUM('all', 'students', 'admins') DEFAULT 'all',
            display_type ENUM('popup', 'inline', 'both') DEFAULT 'popup',
            is_active TINYINT(1) DEFAULT 1,
            start_date TIMESTAMP NULL,
            end_date TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];

    foreach ($tables as $sql) {
        $pdo->exec($sql);
    }
}

function insertDefaultData($pdo, $config) {
    // Insert default universities
    $universities = [
        ['aou', 'الجامعة العربية المفتوحة', 'Arab Open University', 'Saudi Arabia', 'saudi'],
        ['ksu', 'جامعة الملك سعود', 'King Saud University', 'Saudi Arabia', 'saudi'],
        ['kau', 'جامعة الملك عبدالعزيز', 'King Abdulaziz University', 'Saudi Arabia', 'saudi'],
        ['kfupm', 'جامعة الملك فهد للبترول والمعادن', 'King Fahd University of Petroleum and Minerals', 'Saudi Arabia', 'saudi'],
        ['kfu', 'جامعة الملك فيصل', 'King Faisal University', 'Saudi Arabia', 'saudi'],
        ['iu', 'الجامعة الإسلامية', 'Islamic University', 'Saudi Arabia', 'saudi'],
        ['disabled', 'معطل', 'Disabled', 'N/A', 'saudi']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO universities (id, name_ar, name_en, country, grading_system) VALUES (?, ?, ?, ?, ?)");
    foreach ($universities as $university) {
        $stmt->execute($university);
    }

    // Insert default grading systems
    $gradingSystems = [
        ['saudi', 'النظام السعودي', 'Saudi System', 'النظام المعتمد في الجامعات السعودية', 'Standard system used in Saudi universities', 4.00],
        ['american', 'النظام الأمريكي', 'American System', 'النظام الأمريكي المعتمد دولياً', 'International American system', 4.00],
        ['british', 'النظام البريطاني', 'British System', 'النظام البريطاني', 'British grading system', 4.00]
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO grading_systems (id, name_ar, name_en, description_ar, description_en, max_points) VALUES (?, ?, ?, ?, ?, ?)");
    foreach ($gradingSystems as $system) {
        $stmt->execute($system);
    }

    // Insert default grading scales
    $gradingScales = [
        // Saudi system
        ['saudi', 'A+', 4.00, 'ممتاز مرتفع', 'Excellent Plus', 95, 100, '#10B981'],
        ['saudi', 'A', 4.00, 'ممتاز', 'Excellent', 90, 94, '#059669'],
        ['saudi', 'B+', 3.50, 'جيد جداً مرتفع', 'Very Good Plus', 85, 89, '#3B82F6'],
        ['saudi', 'B', 3.00, 'جيد جداً', 'Very Good', 80, 84, '#1D4ED8'],
        ['saudi', 'C+', 2.50, 'جيد مرتفع', 'Good Plus', 75, 79, '#F59E0B'],
        ['saudi', 'C', 2.00, 'جيد', 'Good', 70, 74, '#D97706'],
        ['saudi', 'D+', 1.50, 'مقبول مرتفع', 'Pass Plus', 65, 69, '#F97316'],
        ['saudi', 'D', 1.00, 'مقبول', 'Pass', 60, 64, '#EA580C'],
        ['saudi', 'F', 0.00, 'راسب', 'Fail', 0, 59, '#EF4444']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO grading_scales (grading_system_id, grade, points, description_ar, description_en, min_percentage, max_percentage, color_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    foreach ($gradingScales as $scale) {
        $stmt->execute($scale);
    }

    // Insert default GPA classifications for all universities
    $classifications = [
        [4.00, 3.67, 'ممتاز', 'Excellent', '#10B981', 1],
        [3.66, 3.00, 'جيد جداً', 'Very Good', '#3B82F6', 2],
        [2.99, 2.33, 'جيد', 'Good', '#F59E0B', 3],
        [2.32, 2.00, 'مقبول', 'Fair', '#F97316', 4],
        [1.99, 0.00, 'راسب', 'Fail', '#EF4444', 5]
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO university_gpa_classifications (university_id, min_gpa, max_gpa, classification_ar, classification_en, color_code, display_order) VALUES (?, ?, ?, ?, ?, ?, ?)");
    foreach ($universities as $university) {
        if ($university[0] !== 'disabled') {
            foreach ($classifications as $classification) {
                $stmt->execute([
                    $university[0],
                    $classification[1], // min_gpa
                    $classification[0], // max_gpa
                    $classification[2], // classification_ar
                    $classification[3], // classification_en
                    $classification[4], // color_code
                    $classification[5]  // display_order
                ]);
            }
        }
    }

    // Insert sample courses
    $courses = [
        ['MATH101', 'الرياضيات الأساسية', 'Basic Mathematics', 3, 'aou', 'Mathematics', 'Undergraduate'],
        ['PHYS101', 'الفيزياء العامة', 'General Physics', 3, 'aou', 'Physics', 'Undergraduate'],
        ['CHEM101', 'الكيمياء العامة', 'General Chemistry', 3, 'aou', 'Chemistry', 'Undergraduate'],
        ['ENG101', 'اللغة الإنجليزية', 'English Language', 3, 'aou', 'Languages', 'Undergraduate'],
        ['CS101', 'مقدمة في الحاسوب', 'Introduction to Computer Science', 3, 'aou', 'Computer Science', 'Undergraduate']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO courses (course_code, course_title_ar, course_title_en, credit_hours, university_id, department, level) VALUES (?, ?, ?, ?, ?, ?, ?)");
    foreach ($courses as $course) {
        $stmt->execute($course);
    }

    // Create admin user
    $hashedPassword = password_hash($config['admin_password'], PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO admin_users (username, password, email, full_name, role) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([
        $config['admin_username'],
        $hashedPassword,
        $config['admin_email'],
        'System Administrator',
        'admin'
    ]);

    // Insert default settings
    $settings = [
        ['site_name_ar', 'حاسبة المعدل التراكمي', 'string', 'اسم الموقع بالعربية', 'Site name in Arabic'],
        ['site_name_en', 'GPA Calculator', 'string', 'اسم الموقع بالإنجليزية', 'Site name in English'],
        ['default_language', 'ar', 'string', 'اللغة الافتراضية', 'Default language'],
        ['default_university', 'aou', 'string', 'الجامعة الافتراضية', 'Default university'],
        ['maintenance_mode', '0', 'boolean', 'وضع الصيانة', 'Maintenance mode'],
        ['student_link_expiry_days', '30', 'number', 'مدة انتهاء رابط الطالب بالأيام', 'Student link expiry in days'],
        ['max_courses_per_calculation', '20', 'number', 'الحد الأقصى للمواد في الحساب الواحد', 'Maximum courses per calculation'],
        ['enable_notifications', '1', 'boolean', 'تفعيل الإشعارات', 'Enable notifications'],
        ['notification_display_type', 'popup', 'string', 'نوع عرض الإشعارات', 'Notification display type']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description_ar, description_en) VALUES (?, ?, ?, ?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
}

function createConfigFile($config) {
    $configContent = "<?php
/**
 * GPA Calculator Configuration File
 * Generated automatically during installation
 */

// Database Configuration
define('DB_HOST', '{$config['db_host']}');
define('DB_NAME', '{$config['db_name']}');
define('DB_USER', '{$config['db_user']}');
define('DB_PASS', '{$config['db_pass']}');

// Application Configuration
define('APP_NAME', 'GPA Calculator');
define('APP_VERSION', '2.0.0');
define('APP_URL', 'http://' . \$_SERVER['HTTP_HOST'] . dirname(\$_SERVER['SCRIPT_NAME']));

// Security Configuration
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour
define('STUDENT_LINK_EXPIRY', 30 * 24 * 60 * 60); // 30 days

// Default Settings
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_UNIVERSITY', 'aou');

// File Paths
define('DATA_DIR', __DIR__ . '/data');
define('UPLOAD_DIR', __DIR__ . '/uploads');

// Create data directory if it doesn't exist
if (!file_exists(DATA_DIR)) {
    mkdir(DATA_DIR, 0755, true);
}

// Session Configuration
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_lifetime', 86400); // 24 hours
    session_start();
}

// Database Connection Function
function getDBConnection() {
    try {
        \$pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return \$pdo;
    } catch (PDOException \$e) {
        die('Database connection failed: ' . \$e->getMessage());
    }
}

// Installation check
define('INSTALLATION_COMPLETE', true);
?>";

    file_put_contents('config.php', $configContent);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت حاسبة المعدل التراكمي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .step-indicator {
            transition: all 0.3s ease;
        }
        .step-indicator.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .step-indicator.completed {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-purple-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-graduation-cap text-white text-3xl"></i>
            </div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">حاسبة المعدل التراكمي</h1>
            <p class="text-gray-600">معالج التثبيت - الإصدار 2.0</p>
            <div class="mt-4 text-sm text-gray-500">
                <p>المطور: محمد الحراني | <a href="https://www.linkedin.com/in/mohamed-elharany/" class="text-blue-600 hover:underline" target="_blank">LinkedIn</a></p>
                <p class="text-purple-600 font-semibold">صُنع بكل حب من الطلاب للطلاب</p>
            </div>
        </div>

        <!-- Step Indicator -->
        <div class="flex justify-center mb-8">
            <div class="flex items-center space-x-4 space-x-reverse">
                <?php for ($i = 1; $i <= 4; $i++): ?>
                    <div class="step-indicator w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold
                        <?php echo $i < $step ? 'completed' : ($i == $step ? 'active' : 'bg-gray-200 text-gray-500'); ?>">
                        <?php if ($i < $step): ?>
                            <i class="fas fa-check"></i>
                        <?php else: ?>
                            <?php echo $i; ?>
                        <?php endif; ?>
                    </div>
                    <?php if ($i < 4): ?>
                        <div class="w-8 h-1 bg-gray-200 <?php echo $i < $step ? 'bg-blue-400' : ''; ?>"></div>
                    <?php endif; ?>
                <?php endfor; ?>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-2xl shadow-xl p-8">

                <?php if (!empty($errors)): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle ml-2"></i>
                            <strong>خطأ!</strong>
                        </div>
                        <ul class="mt-2 list-disc list-inside">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle ml-2"></i>
                            <strong>نجح!</strong>
                        </div>
                        <ul class="mt-2 list-disc list-inside">
                            <?php foreach ($success as $msg): ?>
                                <li><?php echo htmlspecialchars($msg); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if ($step == 1): ?>
                    <!-- Step 1: Database Configuration -->
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">إعداد قاعدة البيانات</h2>
                        <p class="text-gray-600">أدخل بيانات الاتصال بقاعدة البيانات</p>
                    </div>

                    <form method="POST" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">خادم قاعدة البيانات</label>
                            <input type="text" name="db_host" value="<?php echo htmlspecialchars($config['db_host']); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="localhost" required>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم قاعدة البيانات</label>
                            <input type="text" name="db_name" value="<?php echo htmlspecialchars($config['db_name']); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="gpa_calculator" required>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                            <input type="text" name="db_user" value="<?php echo htmlspecialchars($config['db_user']); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="root" required>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                            <input type="password" name="db_pass" value="<?php echo htmlspecialchars($config['db_pass']); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="اتركه فارغاً إذا لم تكن هناك كلمة مرور">
                        </div>

                        <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-database ml-2"></i>
                            اختبار الاتصال والمتابعة
                        </button>
                    </form>

                <?php elseif ($step == 2): ?>
                    <!-- Step 2: Admin Configuration -->
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">إعداد حساب المدير</h2>
                        <p class="text-gray-600">أنشئ حساب المدير الرئيسي للنظام</p>
                    </div>

                    <form method="POST" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                            <input type="text" name="admin_username" value="<?php echo htmlspecialchars($config['admin_username']); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="admin" required>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                            <input type="password" name="admin_password"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="أدخل كلمة مرور قوية (6 أحرف على الأقل)" required minlength="6">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                            <input type="email" name="admin_email" value="<?php echo htmlspecialchars($config['admin_email']); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="<EMAIL>">
                        </div>

                        <div class="flex gap-4">
                            <a href="install.php?step=1" class="flex-1 bg-gray-500 text-white py-3 px-6 rounded-lg hover:bg-gray-600 transition-colors text-center">
                                <i class="fas fa-arrow-right ml-2"></i>
                                السابق
                            </a>
                            <button type="submit" class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
                                <i class="fas fa-user-shield ml-2"></i>
                                إنشاء المدير
                            </button>
                        </div>
                    </form>

                <?php elseif ($step == 3): ?>
                    <!-- Step 3: Installation -->
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">تثبيت النظام</h2>
                        <p class="text-gray-600">سيتم الآن إنشاء الجداول وإدراج البيانات الافتراضية</p>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                        <h3 class="font-semibold text-blue-800 mb-3">ما سيتم تثبيته:</h3>
                        <ul class="text-blue-700 space-y-2 text-sm">
                            <li>✅ إنشاء جداول قاعدة البيانات</li>
                            <li>✅ إدراج الجامعات السعودية الرئيسية</li>
                            <li>✅ إعداد أنظمة التقدير</li>
                            <li>✅ إضافة تصنيفات المعدل التراكمي</li>
                            <li>✅ إنشاء حساب المدير</li>
                            <li>✅ إعداد الإعدادات الافتراضية</li>
                            <li>✅ إنشاء ملف التكوين</li>
                        </ul>
                    </div>

                    <form method="POST" class="space-y-6">
                        <div class="flex gap-4">
                            <a href="install.php?step=2" class="flex-1 bg-gray-500 text-white py-3 px-6 rounded-lg hover:bg-gray-600 transition-colors text-center">
                                <i class="fas fa-arrow-right ml-2"></i>
                                السابق
                            </a>
                            <button type="submit" class="flex-1 bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 px-6 rounded-lg hover:from-green-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105">
                                <i class="fas fa-download ml-2"></i>
                                بدء التثبيت
                            </button>
                        </div>
                    </form>

                <?php elseif ($step == 4): ?>
                    <!-- Step 4: Completion -->
                    <div class="text-center">
                        <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-check text-green-600 text-3xl"></i>
                        </div>

                        <h2 class="text-3xl font-bold text-gray-800 mb-4">تم التثبيت بنجاح! 🎉</h2>
                        <p class="text-gray-600 mb-8">تم تثبيت حاسبة المعدل التراكمي بنجاح وهي جاهزة للاستخدام</p>

                        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                            <h3 class="font-semibold text-green-800 mb-3">معلومات تسجيل الدخول:</h3>
                            <div class="text-green-700 space-y-2">
                                <p><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($_SESSION['install_config']['admin_username'] ?? 'admin'); ?></p>
                                <p><strong>كلمة المرور:</strong> التي أدخلتها في الخطوة السابقة</p>
                                <p><strong>رابط لوحة الإدارة:</strong> <a href="simple_admin_login.php" class="text-blue-600 hover:underline">simple_admin_login.php</a></p>
                            </div>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                            <h3 class="font-semibold text-yellow-800 mb-3">تنبيهات أمنية مهمة:</h3>
                            <ul class="text-yellow-700 space-y-2 text-sm text-right">
                                <li>🔒 احذف ملف install.php بعد التثبيت</li>
                                <li>🔑 غيّر كلمة مرور المدير من لوحة الإدارة</li>
                                <li>🛡️ تأكد من تفعيل HTTPS في الإنتاج</li>
                                <li>💾 قم بعمل نسخة احتياطية من قاعدة البيانات</li>
                                <li>📁 تأكد من صلاحيات الملفات المناسبة</li>
                            </ul>
                        </div>

                        <div class="flex gap-4">
                            <a href="index.php" class="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 text-center">
                                <i class="fas fa-home ml-2"></i>
                                الصفحة الرئيسية
                            </a>
                            <a href="simple_admin_login.php" class="flex-1 bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 px-6 rounded-lg hover:from-green-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 text-center">
                                <i class="fas fa-user-shield ml-2"></i>
                                لوحة الإدارة
                            </a>
                        </div>

                        <div class="mt-8 text-center text-sm text-gray-500">
                            <p>شكراً لاستخدام حاسبة المعدل التراكمي</p>
                            <p class="text-purple-600 font-semibold">صُنع بكل حب من الطلاب للطلاب ❤️</p>
                        </div>
                    </div>

                <?php endif; ?>

            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-sm text-gray-500">
            <p>حاسبة المعدل التراكمي - الإصدار 2.0</p>
            <p>المطور: <a href="https://www.linkedin.com/in/mohamed-elharany/" class="text-blue-600 hover:underline" target="_blank">محمد الحراني</a></p>
        </div>
    </div>

    <script>
        // Auto-focus first input
        document.addEventListener('DOMContentLoaded', function() {
            const firstInput = document.querySelector('input[type="text"], input[type="password"], input[type="email"]');
            if (firstInput) {
                firstInput.focus();
            }
        });

        // Form validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري المعالجة...';
                }
            });
        });
    </script>
</body>
</html>

<?php
// Clean up session after installation
if ($step == 4 && isset($_SESSION['installation_complete'])) {
    unset($_SESSION['install_config']);
    unset($_SESSION['installation_complete']);
}
?>