<?php
/**
 * Create Grading Scales Table
 * إنشاء جدول سلم الدرجات
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$fixes = [];
$errors = [];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $fixes[] = "✅ اتصال قاعدة البيانات ناجح";
    
    // Create grading_scales table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS grading_scales (
            id INT AUTO_INCREMENT PRIMARY KEY,
            university_code VARCHAR(10) NOT NULL,
            grade VARCHAR(5) NOT NULL,
            points DECIMAL(3,2) NOT NULL,
            min_percentage DECIMAL(5,2) NULL,
            max_percentage DECIMAL(5,2) NULL,
            description VARCHAR(100) NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_university_grade (university_code, grade),
            INDEX idx_university_code (university_code),
            INDEX idx_grade (grade),
            INDEX idx_points (points),
            FOREIGN KEY (university_code) REFERENCES universities(code) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $fixes[] = "✅ تم إنشاء جدول grading_scales";
    
    // Insert default grading scales for existing universities
    $defaultScales = [
        'aou' => [
            ['A', 4.0, 90, 100, 'ممتاز'],
            ['B+', 3.5, 85, 89, 'جيد جداً مرتفع'],
            ['B', 3.0, 80, 84, 'جيد جداً'],
            ['C+', 2.5, 75, 79, 'جيد مرتفع'],
            ['C', 2.0, 70, 74, 'جيد'],
            ['D', 1.0, 60, 69, 'مقبول'],
            ['F', 0.0, 0, 59, 'راسب']
        ],
        'ksu' => [
            ['A+', 4.0, 95, 100, 'ممتاز مرتفع'],
            ['A', 3.75, 90, 94, 'ممتاز'],
            ['B+', 3.5, 85, 89, 'جيد جداً مرتفع'],
            ['B', 3.0, 80, 84, 'جيد جداً'],
            ['C+', 2.5, 75, 79, 'جيد مرتفع'],
            ['C', 2.0, 70, 74, 'جيد'],
            ['D+', 1.5, 65, 69, 'مقبول مرتفع'],
            ['D', 1.0, 60, 64, 'مقبول'],
            ['F', 0.0, 0, 59, 'راسب']
        ],
        'kau' => [
            ['A+', 4.0, 95, 100, 'ممتاز مرتفع'],
            ['A', 3.75, 90, 94, 'ممتاز'],
            ['B+', 3.5, 85, 89, 'جيد جداً مرتفع'],
            ['B', 3.0, 80, 84, 'جيد جداً'],
            ['C+', 2.5, 75, 79, 'جيد مرتفع'],
            ['C', 2.0, 70, 74, 'جيد'],
            ['D+', 1.5, 65, 69, 'مقبول مرتفع'],
            ['D', 1.0, 60, 64, 'مقبول'],
            ['F', 0.0, 0, 59, 'راسب']
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO grading_scales (university_code, grade, points, min_percentage, max_percentage, description) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    $totalInserted = 0;
    foreach ($defaultScales as $universityCode => $scales) {
        // Check if university exists
        $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM universities WHERE code = ?");
        $checkStmt->execute([$universityCode]);
        
        if ($checkStmt->fetchColumn() > 0) {
            foreach ($scales as $scale) {
                if ($stmt->execute([$universityCode, $scale[0], $scale[1], $scale[2], $scale[3], $scale[4]])) {
                    $totalInserted++;
                }
            }
            $fixes[] = "✅ تم إدراج سلم الدرجات لجامعة $universityCode";
        } else {
            $errors[] = "❌ جامعة $universityCode غير موجودة";
        }
    }
    
    $fixes[] = "✅ تم إدراج $totalInserted درجة في المجموع";
    
    // Test the new table
    $stmt = $pdo->query("SELECT COUNT(*) FROM grading_scales");
    $totalGrades = $stmt->fetchColumn();
    $fixes[] = "📊 إجمالي الدرجات في النظام: $totalGrades";
    
    // Show sample data
    $stmt = $pdo->query("
        SELECT gs.university_code, u.name_ar, gs.grade, gs.points, gs.description 
        FROM grading_scales gs 
        JOIN universities u ON gs.university_code = u.code 
        ORDER BY gs.university_code, gs.points DESC 
        LIMIT 10
    ");
    $sampleData = $stmt->fetchAll();
    
    $fixes[] = "📋 عينة من البيانات:";
    foreach ($sampleData as $row) {
        $fixes[] = "   {$row['name_ar']}: {$row['grade']} = {$row['points']} ({$row['description']})";
    }
    
} catch (PDOException $e) {
    $errors[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء جدول سلم الدرجات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-graduation-cap text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">إنشاء جدول سلم الدرجات</h1>
                <p class="text-gray-600">إنشاء نظام سلم الدرجات المنفصل لكل جامعة</p>
            </div>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Fixes -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-green-700 mb-4">
                    <i class="fas fa-check-circle ml-2"></i>
                    العمليات المكتملة (<?php echo count($fixes); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php foreach ($fixes as $fix): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg text-sm">
                            <?php echo htmlspecialchars($fix); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Errors -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-red-700 mb-4">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    الأخطاء (<?php echo count($errors); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($errors)): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-thumbs-up ml-2"></i>
                                <span>تم إنشاء النظام بنجاح!</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($errors as $error): ?>
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg text-sm">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Current Grading Scales -->
        <?php if (empty($errors)): ?>
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">سلم الدرجات الحالي</h2>
            
            <?php
            try {
                $stmt = $pdo->query("
                    SELECT gs.university_code, u.name_ar, gs.grade, gs.points, gs.min_percentage, gs.max_percentage, gs.description 
                    FROM grading_scales gs 
                    JOIN universities u ON gs.university_code = u.code 
                    ORDER BY gs.university_code, gs.points DESC
                ");
                $allGrades = $stmt->fetchAll();
                
                $groupedGrades = [];
                foreach ($allGrades as $grade) {
                    $groupedGrades[$grade['university_code']][] = $grade;
                }
                
                echo '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">';
                foreach ($groupedGrades as $universityCode => $grades) {
                    $universityName = $grades[0]['name_ar'];
                    echo '<div class="border rounded-lg p-4">';
                    echo '<h3 class="font-bold text-lg text-gray-800 mb-3">' . htmlspecialchars($universityName) . '</h3>';
                    echo '<div class="space-y-2">';
                    
                    foreach ($grades as $grade) {
                        echo '<div class="flex justify-between items-center text-sm">';
                        echo '<span class="font-medium">' . htmlspecialchars($grade['grade']) . '</span>';
                        echo '<span class="text-gray-600">' . $grade['points'] . '</span>';
                        echo '<span class="text-xs text-gray-500">(' . $grade['min_percentage'] . '-' . $grade['max_percentage'] . '%)</span>';
                        echo '</div>';
                    }
                    
                    echo '</div>';
                    echo '</div>';
                }
                echo '</div>';
                
            } catch (Exception $e) {
                echo '<p class="text-red-600">خطأ في عرض البيانات: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
            ?>
        </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
            <?php if (empty($errors)): ?>
                <a href="admin_grading_scales.php" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center">
                    <i class="fas fa-edit ml-2"></i>
                    إدارة سلم الدرجات
                </a>
                
                <a href="admin_universities.php" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center">
                    <i class="fas fa-university ml-2"></i>
                    إدارة الجامعات
                </a>
                
                <a href="admin_settings.php" class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center">
                    <i class="fas fa-cog ml-2"></i>
                    الإعدادات
                </a>
            <?php else: ?>
                <button onclick="location.reload()" class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة المحاولة
                </button>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="mt-8 text-center text-sm text-gray-500">
            <p>تم إنشاء جدول سلم الدرجات في: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>
</body>
</html>
