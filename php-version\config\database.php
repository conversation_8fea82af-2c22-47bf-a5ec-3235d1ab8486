<?php
/**
 * Database Configuration
 * إعدادات قاعدة البيانات
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'gpa_calculator'); // استخدام قاعدة البيانات الموجودة
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("فشل في الاتصال بقاعدة البيانات");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("خطأ في تنفيذ الاستعلام");
        }
    }
    
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->connection->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        return $this->query($sql, $params);
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }
    
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->fetch($sql, $params);
        return $result['count'];
    }
    
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        return $this->connection->rollback();
    }
    
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    // Prevent cloning
    private function __clone() {}
    
    // Prevent unserialization
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * Helper functions for database operations
 */

function getDB() {
    return Database::getInstance();
}

function dbQuery($sql, $params = []) {
    return getDB()->query($sql, $params);
}

function dbFetch($sql, $params = []) {
    return getDB()->fetch($sql, $params);
}

function dbFetchAll($sql, $params = []) {
    return getDB()->fetchAll($sql, $params);
}

function dbInsert($table, $data) {
    return getDB()->insert($table, $data);
}

function dbUpdate($table, $data, $where, $whereParams = []) {
    return getDB()->update($table, $data, $where, $whereParams);
}

function dbDelete($table, $where, $params = []) {
    return getDB()->delete($table, $where, $params);
}

function dbCount($table, $where = '1=1', $params = []) {
    return getDB()->count($table, $where, $params);
}

/**
 * Logging function for database activities
 */
function logActivity($adminId, $action, $description, $studentId = null, $oldData = null, $newData = null) {
    try {
        $data = [
            'admin_id' => $adminId,
            'student_id' => $studentId,
            'action' => $action,
            'description' => $description,
            'old_data' => $oldData ? json_encode($oldData) : null,
            'new_data' => $newData ? json_encode($newData) : null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        return dbInsert('activity_logs', $data);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Settings management functions
 */
function getSetting($key, $default = null) {
    try {
        $result = dbFetch("SELECT setting_value FROM settings WHERE setting_key = ?", [$key]);
        return $result ? $result['setting_value'] : $default;
    } catch (Exception $e) {
        error_log("Failed to get setting {$key}: " . $e->getMessage());
        return $default;
    }
}

function setSetting($key, $value, $adminId = null) {
    try {
        $existing = dbFetch("SELECT id FROM settings WHERE setting_key = ?", [$key]);
        
        if ($existing) {
            return dbUpdate('settings', 
                ['setting_value' => $value, 'updated_by' => $adminId], 
                'setting_key = ?', 
                [$key]
            );
        } else {
            return dbInsert('settings', [
                'setting_key' => $key,
                'setting_value' => $value,
                'updated_by' => $adminId
            ]);
        }
    } catch (Exception $e) {
        error_log("Failed to set setting {$key}: " . $e->getMessage());
        return false;
    }
}

function getSettings($category = null, $publicOnly = false) {
    try {
        $sql = "SELECT setting_key, setting_value, setting_type FROM settings WHERE 1=1";
        $params = [];
        
        if ($category) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }
        
        if ($publicOnly) {
            $sql .= " AND is_public = 1";
        }
        
        $results = dbFetchAll($sql, $params);
        $settings = [];
        
        foreach ($results as $row) {
            $value = $row['setting_value'];
            
            // Convert based on type
            switch ($row['setting_type']) {
                case 'boolean':
                    $value = (bool) $value;
                    break;
                case 'number':
                    $value = is_numeric($value) ? (float) $value : $value;
                    break;
                case 'json':
                    $value = json_decode($value, true);
                    break;
            }
            
            $settings[$row['setting_key']] = $value;
        }
        
        return $settings;
    } catch (Exception $e) {
        error_log("Failed to get settings: " . $e->getMessage());
        return [];
    }
}

/**
 * Initialize database connection on include
 */
try {
    Database::getInstance();
} catch (Exception $e) {
    // Log error but don't stop execution
    error_log("Database initialization warning: " . $e->getMessage());
}
?>
