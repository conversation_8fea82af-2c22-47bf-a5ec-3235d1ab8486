<?php
/**
 * Initialize data directory and create sample data
 */

// Create data directory
$data_dir = __DIR__ . '/data/';
if (!is_dir($data_dir)) {
    mkdir($data_dir, 0755, true);
    echo "تم إنشاء مجلد البيانات<br>";
}

// Create sample student data
$sample_students = [
    [
        'name' => 'أحمد محمد',
        'phone' => '0501234567',
        'gpa' => 3.75,
        'courses' => [
            ['name' => 'الرياضيات', 'hours' => 3, 'grade' => 'A'],
            ['name' => 'الفيزياء', 'hours' => 4, 'grade' => 'B+'],
            ['name' => 'الكيمياء', 'hours' => 3, 'grade' => 'A']
        ],
        'calculation_type' => 'semester',
        'previous_gpa' => 0,
        'previous_hours' => 0,
        'classification' => ['name' => 'ممتاز', 'class' => 'excellent'],
        'university' => 'aou',
        'grading_system' => 'aou',
        'created_at' => date('Y-m-d H:i:s'),
        'expires_at' => date('Y-m-d H:i:s', strtotime('+30 days'))
    ],
    [
        'name' => 'فاطمة علي',
        'phone' => '0507654321',
        'gpa' => 3.25,
        'courses' => [
            ['name' => 'الأدب العربي', 'hours' => 3, 'grade' => 'B+'],
            ['name' => 'التاريخ', 'hours' => 3, 'grade' => 'B'],
            ['name' => 'الجغرافيا', 'hours' => 2, 'grade' => 'A']
        ],
        'calculation_type' => 'semester',
        'previous_gpa' => 0,
        'previous_hours' => 0,
        'classification' => ['name' => 'جيد جداً مرتفع', 'class' => 'very-good-high'],
        'university' => 'aou',
        'grading_system' => 'aou',
        'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
        'expires_at' => date('Y-m-d H:i:s', strtotime('+29 days'))
    ],
    [
        'name' => 'محمد سالم',
        'phone' => '0509876543',
        'gpa' => 2.85,
        'courses' => [
            ['name' => 'الاقتصاد', 'hours' => 3, 'grade' => 'B'],
            ['name' => 'الإحصاء', 'hours' => 3, 'grade' => 'C+'],
            ['name' => 'المحاسبة', 'hours' => 4, 'grade' => 'B']
        ],
        'calculation_type' => 'semester',
        'previous_gpa' => 0,
        'previous_hours' => 0,
        'classification' => ['name' => 'جيد جداً', 'class' => 'very-good'],
        'university' => 'ksu',
        'grading_system' => 'aou',
        'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
        'expires_at' => date('Y-m-d H:i:s', strtotime('+28 days'))
    ],
    [
        'name' => 'نورا أحمد',
        'phone' => '0502468135',
        'gpa' => 3.95,
        'courses' => [
            ['name' => 'علوم الحاسوب', 'hours' => 4, 'grade' => 'A'],
            ['name' => 'البرمجة', 'hours' => 3, 'grade' => 'A'],
            ['name' => 'قواعد البيانات', 'hours' => 3, 'grade' => 'A']
        ],
        'calculation_type' => 'cumulative',
        'previous_gpa' => 3.8,
        'previous_hours' => 45,
        'classification' => ['name' => 'ممتاز', 'class' => 'excellent'],
        'university' => 'aou',
        'grading_system' => 'aou',
        'created_at' => date('Y-m-d H:i:s'),
        'expires_at' => date('Y-m-d H:i:s', strtotime('+30 days'))
    ],
    [
        'name' => 'خالد عبدالله',
        'phone' => '0501357924',
        'gpa' => 2.15,
        'courses' => [
            ['name' => 'الهندسة', 'hours' => 4, 'grade' => 'C'],
            ['name' => 'الرسم الهندسي', 'hours' => 2, 'grade' => 'C+'],
            ['name' => 'الميكانيكا', 'hours' => 3, 'grade' => 'D']
        ],
        'calculation_type' => 'semester',
        'previous_gpa' => 0,
        'previous_hours' => 0,
        'classification' => ['name' => 'جيد مرتفع', 'class' => 'good-high'],
        'university' => 'kau',
        'grading_system' => 'aou',
        'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
        'expires_at' => date('Y-m-d H:i:s', strtotime('+27 days'))
    ]
];

// Save sample data
foreach ($sample_students as $index => $student) {
    $link_id = 'sample_' . ($index + 1) . '_' . time();
    $file_path = $data_dir . $link_id . '.json';
    
    if (file_put_contents($file_path, json_encode($student, JSON_PRETTY_PRINT))) {
        echo "تم إنشاء بيانات الطالب: " . $student['name'] . "<br>";
    }
}

echo "<br><strong>تم إنشاء البيانات التجريبية بنجاح!</strong><br>";
echo "<a href='index.php' style='color: blue; text-decoration: underline;'>العودة للصفحة الرئيسية</a><br>";
echo "<a href='admin.php' style='color: purple; text-decoration: underline;'>الذهاب للوحة الإدارة</a>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .links {
            margin-top: 20px;
            text-align: center;
        }
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .links a:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 إعداد بيانات حاسبة المعدل التراكمي</h1>
        
        <div class="success">
            ✅ تم إنشاء مجلد البيانات بنجاح<br>
            ✅ تم إضافة 5 طلاب تجريبيين<br>
            ✅ النظام جاهز للاستخدام
        </div>
        
        <div class="links">
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="admin.php">👨‍💼 لوحة الإدارة</a>
            <a href="universities.php">🏛️ الجامعات</a>
        </div>
        
        <hr style="margin: 20px 0;">
        
        <h3>📊 البيانات التجريبية المضافة:</h3>
        <ul>
            <li><strong>أحمد محمد</strong> - معدل 3.75 (ممتاز)</li>
            <li><strong>فاطمة علي</strong> - معدل 3.25 (جيد جداً مرتفع)</li>
            <li><strong>محمد سالم</strong> - معدل 2.85 (جيد جداً)</li>
            <li><strong>نورا أحمد</strong> - معدل 3.95 (ممتاز)</li>
            <li><strong>خالد عبدالله</strong> - معدل 2.15 (جيد مرتفع)</li>
        </ul>
        
        <p><strong>ملاحظة:</strong> يمكنك الآن استخدام لوحة الإدارة لمشاهدة الإحصائيات وإدارة بيانات الطلاب.</p>
    </div>
</body>
</html>
