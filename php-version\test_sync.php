<?php
/**
 * Test Grading System Sync
 * اختبار مزامنة نظام التقدير
 */

try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>🔄 اختبار مزامنة نظام التقدير</h1>";
    
    // 1. Test database connection
    echo "<h2>1. ✅ اتصال قاعدة البيانات</h2>";
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br><br>";
    
    // 2. Test grading systems
    echo "<h2>2. 📊 أنظمة التقدير المتاحة</h2>";
    $systems = $pdo->query("SELECT * FROM grading_systems WHERE is_active = 1 ORDER BY name_ar")->fetchAll();
    
    if (empty($systems)) {
        echo "❌ لا توجد أنظمة تقدير نشطة<br>";
        echo "<a href='smart_fix.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تشغيل الإصلاح الذكي</a><br><br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
        echo "<tr style='background: #f0f0f0;'><th>المعرف</th><th>الاسم العربي</th><th>الاسم الإنجليزي</th><th>عدد الدرجات</th><th>الإجراءات</th></tr>";
        
        foreach ($systems as $system) {
            $gradesCount = $pdo->prepare("SELECT COUNT(*) FROM grading_scales WHERE grading_system_id = ?");
            $gradesCount->execute([$system['id']]);
            $count = $gradesCount->fetchColumn();
            
            echo "<tr>";
            echo "<td>{$system['id']}</td>";
            echo "<td>{$system['name_ar']}</td>";
            echo "<td>{$system['name_en']}</td>";
            echo "<td style='text-align: center;'>$count</td>";
            echo "<td style='text-align: center;'>";
            echo "<button onclick=\"testSystem('{$system['id']}')\" style='background: #007cba; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;'>اختبار</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 3. Test universities
    echo "<h2>3. 🏛️ الجامعات وأنظمة التقدير</h2>";
    $universities = $pdo->query("SELECT * FROM universities WHERE is_active = 1 ORDER BY name_ar")->fetchAll();
    
    if (empty($universities)) {
        echo "❌ لا توجد جامعات نشطة<br><br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
        echo "<tr style='background: #f0f0f0;'><th>المعرف</th><th>اسم الجامعة</th><th>نظام التقدير</th><th>الإجراءات</th></tr>";
        
        foreach ($universities as $uni) {
            echo "<tr>";
            echo "<td>{$uni['id']}</td>";
            echo "<td>{$uni['name_ar']}</td>";
            echo "<td>{$uni['grading_system']}</td>";
            echo "<td style='text-align: center;'>";
            echo "<button onclick=\"testUniversity('{$uni['id']}')\" style='background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;'>اختبار</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 4. Test API endpoints
    echo "<h2>4. 🔌 اختبار واجهات البرمجة</h2>";
    echo "<div style='margin-bottom: 20px;'>";
    echo "<button onclick=\"testChangeGradingSystem()\" style='background: #6f42c1; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; margin: 5px;'>اختبار تغيير نظام التقدير</button>";
    echo "<button onclick=\"testChangeUniversity()\" style='background: #fd7e14; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; margin: 5px;'>اختبار تغيير الجامعة</button>";
    echo "</div>";
    
    // 5. Results area
    echo "<div id='testResults' style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin-top: 20px;'>";
    echo "<h3>نتائج الاختبار:</h3>";
    echo "<p>اختر أحد الاختبارات أعلاه لرؤية النتائج هنا...</p>";
    echo "</div>";
    
    // 6. Quick links
    echo "<h2>5. 🔗 روابط سريعة</h2>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a>";
    echo "<a href='admin_grading_scales.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة سلالم الدرجات</a>";
    echo "<a href='admin_universities.php' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة الجامعات</a>";
    echo "<a href='smart_fix.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الإصلاح الذكي</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>

<script>
async function testSystem(systemId) {
    updateResults(`جاري اختبار نظام التقدير: ${systemId}...`);
    
    try {
        const response = await fetch('index.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'change_grading_system',
                system: systemId
            })
        });
        
        const result = await response.json();
        
        if (result.success && result.system) {
            let html = `
                <h4>✅ نجح اختبار نظام التقدير: ${systemId}</h4>
                <p><strong>اسم النظام:</strong> ${result.system.name}</p>
                <p><strong>عدد الدرجات:</strong> ${Object.keys(result.system.grades).length}</p>
                <h5>الدرجات المتاحة:</h5>
                <table border="1" style="border-collapse: collapse; width: 100%; margin-top: 10px;">
                    <tr style="background: #f0f0f0;">
                        <th>الدرجة</th>
                        <th>النقاط</th>
                        <th>الوصف</th>
                        <th>النسبة المئوية</th>
                    </tr>
            `;
            
            Object.keys(result.system.grades).forEach(grade => {
                const info = result.system.grades[grade];
                html += `
                    <tr>
                        <td style="text-align: center; font-weight: bold;">${grade}</td>
                        <td style="text-align: center;">${info.points}</td>
                        <td>${info.description}</td>
                        <td style="text-align: center;">
                            ${info.min_percentage !== undefined ? `${info.min_percentage}% - ${info.max_percentage}%` : 'غير محدد'}
                        </td>
                    </tr>
                `;
            });
            
            html += '</table>';
            updateResults(html);
        } else {
            updateResults(`<p style="color: red;">❌ فشل اختبار النظام: ${result.error || 'خطأ غير معروف'}</p>`);
        }
        
    } catch (error) {
        updateResults(`<p style="color: red;">❌ خطأ في الشبكة: ${error.message}</p>`);
    }
}

async function testUniversity(universityId) {
    updateResults(`جاري اختبار الجامعة: ${universityId}...`);
    
    try {
        const response = await fetch('index.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'change_university',
                university: universityId
            })
        });
        
        const result = await response.json();
        
        if (result.success && result.grading_system) {
            let html = `
                <h4>✅ نجح اختبار الجامعة: ${universityId}</h4>
                <p><strong>نظام التقدير المرتبط:</strong> ${result.grading_system.name}</p>
                <p><strong>عدد الدرجات:</strong> ${Object.keys(result.grading_system.grades).length}</p>
                <p><strong>الدرجات:</strong> ${Object.keys(result.grading_system.grades).join(', ')}</p>
            `;
            updateResults(html);
        } else {
            updateResults(`<p style="color: red;">❌ فشل اختبار الجامعة: ${result.error || 'خطأ غير معروف'}</p>`);
        }
        
    } catch (error) {
        updateResults(`<p style="color: red;">❌ خطأ في الشبكة: ${error.message}</p>`);
    }
}

async function testChangeGradingSystem() {
    updateResults('جاري اختبار تغيير نظام التقدير...');
    
    const systems = ['aou', 'standard', 'simple'];
    let results = '<h4>🔄 اختبار تغيير أنظمة التقدير:</h4>';
    
    for (const system of systems) {
        try {
            const response = await fetch('index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'change_grading_system',
                    system: system
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                results += `<p>✅ ${system}: ${result.system.name} (${Object.keys(result.system.grades).length} درجة)</p>`;
            } else {
                results += `<p>❌ ${system}: ${result.error || 'فشل'}</p>`;
            }
            
        } catch (error) {
            results += `<p>❌ ${system}: خطأ في الشبكة</p>`;
        }
    }
    
    updateResults(results);
}

async function testChangeUniversity() {
    updateResults('جاري اختبار تغيير الجامعات...');
    
    const universities = ['aou', 'ksu', 'kau', 'simple_uni'];
    let results = '<h4>🏛️ اختبار تغيير الجامعات:</h4>';
    
    for (const uni of universities) {
        try {
            const response = await fetch('index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'change_university',
                    university: uni
                })
            });
            
            const result = await response.json();
            
            if (result.success && result.grading_system) {
                results += `<p>✅ ${uni}: ${result.grading_system.name}</p>`;
            } else {
                results += `<p>❌ ${uni}: ${result.error || 'فشل'}</p>`;
            }
            
        } catch (error) {
            results += `<p>❌ ${uni}: خطأ في الشبكة</p>`;
        }
    }
    
    updateResults(results);
}

function updateResults(html) {
    document.getElementById('testResults').innerHTML = '<h3>نتائج الاختبار:</h3>' + html;
}
</script>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
}
h1, h2, h3 { 
    color: #333; 
}
table { 
    background: white; 
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
th, td { 
    padding: 10px; 
    text-align: right; 
}
th { 
    background-color: #f0f0f0; 
    font-weight: bold;
}
button:hover { 
    opacity: 0.8; 
    transform: translateY(-1px);
}
a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}
#testResults {
    background: white !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
