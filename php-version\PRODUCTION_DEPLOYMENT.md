# دليل النشر للإنتاج - حاسبة المعدل التراكمي

## 🚀 خطوات النشر

### 1. تنظيف البيانات التجريبية
```bash
# تشغيل سكريبت التنظيف
php cleanup_test_data.php
```

### 2. إعد<PERSON> قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE gpa_calculator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER 'gpa_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON gpa_calculator.* TO 'gpa_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. تحديث إعدادات قاعدة البيانات
```php
// في ملف config.php
$pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'gpa_user', 'secure_password_here');
```

### 4. إعداد صلاحيات الملفات
```bash
# صلاحيات المجلدات
chmod 755 /path/to/gpa-calculator/
chmod 755 /path/to/gpa-calculator/data/
chmod 755 /path/to/gpa-calculator/uploads/

# صلاحيات الملفات
chmod 644 /path/to/gpa-calculator/*.php
chmod 600 /path/to/gpa-calculator/config.php
```

### 5. إعداد HTTPS
```apache
# في ملف .htaccess
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## 🔒 الأمان

### كلمات المرور الافتراضية
- **المدير الرئيسي**: admin / admin123
- **يجب تغييرها فوراً بعد النشر**

### إعدادات الأمان المطلوبة
1. تغيير كلمة مرور المدير
2. تحديث بيانات قاعدة البيانات
3. تفعيل HTTPS
4. إعداد النسخ الاحتياطي
5. تحديث صلاحيات الملفات

## 📁 هيكل الملفات

```
php-version/
├── index.php                    # الصفحة الرئيسية
├── config.php                   # إعدادات قاعدة البيانات
├── functions.php                # الدوال المساعدة
├── ajax_handler.php             # معالج طلبات AJAX
├── simple_admin_login.php       # تسجيل دخول الإدارة
├── admin_dashboard.php          # لوحة الإدارة الرئيسية
├── admin_universities.php       # إدارة الجامعات
├── admin_university_grades.php  # إدارة تقديرات الجامعات
├── admin_students.php           # إدارة الطلاب
├── admin_courses.php            # إدارة المواد
├── admin_users.php              # إدارة المستخدمين
├── admin_notifications.php      # إدارة الإشعارات
├── admin_settings.php           # إعدادات النظام
├── admin_sidebar.php            # الشريط الجانبي للإدارة
├── cleanup_test_data.php        # سكريبت تنظيف البيانات التجريبية
└── data/                        # مجلد البيانات
```

## 🗄️ جداول قاعدة البيانات

### الجداول الأساسية
- `universities` - بيانات الجامعات
- `grading_systems` - أنظمة التقدير
- `grading_scales` - سلالم التقدير
- `university_grades` - تقديرات الجامعات المخصصة
- `students` - بيانات الطلاب
- `courses` - المواد الدراسية
- `admin_users` - مستخدمي الإدارة
- `settings` - إعدادات النظام

## 🔧 الميزات

### للطلاب
- ✅ حساب المعدل الفصلي والتراكمي
- ✅ دعم أنظمة تقدير متعددة
- ✅ دعم جامعات متعددة
- ✅ مشاركة النتائج عبر روابط
- ✅ اقتراحات ذكية لتحسين المعدل
- ✅ تصنيف المعدل التراكمي
- ✅ دعم اللغتين العربية والإنجليزية

### للإدارة
- ✅ إدارة الجامعات وأنظمة التقدير
- ✅ إدارة تقديرات مخصصة لكل جامعة
- ✅ إحصائيات شاملة
- ✅ إدارة بيانات الطلاب
- ✅ نظام إشعارات
- ✅ إعدادات متقدمة

## 🚨 تحذيرات مهمة

### قبل النشر
1. **احذف جميع البيانات التجريبية**
2. **غيّر كلمات المرور الافتراضية**
3. **اختبر جميع الوظائف**
4. **تأكد من إعدادات قاعدة البيانات**
5. **فعّل HTTPS**

### بعد النشر
1. **اختبر تسجيل الدخول**
2. **تأكد من عمل حساب المعدل**
3. **اختبر مشاركة الروابط**
4. **تحقق من الإحصائيات**
5. **اختبر النسخ الاحتياطي**

## 📞 الدعم الفني

### المطور
- **الاسم**: محمد الحراني
- **LinkedIn**: https://www.linkedin.com/in/mohamed-elharany/
- **الرسالة**: صُنع بكل حب من الطلاب للطلاب

### المتطلبات التقنية
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- SSL Certificate (للأمان)

## 🔄 النسخ الاحتياطي

### النسخ الاحتياطي اليومي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u gpa_user -p gpa_calculator > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf files_backup_$(date +%Y%m%d).tar.gz /path/to/gpa-calculator/
```

### استعادة النسخة الاحتياطية
```bash
# استعادة قاعدة البيانات
mysql -u gpa_user -p gpa_calculator < backup_20241201.sql

# استعادة الملفات
tar -xzf files_backup_20241201.tar.gz
```

## ✅ قائمة التحقق النهائية

- [ ] تم تشغيل سكريبت تنظيف البيانات التجريبية
- [ ] تم تحديث بيانات قاعدة البيانات
- [ ] تم تغيير كلمة مرور المدير
- [ ] تم تفعيل HTTPS
- [ ] تم إعداد صلاحيات الملفات
- [ ] تم اختبار جميع الوظائف
- [ ] تم إعداد النسخ الاحتياطي
- [ ] تم اختبار الأداء
- [ ] تم توثيق التغييرات

---

**🎉 النظام جاهز للنشر!**

*تأكد من اتباع جميع الخطوات المذكورة أعلاه لضمان نشر آمن وناجح.*
