# حاسبة المعدل التراكمي الذكية - الجامعة العربية المفتوحة

## نظرة عامة | Overview

تطبيق ويب تفاعلي لحساب المعدل التراكمي (GPA) وفقاً لنظام الجامعة العربية المفتوحة مع ميزات الذكاء الاصطناعي والتحليل الذكي للنتائج، بالإضافة إلى ميزات المشاركة ولوحة الإدارة.

An interactive web application for calculating GPA according to Arab Open University system with AI features, smart analysis, sharing capabilities, and admin dashboard.

## 🏛️ الجامعات المدعومة | Supported Universities

### ✅ متاح حالياً | Currently Available
- **الجامعة العربية المفتوحة (AOU)** - Arab Open University
  - نظام الدرجات الكامل (A, B+, B, C+, C, D, F)
  - التقديرات: ممتاز، جيد جداً، جيد، مقبول، راسب
  - نقاط الدرجة D = 1.5 (وفقاً لنظام AOU)

### 🔄 قريباً | Coming Soon
- الجامعة السعودية الإلكترونية
- جامعة الملك عبدالعزيز
- جامعة الملك سعود
- الجامعة الأمريكية
- المزيد من الجامعات...

## الميزات الرئيسية | Key Features

### 🧮 حساب المعدل التراكمي | GPA Calculation
- حساب المعدل الفصلي والتراكمي
- دعم النظام الأمريكي (4.0)
- التحقق التلقائي من صحة المدخلات
- إدارة المواد الدراسية بسهولة

### 🤖 الذكاء الاصطناعي | AI Features
- مساعد ذكي للدردشة
- اقتراحات ذكية لتحسين المعدل
- تحليل الأداء الأكاديمي
- سيناريوهات "ماذا لو" للتخطيط

### 📊 التصور البياني | Data Visualization
- رسوم بيانية تفاعلية باستخدام Chart.js
- عرض توزيع المعدل التراكمي
- مؤشرات بصرية للأداء

### 🌐 دعم متعدد اللغات | Multi-language Support
- العربية (RTL)
- الإنجليزية (LTR)
- تبديل سهل بين اللغات

### 💾 إدارة البيانات | Data Management
- حفظ البيانات محلياً
- تصدير النتائج إلى PDF
- استيراد البيانات المحفوظة

### 📱 تصميم متجاوب | Responsive Design
- يعمل على جميع الأجهزة
- واجهة مستخدم حديثة
- تأثيرات بصرية جذابة

## التقنيات المستخدمة | Technologies Used

- **HTML5** - هيكل التطبيق
- **CSS3** - التصميم والتنسيق
- **JavaScript (ES6+)** - المنطق والتفاعل
- **Tailwind CSS** - إطار عمل CSS
- **Chart.js** - الرسوم البيانية
- **jsPDF** - تصدير PDF
- **Font Awesome** - الأيقونات

## كيفية الاستخدام | How to Use

### 1. تشغيل التطبيق | Running the Application

```bash
# استخدام PHP
php -S localhost:8000

# أو استخدام Python
python -m http.server 8000

# أو استخدام Node.js
npx http-server
```

### 2. فتح التطبيق | Open the Application
افتح المتصفح وانتقل إلى: `http://localhost:8080`

### 3. إدخال البيانات | Data Entry
1. اختر نوع الحساب (فصلي أو تراكمي)
2. أدخل المعلومات السابقة (للمعدل التراكمي)
3. أضف المواد الدراسية مع الدرجات والساعات
4. اضغط "احسب المعدل"

### 4. عرض النتائج | View Results
- المعدل الفصلي والتراكمي
- التقدير الأكاديمي (ممتاز، جيد جداً، جيد، مقبول، راسب)
- مجموع الساعات
- الاقتراحات الذكية للتحسين

## هيكل المشروع | Project Structure

```
gpa-calculator/
├── index.html          # الصفحة الرئيسية
├── styles.css          # ملف التنسيق المخصص
├── script.js           # منطق التطبيق
└── README.md           # ملف التوثيق
```

## نظام الدرجات - الجامعة العربية المفتوحة | AOU Grading System

### سلم الدرجات | Grade Scale

| الدرجة | المدى | النقاط | الوصف | Grade | Range | Points | Description |
|--------|------|-------|-------|-------|-------|--------|-------------|
| A      | 100-90 | 4.0   | ممتاز | A     | 100-90 | 4.0    | Excellent   |
| B+     | 89-82  | 3.5   | جيد جداً مرتفع | B+    | 89-82  | 3.5    | Very Good High |
| B      | 81-74  | 3.0   | جيد جداً | B     | 81-74  | 3.0    | Very Good   |
| C+     | 73-66  | 2.5   | جيد مرتفع | C+    | 73-66  | 2.5    | Good High   |
| C      | 65-58  | 2.0   | جيد | C     | 65-58  | 2.0    | Good        |
| D      | 57-50  | 1.5   | مقبول | D     | 57-50  | 1.5    | Fair        |
| F      | أقل من 50 | 0.0   | راسب | F     | Below 50 | 0.0    | Fail        |

### تصنيف المعدل التراكمي | GPA Classification

| المعدل | التقدير | GPA Range | Classification |
|-------|---------|-----------|----------------|
| 4.00 - 3.67 | ممتاز | 4.00 - 3.67 | Excellent |
| 3.66 - 3.00 | جيد جداً | 3.66 - 3.00 | Very Good |
| 2.99 - 2.33 | جيد | 2.99 - 2.33 | Good |
| 2.32 - 2.00 | مقبول | 2.32 - 2.00 | Fair |
| أقل من 2.00 | راسب | Below 2.00 | Fail |

## المساعد الذكي | AI Assistant

المساعد الذكي يقدم:
- نصائح لتحسين المعدل
- تحليل الأداء الحالي
- اقتراحات للمواد القادمة
- إجابات على الأسئلة الأكاديمية

## 📁 هيكل المشروع | Project Structure

```
gpa-calculator/
├── index.html          # الصفحة الرئيسية
├── script.js           # منطق التطبيق
└── README.md           # ملف التوثيق
```

### الملفات الأساسية:
- **`index.html`** - واجهة التطبيق مع التصميم المدمج
- **`script.js`** - حساب المعدل والذكاء الاصطناعي
- **`README.md`** - دليل الاستخدام والتوثيق

## التطوير المستقبلي | Future Development

- [ ] دعم أنظمة تقييم أخرى
- [ ] تكامل مع APIs خارجية
- [ ] تحليلات أكثر تفصيلاً
- [ ] وضع الظلام
- [ ] تصدير إلى Excel
- [ ] مشاركة النتائج

## المساهمة | Contributing

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## الدعم | Support

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

---

**تم تطويره بـ ❤️ للمجتمع الأكاديمي**

**Developed with ❤️ for the Academic Community**
