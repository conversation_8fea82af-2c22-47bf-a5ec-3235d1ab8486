// GPA Calculator - Arab Open University System
class GPACalculator {
    constructor() {
        this.currentLanguage = 'ar';
        // AOU (Arab Open University) Grading System
        this.gradePoints = {
            'A': 4.0,    // 100-90
            'B+': 3.5,   // 89-82
            'B': 3.0,    // 81-74
            'C+': 2.5,   // 73-66
            'C': 2.0,    // 65-58
            'D': 1.5,    // 57-50
            'F': 0.0     // Below 50
        };
        
        // University systems - will be expanded
        this.universitySystems = {
            'AOU': {
                name: 'الجامعة العربية المفتوحة',
                nameEn: 'Arab Open University',
                grades: {
                    'A': { points: 4.0, range: '100-90', description: 'ممتاز' },
                    'B+': { points: 3.5, range: '89-82', description: 'جيد جداً مرتفع' },
                    'B': { points: 3.0, range: '81-74', description: 'جيد جداً' },
                    'C+': { points: 2.5, range: '73-66', description: 'جيد مرتفع' },
                    'C': { points: 2.0, range: '65-58', description: 'جيد' },
                    'D': { points: 1.5, range: '57-50', description: 'مقبول' },
                    'F': { points: 0.0, range: 'أقل من 50', description: 'راسب' }
                },
                classifications: {
                    'excellent': { min: 3.67, max: 4.0, name: 'ممتاز', nameEn: 'Excellent' },
                    'veryGood': { min: 3.0, max: 3.66, name: 'جيد جداً', nameEn: 'Very Good' },
                    'good': { min: 2.33, max: 2.99, name: 'جيد', nameEn: 'Good' },
                    'fair': { min: 2.0, max: 2.32, name: 'مقبول', nameEn: 'Fair' },
                    'fail': { min: 0.0, max: 1.99, name: 'راسب', nameEn: 'Fail' }
                }
            }
        };
        
        this.currentUniversity = 'AOU';
        
        this.translations = {
            ar: {
                langText: 'English',
                shareText: 'مشاركة المعدل',
                adminText: 'لوحة الإدارة',
                calculatorTitle: 'حاسبة المعدل التراكمي',
                calcTypeLabel: 'نوع الحساب',
                prevInfoTitle: 'المعلومات السابقة',
                prevGpaLabel: 'المعدل التراكمي السابق',
                prevHoursLabel: 'الساعات المكتسبة السابقة',
                coursesTitle: 'المواد الدراسية',
                addCourseText: 'إضافة مادة',
                calculateText: 'احسب المعدل',
                resultsTitle: 'النتائج',
                semesterLabel: 'المعدل الفصلي',
                cumulativeLabel: 'المعدل التراكمي',
                hoursLabel: 'مجموع الساعات',
                saveText: 'حفظ البيانات',
                loadText: 'تحميل البيانات',
                gradeScaleTitle: 'سلم الدرجات - AOU',
                suggestionsTitle: 'اقتراحات ذكية',
                noSuggestionsText: 'احسب معدلك أولاً للحصول على اقتراحات ذكية',
                assistantTitle: 'المساعد الذكي',
                welcomeMessage: 'مرحباً! أنا مساعدك الذكي لحساب المعدل التراكمي',
                courseName: 'اسم المادة',
                creditHours: 'الساعات المعتمدة',
                grade: 'الدرجة',
                remove: 'حذف',
                selectGrade: 'اختر الدرجة',
                shareModalTitle: 'مشاركة المعدل التراكمي',
                shareModalDesc: 'أنشئ رابط لمشاركة معدلك أو الرجوع إليه لاحقاً',
                nameLabel: 'الاسم الكامل',
                phoneLabel: 'رقم الهاتف',
                createLinkText: 'إنشاء رابط',
                cancelText: 'إلغاء',
                linkGeneratedText: 'تم إنشاء الرابط بنجاح!',
                adminModalTitle: 'لوحة الإدارة',
                statsTabText: 'الإحصائيات',
                studentsTabText: 'بيانات الطلاب',
                loadingText: 'جاري المعالجة...'
            },
            en: {
                langText: 'العربية',
                shareText: 'Share GPA',
                adminText: 'Admin Panel',
                calculatorTitle: 'GPA Calculator',
                calcTypeLabel: 'Calculation Type',
                prevInfoTitle: 'Previous Information',
                prevGpaLabel: 'Previous Cumulative GPA',
                prevHoursLabel: 'Previous Credit Hours',
                coursesTitle: 'Courses',
                addCourseText: 'Add Course',
                calculateText: 'Calculate GPA',
                resultsTitle: 'Results',
                semesterLabel: 'Semester GPA',
                cumulativeLabel: 'Cumulative GPA',
                hoursLabel: 'Total Hours',
                saveText: 'Save Data',
                loadText: 'Load Data',
                gradeScaleTitle: 'Grade Scale - AOU',
                suggestionsTitle: 'Smart Suggestions',
                noSuggestionsText: 'Calculate your GPA first to get smart suggestions',
                assistantTitle: 'AI Assistant',
                welcomeMessage: 'Hello! I am your smart assistant for GPA calculation',
                courseName: 'Course Name',
                creditHours: 'Credit Hours',
                grade: 'Grade',
                remove: 'Remove',
                selectGrade: 'Select Grade',
                shareModalTitle: 'Share GPA',
                shareModalDesc: 'Create a link to share your GPA or return to it later',
                nameLabel: 'Full Name',
                phoneLabel: 'Phone Number',
                createLinkText: 'Create Link',
                cancelText: 'Cancel',
                linkGeneratedText: 'Link created successfully!',
                adminModalTitle: 'Admin Panel',
                statsTabText: 'Statistics',
                studentsTabText: 'Student Data',
                loadingText: 'Processing...'
            }
        };

        // Initialize admin data
        this.adminData = {
            students: JSON.parse(localStorage.getItem('gpaStudents') || '[]'),
            stats: JSON.parse(localStorage.getItem('gpaStats') || '{"totalUsers": 0, "totalCalculations": 0, "avgGPA": 0}')
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.updateLanguage();
        this.addInitialCourse();
        this.loadSavedData();
        this.checkForSharedGPA();
    }
    
    setupEventListeners() {
        // Language toggle
        const languageToggle = document.getElementById('languageToggle');
        if (languageToggle) {
            console.log('Language toggle found, adding listener');
            languageToggle.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Language toggle clicked');
                this.toggleLanguage();
            });
        } else {
            console.error('Language toggle not found');
        }

        // Share button
        const shareBtn = document.getElementById('shareBtn');
        if (shareBtn) {
            console.log('Share button found, adding listener');
            shareBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Share button clicked');
                this.openShareModal();
            });
        } else {
            console.error('Share button not found');
        }

        // Admin button
        const adminBtn = document.getElementById('adminBtn');
        if (adminBtn) {
            console.log('Admin button found, adding listener');
            adminBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Admin button clicked');
                this.openAdminModal();
            });
        } else {
            console.error('Admin button not found');
        }
        
        // Calculation type change
        const calculationType = document.getElementById('calculationType');
        if (calculationType) {
            calculationType.addEventListener('change', (e) => {
                console.log('Calculation type changed:', e.target.value);
                this.togglePreviousGpaSection(e.target.value === 'cumulative');
            });
        }
        
        // Add course button
        const addCourseBtn = document.getElementById('addCourseBtn');
        if (addCourseBtn) {
            console.log('Add course button found, adding listener');
            addCourseBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Add course button clicked');
                this.addCourse();
            });
        } else {
            console.error('Add course button not found');
        }

        // Calculate button
        const calculateBtn = document.getElementById('calculateBtn');
        if (calculateBtn) {
            console.log('Calculate button found, adding listener');
            calculateBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Calculate button clicked');
                this.calculateGPA();
            });
        } else {
            console.error('Calculate button not found');
        }
        
        // Save data button
        const saveDataBtn = document.getElementById('saveDataBtn');
        if (saveDataBtn) {
            saveDataBtn.addEventListener('click', () => {
                console.log('Save data button clicked');
                this.saveData();
            });
        }
        
        // Load data button
        const loadDataBtn = document.getElementById('loadDataBtn');
        if (loadDataBtn) {
            loadDataBtn.addEventListener('click', () => {
                console.log('Load data button clicked');
                this.loadData();
            });
        }
        
        // Chat functionality
        const sendChatBtn = document.getElementById('sendChatBtn');
        const chatInput = document.getElementById('chatInput');
        
        if (sendChatBtn) {
            sendChatBtn.addEventListener('click', () => {
                console.log('Send chat button clicked');
                this.sendChatMessage();
            });
        }
        
        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    console.log('Enter pressed in chat input');
                    this.sendChatMessage();
                }
            });
        }
    }
    
    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        const html = document.documentElement;

        if (this.currentLanguage === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
        } else {
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
        }

        this.updateLanguage();

        const message = this.currentLanguage === 'ar' ? 'تم تغيير اللغة بنجاح' : 'Language changed successfully';
        this.showAlert(message, 'success');
    }
    
    updateLanguage() {
        const t = this.translations[this.currentLanguage];

        // Update navigation
        const langText = document.getElementById('langText');
        const shareText = document.getElementById('shareText');
        const adminText = document.getElementById('adminText');

        if (langText) langText.textContent = t.langText;
        if (shareText) shareText.textContent = t.shareText;
        if (adminText) adminText.textContent = t.adminText;

        // Update main content
        const calculatorTitle = document.getElementById('calculatorTitle');
        const calcTypeLabel = document.getElementById('calcTypeLabel');
        const prevInfoTitle = document.getElementById('prevInfoTitle');
        const prevGpaLabel = document.getElementById('prevGpaLabel');
        const prevHoursLabel = document.getElementById('prevHoursLabel');
        const coursesTitle = document.getElementById('coursesTitle');
        const addCourseText = document.getElementById('addCourseText');
        const calculateText = document.getElementById('calculateText');

        if (calculatorTitle) calculatorTitle.textContent = t.calculatorTitle;
        if (calcTypeLabel) calcTypeLabel.textContent = t.calcTypeLabel;
        if (prevInfoTitle) prevInfoTitle.textContent = t.prevInfoTitle;
        if (prevGpaLabel) prevGpaLabel.textContent = t.prevGpaLabel;
        if (prevHoursLabel) prevHoursLabel.textContent = t.prevHoursLabel;
        if (coursesTitle) coursesTitle.textContent = t.coursesTitle;
        if (addCourseText) addCourseText.textContent = t.addCourseText;
        if (calculateText) calculateText.textContent = t.calculateText;

        // Update results section
        const resultsTitle = document.getElementById('resultsTitle');
        const semesterLabel = document.getElementById('semesterLabel');
        const cumulativeLabel = document.getElementById('cumulativeLabel');
        const hoursLabel = document.getElementById('hoursLabel');
        const saveText = document.getElementById('saveText');
        const loadText = document.getElementById('loadText');

        if (resultsTitle) resultsTitle.textContent = t.resultsTitle;
        if (semesterLabel) semesterLabel.textContent = t.semesterLabel;
        if (cumulativeLabel) cumulativeLabel.textContent = t.cumulativeLabel;
        if (hoursLabel) hoursLabel.textContent = t.hoursLabel;
        if (saveText) saveText.textContent = t.saveText;
        if (loadText) loadText.textContent = t.loadText;

        // Update sidebar
        const gradeScaleTitle = document.getElementById('gradeScaleTitle');
        const suggestionsTitle = document.getElementById('suggestionsTitle');
        const noSuggestionsText = document.getElementById('noSuggestionsText');
        const assistantTitle = document.getElementById('assistantTitle');
        const welcomeMessage = document.getElementById('welcomeMessage');

        if (gradeScaleTitle) gradeScaleTitle.textContent = t.gradeScaleTitle;
        if (suggestionsTitle) suggestionsTitle.textContent = t.suggestionsTitle;
        if (noSuggestionsText) noSuggestionsText.textContent = t.noSuggestionsText;
        if (assistantTitle) assistantTitle.textContent = t.assistantTitle;
        if (welcomeMessage) welcomeMessage.textContent = t.welcomeMessage;

        // Update modals
        const shareModalTitle = document.getElementById('shareModalTitle');
        const shareModalDesc = document.getElementById('shareModalDesc');
        const nameLabel = document.getElementById('nameLabel');
        const phoneLabel = document.getElementById('phoneLabel');
        const createLinkText = document.getElementById('createLinkText');
        const cancelText = document.getElementById('cancelText');
        const linkGeneratedText = document.getElementById('linkGeneratedText');
        const adminModalTitle = document.getElementById('adminModalTitle');
        const statsTabText = document.getElementById('statsTabText');
        const studentsTabText = document.getElementById('studentsTabText');
        const loadingText = document.getElementById('loadingText');

        if (shareModalTitle) shareModalTitle.textContent = t.shareModalTitle;
        if (shareModalDesc) shareModalDesc.textContent = t.shareModalDesc;
        if (nameLabel) nameLabel.textContent = t.nameLabel;
        if (phoneLabel) phoneLabel.textContent = t.phoneLabel;
        if (createLinkText) createLinkText.textContent = t.createLinkText;
        if (cancelText) cancelText.textContent = t.cancelText;
        if (linkGeneratedText) linkGeneratedText.textContent = t.linkGeneratedText;
        if (adminModalTitle) adminModalTitle.textContent = t.adminModalTitle;
        if (statsTabText) statsTabText.textContent = t.statsTabText;
        if (studentsTabText) studentsTabText.textContent = t.studentsTabText;
        if (loadingText) loadingText.textContent = t.loadingText;

        // Update course labels
        this.updateCoursesLanguage();
    }
    
    togglePreviousGpaSection(show) {
        const section = document.getElementById('previousGpaSection');
        if (section) {
            if (show) {
                section.classList.remove('hidden');
                section.classList.add('fade-in');
            } else {
                section.classList.add('hidden');
            }
        }
    }

    addCourse(courseData = null) {
        console.log('Adding course:', courseData);
        const container = document.getElementById('coursesContainer');
        if (!container) {
            console.error('Courses container not found');
            return;
        }

        const courseId = 'course_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const t = this.translations[this.currentLanguage];

        const courseDiv = document.createElement('div');
        courseDiv.className = 'course-item p-4 rounded-lg mb-4 slide-in';
        courseDiv.id = courseId;

        courseDiv.innerHTML = `
            <div class="grid md:grid-cols-4 gap-4 items-end">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        ${t.courseName}
                    </label>
                    <input type="text" class="course-name w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="${t.courseName}" value="${courseData?.name || ''}">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        ${t.creditHours}
                    </label>
                    <input type="number" class="credit-hours w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           min="1" max="6" placeholder="${t.creditHours}" value="${courseData?.hours || ''}">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        ${t.grade}
                    </label>
                    <select class="grade-select w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">${t.selectGrade}</option>
                        <option value="A" ${courseData?.grade === 'A' ? 'selected' : ''}>A (4.0) - ممتاز</option>
                        <option value="B+" ${courseData?.grade === 'B+' ? 'selected' : ''}>B+ (3.5) - جيد جداً مرتفع</option>
                        <option value="B" ${courseData?.grade === 'B' ? 'selected' : ''}>B (3.0) - جيد جداً</option>
                        <option value="C+" ${courseData?.grade === 'C+' ? 'selected' : ''}>C+ (2.5) - جيد مرتفع</option>
                        <option value="C" ${courseData?.grade === 'C' ? 'selected' : ''}>C (2.0) - جيد</option>
                        <option value="D" ${courseData?.grade === 'D' ? 'selected' : ''}>D (1.5) - مقبول</option>
                        <option value="F" ${courseData?.grade === 'F' ? 'selected' : ''}>F (0.0) - راسب</option>
                    </select>
                </div>

                <div>
                    <button class="remove-course w-full bg-red-500 text-white p-3 rounded-lg hover:bg-red-600 transition-colors duration-300 flex items-center justify-center">
                        <i class="fas fa-trash ${this.currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}"></i>
                        ${t.remove}
                    </button>
                </div>
            </div>
        `;

        // Add remove functionality
        const removeBtn = courseDiv.querySelector('.remove-course');
        if (removeBtn) {
            removeBtn.addEventListener('click', () => {
                console.log('Remove course clicked:', courseId);
                this.removeCourse(courseId);
            });
        }

        // Add validation
        this.addCourseValidation(courseDiv);

        container.appendChild(courseDiv);
        console.log('Course added successfully:', courseId);
    }

    addInitialCourse() {
        this.addCourse();
    }

    removeCourse(courseId) {
        const courseElement = document.getElementById(courseId);
        if (courseElement) {
            courseElement.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => {
                courseElement.remove();
                console.log('Course removed:', courseId);
            }, 300);
        }
    }

    addCourseValidation(courseDiv) {
        const inputs = courseDiv.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.validateCourse(courseDiv);
            });
        });
    }

    validateCourse(courseDiv) {
        const name = courseDiv.querySelector('.course-name').value.trim();
        const hours = courseDiv.querySelector('.credit-hours').value;
        const grade = courseDiv.querySelector('.grade-select').value;

        const isValid = name && hours && grade && hours >= 1 && hours <= 6;

        if (isValid) {
            courseDiv.classList.remove('error');
        } else {
            courseDiv.classList.add('error');
        }

        return isValid;
    }

    updateCoursesLanguage() {
        const courses = document.querySelectorAll('.course-item');
        const t = this.translations[this.currentLanguage];

        courses.forEach(course => {
            const labels = course.querySelectorAll('label');
            const button = course.querySelector('.remove-course');

            if (labels.length >= 3) {
                labels[0].textContent = t.courseName;
                labels[1].textContent = t.creditHours;
                labels[2].textContent = t.grade;
            }

            if (button) {
                button.innerHTML = `<i class="fas fa-trash ${this.currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}"></i>${t.remove}`;
            }
        });
    }

    calculateGPA() {
        console.log('Starting GPA calculation...');
        const courses = this.getCourseData();

        if (courses.length === 0) {
            this.showAlert('يرجى إضافة مادة واحدة على الأقل', 'warning');
            return;
        }

        // Validate all courses
        let hasErrors = false;
        const courseElements = document.querySelectorAll('.course-item');

        courses.forEach((course, index) => {
            if (!course.name || !course.hours || !course.grade) {
                hasErrors = true;
                if (courseElements[index]) {
                    courseElements[index].classList.add('error');
                }
            }
        });

        if (hasErrors) {
            this.showAlert('يرجى ملء جميع البيانات المطلوبة', 'error');
            return;
        }

        this.showLoading(true);

        setTimeout(() => {
            try {
                const results = this.performCalculation(courses);
                this.displayResults(results);
                this.generateSmartSuggestions(results);
                this.showLoading(false);
                console.log('GPA calculation completed:', results);
            } catch (error) {
                console.error('Error in calculation:', error);
                this.showAlert('حدث خطأ في الحساب', 'error');
                this.showLoading(false);
            }
        }, 1000);
    }

    getCourseData() {
        const courseElements = document.querySelectorAll('.course-item');
        const courses = [];

        courseElements.forEach(element => {
            const name = element.querySelector('.course-name').value.trim();
            const hours = parseInt(element.querySelector('.credit-hours').value);
            const grade = element.querySelector('.grade-select').value;

            if (name && hours && grade) {
                courses.push({ name, hours, grade });
            }
        });

        console.log('Course data collected:', courses);
        return courses;
    }

    performCalculation(courses) {
        let totalPoints = 0;
        let totalHours = 0;

        courses.forEach(course => {
            const points = this.gradePoints[course.grade] * course.hours;
            totalPoints += points;
            totalHours += course.hours;
        });

        const semesterGPA = totalHours > 0 ? (totalPoints / totalHours) : 0;

        // Calculate cumulative GPA if previous data exists
        const calculationType = document.getElementById('calculationType').value;
        let cumulativeGPA = semesterGPA;
        let totalCumulativeHours = totalHours;

        if (calculationType === 'cumulative') {
            const previousGPA = parseFloat(document.getElementById('previousGpa').value) || 0;
            const previousHours = parseInt(document.getElementById('previousHours').value) || 0;

            if (previousHours > 0) {
                const previousPoints = previousGPA * previousHours;
                const totalCumulativePoints = previousPoints + totalPoints;
                totalCumulativeHours = previousHours + totalHours;
                cumulativeGPA = totalCumulativeHours > 0 ? (totalCumulativePoints / totalCumulativeHours) : 0;
            }
        }

        return {
            semesterGPA: Math.round(semesterGPA * 100) / 100,
            cumulativeGPA: Math.round(cumulativeGPA * 100) / 100,
            totalHours: totalCumulativeHours,
            courses: courses,
            semesterHours: totalHours
        };
    }

    displayResults(results) {
        console.log('Displaying results:', results);

        const semesterGpaValue = document.getElementById('semesterGpaValue');
        const cumulativeGpaValue = document.getElementById('cumulativeGpaValue');
        const totalHoursValue = document.getElementById('totalHoursValue');
        const resultsSection = document.getElementById('resultsSection');

        if (semesterGpaValue) semesterGpaValue.textContent = results.semesterGPA.toFixed(2);
        if (cumulativeGpaValue) cumulativeGpaValue.textContent = results.cumulativeGPA.toFixed(2);
        if (totalHoursValue) totalHoursValue.textContent = results.totalHours;

        // Add classification
        const classification = this.getGPAClassification(results.cumulativeGPA);

        // Update or create classification display
        let classificationElement = document.getElementById('gpaClassification');
        if (!classificationElement) {
            classificationElement = document.createElement('div');
            classificationElement.id = 'gpaClassification';
            classificationElement.className = 'text-center mt-4 p-4 rounded-lg';

            const cumulativeCard = cumulativeGpaValue?.closest('.bg-gradient-to-r');
            if (cumulativeCard) {
                cumulativeCard.appendChild(classificationElement);
            }
        }

        if (classificationElement) {
            classificationElement.innerHTML = `
                <div class="text-sm font-medium opacity-90 mb-1">التقدير</div>
                <div class="text-lg font-bold">${classification.name}</div>
            `;
        }

        if (resultsSection) {
            resultsSection.classList.remove('hidden');
            resultsSection.classList.add('fade-in');

            // Scroll to results
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        this.showAlert(`تم حساب المعدل بنجاح! التقدير: ${classification.name}`, 'success');
    }

    getGPAClassification(gpa) {
        const classifications = this.universitySystems[this.currentUniversity].classifications;

        for (const [key, classification] of Object.entries(classifications)) {
            if (gpa >= classification.min && gpa <= classification.max) {
                return classification;
            }
        }

        return classifications.fail; // Default fallback
    }

    generateSmartSuggestions(results) {
        const suggestionsContainer = document.getElementById('smartSuggestions');
        if (!suggestionsContainer) return;

        suggestionsContainer.innerHTML = '';

        const suggestions = this.getSmartSuggestions(results);

        suggestions.forEach((suggestion, index) => {
            const suggestionDiv = document.createElement('div');
            suggestionDiv.className = `p-4 rounded-lg mb-3 ${suggestion.bgColor}`;
            suggestionDiv.style.animationDelay = `${index * 0.1}s`;

            suggestionDiv.innerHTML = `
                <div class="flex items-start">
                    <i class="fas ${suggestion.icon} text-lg ml-3 mt-1"></i>
                    <div>
                        <h4 class="font-semibold mb-1">${suggestion.title}</h4>
                        <p class="text-sm">${suggestion.description}</p>
                    </div>
                </div>
            `;

            suggestionsContainer.appendChild(suggestionDiv);
        });
    }

    getSmartSuggestions(results) {
        const suggestions = [];
        const classification = this.getGPAClassification(results.cumulativeGPA);

        // GPA level analysis based on AOU system
        if (results.cumulativeGPA >= 3.67) {
            suggestions.push({
                bgColor: 'bg-green-100 text-green-800',
                icon: 'fa-star',
                title: 'ممتاز - أداء استثنائي!',
                description: 'تهانينا! معدلك ممتاز وفقاً لنظام الجامعة العربية المفتوحة. استمر في التفوق!'
            });
        } else if (results.cumulativeGPA >= 3.0) {
            suggestions.push({
                bgColor: 'bg-blue-100 text-blue-800',
                icon: 'fa-thumbs-up',
                title: 'جيد جداً - أداء متميز',
                description: 'معدلك جيد جداً! تحتاج لرفعه قليلاً للوصول لتقدير ممتاز (3.67 فما فوق)'
            });
        } else if (results.cumulativeGPA >= 2.33) {
            suggestions.push({
                bgColor: 'bg-yellow-100 text-yellow-800',
                icon: 'fa-chart-line',
                title: 'جيد - يمكن التحسين',
                description: 'معدلك جيد، ركز على الحصول على درجات أعلى للوصول لتقدير جيد جداً (3.0 فما فوق)'
            });
        } else if (results.cumulativeGPA >= 2.0) {
            suggestions.push({
                bgColor: 'bg-orange-100 text-orange-800',
                icon: 'fa-exclamation-triangle',
                title: 'مقبول - يحتاج تحسين',
                description: 'معدلك مقبول، يجب التركيز أكثر لرفع المعدل إلى تقدير جيد (2.33 فما فوق)'
            });
        } else {
            suggestions.push({
                bgColor: 'bg-red-100 text-red-800',
                icon: 'fa-exclamation-circle',
                title: 'راسب - تحذير أكاديمي',
                description: 'معدلك أقل من 2.0، يجب وضع خطة دراسية عاجلة ومراجعة المرشد الأكاديمي'
            });
        }

        return suggestions;
    }

    sendChatMessage() {
        const chatInput = document.getElementById('chatInput');
        if (!chatInput) return;

        const message = chatInput.value.trim();
        if (!message) return;

        this.addChatMessage(message, 'user');
        chatInput.value = '';

        // Simulate AI response
        setTimeout(() => {
            const response = this.generateAIResponse(message);
            this.addChatMessage(response, 'ai');
        }, 1000);
    }

    addChatMessage(message, sender) {
        const container = document.getElementById('chatContainer');
        if (!container) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `mb-3 ${sender === 'user' ? 'text-right' : 'text-left'}`;

        const bubbleClass = sender === 'user' ?
            'bg-blue-600 text-white' :
            'bg-gray-200 text-gray-800';

        messageDiv.innerHTML = `
            <div class="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${bubbleClass}">
                ${message}
            </div>
        `;

        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
    }

    generateAIResponse(message) {
        const lowerMessage = message.toLowerCase();

        // Simple AI responses based on keywords
        if (lowerMessage.includes('معدل') || lowerMessage.includes('gpa')) {
            return 'لحساب معدلك التراكمي، أدخل درجاتك وساعاتك المعتمدة، وسأقوم بحساب معدلك وتقديم نصائح لتحسينه.';
        }

        if (lowerMessage.includes('تحسين') || lowerMessage.includes('improve')) {
            return 'لتحسين معدلك: 1) ركز على المواد ذات الساعات الأكثر 2) احصل على درجات عالية في المواد الجديدة 3) أعد دراسة المواد التي حصلت فيها على درجات منخفضة';
        }

        if (lowerMessage.includes('نصيحة') || lowerMessage.includes('advice')) {
            return 'نصائح للنجاح الأكاديمي: حضر المحاضرات، نظم وقتك، اطلب المساعدة عند الحاجة، راجع دروسك بانتظام، وحافظ على توازن صحي بين الدراسة والراحة.';
        }

        return 'أنا هنا لمساعدتك في حساب معدلك التراكمي وتقديم النصائح. يمكنك سؤالي عن أي شيء متعلق بالمعدل التراكمي!';
    }

    // Share functionality
    openShareModal() {
        const modal = document.getElementById('shareModal');
        if (modal) {
            modal.classList.remove('hidden');

            // Setup form submission
            const form = document.getElementById('shareForm');
            if (form) {
                form.onsubmit = (e) => {
                    e.preventDefault();
                    this.createShareLink();
                };
            }
        }
    }

    createShareLink() {
        const name = document.getElementById('studentName').value.trim();
        const phone = document.getElementById('studentPhone').value.trim();

        if (!name || !phone) {
            this.showAlert('يرجى ملء جميع البيانات المطلوبة', 'error');
            return;
        }

        // Generate unique ID
        const shareId = this.generateShareId();

        // Get current GPA data
        const gpaData = {
            courses: this.getCourseData(),
            calculationType: document.getElementById('calculationType').value,
            previousGpa: document.getElementById('previousGpa').value,
            previousHours: document.getElementById('previousHours').value,
            results: this.getLastResults()
        };

        // Save student data
        const studentData = {
            id: shareId,
            name: name,
            phone: phone,
            gpaData: gpaData,
            timestamp: new Date().toISOString(),
            lastAccessed: new Date().toISOString()
        };

        // Store in admin data
        this.adminData.students.push(studentData);
        localStorage.setItem('gpaStudents', JSON.stringify(this.adminData.students));

        // Update stats
        this.adminData.stats.totalUsers++;
        localStorage.setItem('gpaStats', JSON.stringify(this.adminData.stats));

        // Generate share URL
        const shareUrl = `${window.location.origin}${window.location.pathname}?share=${shareId}`;

        // Display the link
        document.getElementById('shareUrl').value = shareUrl;
        document.getElementById('generatedLink').classList.remove('hidden');

        this.showAlert('تم إنشاء رابط المشاركة بنجاح!', 'success');
    }

    generateShareId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
    }

    getLastResults() {
        return {
            semesterGPA: document.getElementById('semesterGpaValue')?.textContent || '0.00',
            cumulativeGPA: document.getElementById('cumulativeGpaValue')?.textContent || '0.00',
            totalHours: document.getElementById('totalHoursValue')?.textContent || '0'
        };
    }

    // Admin functionality
    openAdminModal() {
        const modal = document.getElementById('adminModal');
        if (modal) {
            modal.classList.remove('hidden');
            this.loadAdminData();
        }
    }

    loadAdminData() {
        // Load statistics
        this.updateAdminStats();

        // Load students table
        this.updateStudentsTable();
    }

    updateAdminStats() {
        const students = this.adminData.students;
        const totalUsers = students.length;

        // Calculate average GPA
        let totalGPA = 0;
        let validGPAs = 0;

        students.forEach(student => {
            if (student.gpaData && student.gpaData.results) {
                const gpa = parseFloat(student.gpaData.results.cumulativeGPA);
                if (!isNaN(gpa)) {
                    totalGPA += gpa;
                    validGPAs++;
                }
            }
        });

        const avgGPA = validGPAs > 0 ? (totalGPA / validGPAs).toFixed(2) : '0.00';

        // Today's calculations
        const today = new Date().toDateString();
        const todayCalculations = students.filter(student =>
            new Date(student.timestamp).toDateString() === today
        ).length;

        // Update display
        document.getElementById('totalUsers').textContent = totalUsers;
        document.getElementById('avgGPA').textContent = avgGPA;
        document.getElementById('todayCalculations').textContent = todayCalculations;

        // Update grade distribution
        this.updateGradeDistribution();
    }

    updateGradeDistribution() {
        const container = document.getElementById('gradeDistribution');
        if (!container) return;

        const grades = { 'ممتاز': 0, 'جيد جداً': 0, 'جيد': 0, 'مقبول': 0, 'راسب': 0 };

        this.adminData.students.forEach(student => {
            if (student.gpaData && student.gpaData.results) {
                const gpa = parseFloat(student.gpaData.results.cumulativeGPA);
                if (!isNaN(gpa)) {
                    const classification = this.getGPAClassification(gpa);
                    if (grades.hasOwnProperty(classification.name)) {
                        grades[classification.name]++;
                    }
                }
            }
        });

        container.innerHTML = '';
        Object.entries(grades).forEach(([grade, count]) => {
            const div = document.createElement('div');
            div.className = 'text-center p-3 bg-white rounded-lg';
            div.innerHTML = `
                <div class="text-2xl font-bold text-gray-800">${count}</div>
                <div class="text-sm text-gray-600">${grade}</div>
            `;
            container.appendChild(div);
        });
    }

    updateStudentsTable() {
        const tbody = document.getElementById('studentsTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        this.adminData.students.forEach((student, index) => {
            const row = document.createElement('tr');
            const gpa = student.gpaData?.results?.cumulativeGPA || 'N/A';
            const classification = gpa !== 'N/A' ? this.getGPAClassification(parseFloat(gpa)).name : 'N/A';
            const date = new Date(student.timestamp).toLocaleDateString('ar-SA');

            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${student.name}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${student.phone}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${gpa}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${classification}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${date}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="viewStudentData('${student.id}')" class="text-blue-600 hover:text-blue-900 ml-3">عرض</button>
                    <button onclick="deleteStudent(${index})" class="text-red-600 hover:text-red-900">حذف</button>
                </td>
            `;

            tbody.appendChild(row);
        });
    }

    // Check for shared GPA on page load
    checkForSharedGPA() {
        const urlParams = new URLSearchParams(window.location.search);
        const shareId = urlParams.get('share');

        if (shareId) {
            const student = this.adminData.students.find(s => s.id === shareId);
            if (student) {
                // Load the shared GPA data
                this.loadSharedGPA(student);
                this.showAlert(`تم تحميل بيانات ${student.name} بنجاح!`, 'success');
            } else {
                this.showAlert('الرابط غير صحيح أو منتهي الصلاحية', 'error');
            }
        }
    }

    loadSharedGPA(student) {
        // Update last accessed
        student.lastAccessed = new Date().toISOString();
        localStorage.setItem('gpaStudents', JSON.stringify(this.adminData.students));

        // Load the GPA data
        if (student.gpaData) {
            this.loadSavedData(student.gpaData);

            // If results exist, display them
            if (student.gpaData.results) {
                setTimeout(() => {
                    document.getElementById('semesterGpaValue').textContent = student.gpaData.results.semesterGPA;
                    document.getElementById('cumulativeGpaValue').textContent = student.gpaData.results.cumulativeGPA;
                    document.getElementById('totalHoursValue').textContent = student.gpaData.results.totalHours;

                    const resultsSection = document.getElementById('resultsSection');
                    if (resultsSection) {
                        resultsSection.classList.remove('hidden');
                        resultsSection.classList.add('fade-in');
                    }
                }, 1000);
            }
        }
    }

    saveData() {
        try {
            const data = {
                courses: this.getCourseData(),
                calculationType: document.getElementById('calculationType').value,
                previousGpa: document.getElementById('previousGpa').value,
                previousHours: document.getElementById('previousHours').value,
                language: this.currentLanguage,
                timestamp: new Date().toISOString()
            };

            localStorage.setItem('gpaCalculatorData', JSON.stringify(data));
            this.showAlert('تم حفظ البيانات بنجاح', 'success');
            console.log('Data saved:', data);
        } catch (error) {
            console.error('Error saving data:', error);
            this.showAlert('خطأ في حفظ البيانات', 'error');
        }
    }

    loadData() {
        try {
            const savedData = localStorage.getItem('gpaCalculatorData');
            if (savedData) {
                const data = JSON.parse(savedData);
                this.loadSavedData(data);
                this.showAlert('تم تحميل البيانات بنجاح', 'success');
                console.log('Data loaded:', data);
            } else {
                this.showAlert('لا توجد بيانات محفوظة', 'warning');
            }
        } catch (error) {
            console.error('Error loading data:', error);
            this.showAlert('خطأ في تحميل البيانات', 'error');
        }
    }

    loadSavedData(data = null) {
        if (!data) {
            const savedData = localStorage.getItem('gpaCalculatorData');
            if (savedData) {
                data = JSON.parse(savedData);
            } else {
                return;
            }
        }

        if (data.language) {
            this.currentLanguage = data.language;
            this.updateLanguage();
        }

        if (data.calculationType) {
            const calculationType = document.getElementById('calculationType');
            if (calculationType) {
                calculationType.value = data.calculationType;
                this.togglePreviousGpaSection(data.calculationType === 'cumulative');
            }
        }

        if (data.previousGpa) {
            const previousGpa = document.getElementById('previousGpa');
            if (previousGpa) previousGpa.value = data.previousGpa;
        }

        if (data.previousHours) {
            const previousHours = document.getElementById('previousHours');
            if (previousHours) previousHours.value = data.previousHours;
        }

        if (data.courses && data.courses.length > 0) {
            // Clear existing courses
            const container = document.getElementById('coursesContainer');
            if (container) {
                container.innerHTML = '';

                // Add saved courses
                data.courses.forEach(course => {
                    this.addCourse(course);
                });
            }
        }
    }

    showAlert(message, type = 'success') {
        console.log('Showing alert:', message, type);

        try {
            const alertDiv = document.createElement('div');

            let bgColor = 'bg-green-500';
            let icon = 'fa-check-circle';

            if (type === 'error') {
                bgColor = 'bg-red-500';
                icon = 'fa-exclamation-circle';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-500';
                icon = 'fa-exclamation-triangle';
            } else if (type === 'info') {
                bgColor = 'bg-blue-500';
                icon = 'fa-info-circle';
            }

            alertDiv.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center gap-3 fade-in`;
            alertDiv.innerHTML = `
                <i class="fas ${icon}"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.remove()" class="text-white hover:text-gray-200 ml-2">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(alertDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentElement) {
                    alertDiv.remove();
                }
            }, 5000);

        } catch (error) {
            console.error('Error showing alert:', error);
            alert(message);
        }
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            if (show) {
                overlay.style.display = 'flex';
                overlay.classList.remove('hidden');
            } else {
                overlay.style.display = 'none';
                overlay.classList.add('hidden');
            }
        }
    }
}

}

// Global functions for modal management
function closeShareModal() {
    const modal = document.getElementById('shareModal');
    if (modal) {
        modal.classList.add('hidden');

        // Reset form
        document.getElementById('shareForm').reset();
        document.getElementById('generatedLink').classList.add('hidden');
    }
}

function copyLink() {
    const shareUrl = document.getElementById('shareUrl');
    if (shareUrl) {
        shareUrl.select();
        document.execCommand('copy');

        if (window.gpaCalculator) {
            window.gpaCalculator.showAlert('تم نسخ الرابط بنجاح!', 'success');
        }
    }
}

function closeAdminModal() {
    const modal = document.getElementById('adminModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

function showAdminTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.admin-content').forEach(tab => {
        tab.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.admin-tab').forEach(btn => {
        btn.classList.remove('active', 'border-purple-500', 'text-purple-600');
        btn.classList.add('border-transparent', 'text-gray-500');
    });

    // Show selected tab
    const selectedTab = document.getElementById(tabName + 'Tab');
    if (selectedTab) {
        selectedTab.classList.remove('hidden');
    }

    // Activate selected tab button
    const selectedBtn = event.target.closest('.admin-tab');
    if (selectedBtn) {
        selectedBtn.classList.add('active', 'border-purple-500', 'text-purple-600');
        selectedBtn.classList.remove('border-transparent', 'text-gray-500');
    }
}

function viewStudentData(studentId) {
    if (window.gpaCalculator) {
        const student = window.gpaCalculator.adminData.students.find(s => s.id === studentId);
        if (student) {
            // Create a detailed view modal or navigate to the student's GPA
            const shareUrl = `${window.location.origin}${window.location.pathname}?share=${studentId}`;
            window.open(shareUrl, '_blank');
        }
    }
}

function deleteStudent(index) {
    if (confirm('هل أنت متأكد من حذف بيانات هذا الطالب؟')) {
        if (window.gpaCalculator) {
            window.gpaCalculator.adminData.students.splice(index, 1);
            localStorage.setItem('gpaStudents', JSON.stringify(window.gpaCalculator.adminData.students));
            window.gpaCalculator.updateStudentsTable();
            window.gpaCalculator.updateAdminStats();
            window.gpaCalculator.showAlert('تم حذف بيانات الطالب بنجاح', 'success');
        }
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('Initializing GPA Calculator...');
        const calculator = new GPACalculator();
        window.gpaCalculator = calculator;
        console.log('GPA Calculator initialized successfully');
    } catch (error) {
        console.error('Initialization error:', error);
        alert('خطأ في تحميل التطبيق. يرجى إعادة تحميل الصفحة.');
    }
});

// Backup initialization for slower connections
window.addEventListener('load', () => {
    if (!window.gpaCalculator) {
        console.log('Backup initialization...');
        try {
            const calculator = new GPACalculator();
            window.gpaCalculator = calculator;
        } catch (error) {
            console.error('Backup initialization error:', error);
        }
    }
});
