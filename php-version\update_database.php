<?php
/**
 * Database Update Script
 * سكريبت تحديث قاعدة البيانات
 */

session_start();

// Simple authentication
if (!isset($_SESSION['update_auth']) && !isset($_POST['password'])) {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تحديث قاعدة البيانات</title>
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-100">
        <div class="min-h-screen flex items-center justify-center">
            <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
                <h1 class="text-2xl font-bold text-center mb-6">تحديث قاعدة البيانات</h1>
                <form method="POST">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                        <input type="password" name="password" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700">
                        دخول
                    </button>
                </form>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Check password
if (isset($_POST['password'])) {
    if ($_POST['password'] === 'admin123') {
        $_SESSION['update_auth'] = true;
    } else {
        echo "<script>alert('كلمة مرور خاطئة'); window.location.href = 'update_database.php';</script>";
        exit;
    }
}

require_once 'db_config.php';

$updates = [];
$errors = [];

try {
    $db = getDB();

    // Check if students table exists
    $studentsTableExists = fetchOne("SHOW TABLES LIKE 'students'");
    if (!$studentsTableExists) {
        $errors[] = "❌ جدول students غير موجود! يرجى تشغيل database.sql أولاً";
        throw new Exception("جدول students غير موجود");
    }

    // Check current table structure
    $columns = fetchAll("DESCRIBE students");
    $existingColumns = array_column($columns, 'Field');

    $updates[] = "✅ جدول students موجود";
    $updates[] = "الأعمدة الموجودة: " . implode(', ', $existingColumns);

    // Check students count
    $studentsCount = fetchOne("SELECT COUNT(*) as count FROM students")['count'];
    $updates[] = "عدد الطلاب الحالي: $studentsCount";
    
    // Add missing columns
    $columnsToAdd = [
        'university' => "VARCHAR(255) DEFAULT ''",
        'cumulative_gpa' => "DECIMAL(4,2) DEFAULT NULL",
        'semester_gpa' => "DECIMAL(4,2) DEFAULT NULL", 
        'total_hours' => "INT DEFAULT 0",
        'classification' => "VARCHAR(100) DEFAULT NULL",
        'share_link' => "VARCHAR(100) DEFAULT NULL",
        'link_views' => "INT DEFAULT 0",
        'link_expires_at' => "TIMESTAMP NULL",
        'is_verified' => "BOOLEAN DEFAULT FALSE"
    ];
    
    foreach ($columnsToAdd as $column => $definition) {
        if (!in_array($column, $existingColumns)) {
            try {
                executeQuery("ALTER TABLE students ADD COLUMN $column $definition");
                $updates[] = "✅ تم إضافة العمود: $column";
            } catch (Exception $e) {
                $errors[] = "❌ خطأ في إضافة العمود $column: " . $e->getMessage();
            }
        } else {
            $updates[] = "⚠️ العمود $column موجود مسبقاً";
        }
    }
    
    // Add indexes
    $indexes = [
        'idx_students_share_link' => 'CREATE INDEX idx_students_share_link ON students(share_link)',
        'idx_students_university_name' => 'CREATE INDEX idx_students_university_name ON students(university)'
    ];
    
    foreach ($indexes as $indexName => $sql) {
        try {
            executeQuery($sql);
            $updates[] = "✅ تم إنشاء الفهرس: $indexName";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                $updates[] = "⚠️ الفهرس $indexName موجود مسبقاً";
            } else {
                $errors[] = "❌ خطأ في إنشاء الفهرس $indexName: " . $e->getMessage();
            }
        }
    }
    
    // Update existing records
    try {
        // First check if universities table exists and has data
        $universitiesExist = fetchOne("SHOW TABLES LIKE 'universities'");
        if ($universitiesExist) {
            $universityCount = fetchOne("SELECT COUNT(*) as count FROM universities")['count'];
            $updates[] = "جدول الجامعات موجود ويحتوي على $universityCount جامعة";

            if ($universityCount > 0) {
                // Check students with university_id
                $studentsWithUniId = fetchOne("SELECT COUNT(*) as count FROM students WHERE university_id IS NOT NULL AND university_id != ''")['count'];
                $updates[] = "عدد الطلاب الذين لديهم university_id: $studentsWithUniId";

                if ($studentsWithUniId > 0) {
                    // Update university names from university_id
                    $result = executeQuery("
                        UPDATE students s
                        JOIN universities u ON s.university_id = u.id
                        SET s.university = u.name_ar
                        WHERE s.university IS NULL OR s.university = ''
                    ");
                    $updates[] = "✅ تم تحديث أسماء الجامعات من university_id";
                } else {
                    $updates[] = "⚠️ لا توجد طلاب لديهم university_id للتحديث";
                }
            } else {
                $updates[] = "⚠️ جدول الجامعات فارغ";
            }
        } else {
            $updates[] = "⚠️ جدول الجامعات غير موجود";

            // Set default university for existing students
            executeQuery("UPDATE students SET university = 'غير محدد' WHERE university IS NULL OR university = ''");
            $updates[] = "✅ تم تعيين 'غير محدد' كجامعة افتراضية للطلاب";
        }
    } catch (Exception $e) {
        $errors[] = "❌ خطأ في تحديث أسماء الجامعات: " . $e->getMessage();

        // Fallback: set default university
        try {
            executeQuery("UPDATE students SET university = 'غير محدد' WHERE university IS NULL OR university = ''");
            $updates[] = "✅ تم تعيين 'غير محدد' كجامعة افتراضية (fallback)";
        } catch (Exception $e2) {
            $errors[] = "❌ خطأ في تعيين الجامعة الافتراضية: " . $e2->getMessage();
        }
    }
    
    // Update GPA data from calculations
    try {
        // Check if gpa_calculations table exists
        $calculationsExist = fetchOne("SHOW TABLES LIKE 'gpa_calculations'");
        if ($calculationsExist) {
            $calculationsCount = fetchOne("SELECT COUNT(*) as count FROM gpa_calculations")['count'];
            $updates[] = "جدول الحسابات موجود ويحتوي على $calculationsCount حساب";

            if ($calculationsCount > 0) {
                // Use a simpler approach for MySQL compatibility
                $latestCalculations = fetchAll("
                    SELECT
                        student_id,
                        cumulative_gpa,
                        semester_gpa,
                        total_hours,
                        classification_ar,
                        created_at
                    FROM gpa_calculations gc1
                    WHERE created_at = (
                        SELECT MAX(created_at)
                        FROM gpa_calculations gc2
                        WHERE gc2.student_id = gc1.student_id
                    )
                ");

                $updatedCount = 0;
                foreach ($latestCalculations as $calc) {
                    executeQuery("
                        UPDATE students
                        SET
                            cumulative_gpa = ?,
                            semester_gpa = ?,
                            total_hours = ?,
                            classification = ?
                        WHERE id = ?
                    ", [
                        $calc['cumulative_gpa'],
                        $calc['semester_gpa'],
                        $calc['total_hours'],
                        $calc['classification_ar'],
                        $calc['student_id']
                    ]);
                    $updatedCount++;
                }

                $updates[] = "✅ تم تحديث بيانات المعدلات لـ $updatedCount طالب";
            } else {
                $updates[] = "⚠️ لا توجد حسابات معدلات للتحديث";
            }
        } else {
            $updates[] = "⚠️ جدول الحسابات غير موجود";
        }
    } catch (Exception $e) {
        $errors[] = "❌ خطأ في تحديث بيانات المعدلات: " . $e->getMessage();
    }
    
    // Add sample data if no students exist
    $studentsCount = fetchOne("SELECT COUNT(*) as count FROM students")['count'];
    if ($studentsCount == 0) {
        try {
            // Insert sample students
            $sampleStudents = [
                ['أحمد محمد علي', '0501234567', '<EMAIL>', 'الجامعة العربية المفتوحة', 3.75, 3.80, 120, 'جيد جداً'],
                ['فاطمة أحمد سالم', '0507654321', '<EMAIL>', 'جامعة الملك سعود', 3.90, 3.85, 110, 'ممتاز'],
                ['محمد عبدالله حسن', '0509876543', '<EMAIL>', 'جامعة الملك عبدالعزيز', 3.25, 3.30, 95, 'جيد'],
            ];

            foreach ($sampleStudents as $student) {
                executeQuery("
                    INSERT INTO students (name, phone, email, university, cumulative_gpa, semester_gpa, total_hours, classification, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ", $student);
            }

            $updates[] = "✅ تم إضافة " . count($sampleStudents) . " طلاب تجريبيين";
        } catch (Exception $e) {
            $errors[] = "❌ خطأ في إضافة البيانات التجريبية: " . $e->getMessage();
        }
    }

    // Check final structure
    $finalColumns = fetchAll("DESCRIBE students");
    $finalStudentsCount = fetchOne("SELECT COUNT(*) as count FROM students")['count'];
    $updates[] = "✅ تم التحديث بنجاح!";
    $updates[] = "الأعمدة النهائية: " . implode(', ', array_column($finalColumns, 'Field'));
    $updates[] = "العدد النهائي للطلاب: $finalStudentsCount";
    
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث قاعدة البيانات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h1 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-database ml-2"></i>
                    تحديث قاعدة البيانات
                </h1>
                
                <?php if (!empty($updates)): ?>
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-green-600">
                        <i class="fas fa-check-circle ml-2"></i>
                        التحديثات المنجزة
                    </h2>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <?php foreach ($updates as $update): ?>
                            <div class="mb-2 text-green-800"><?php echo htmlspecialchars($update); ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-red-600">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        الأخطاء
                    </h2>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <?php foreach ($errors as $error): ?>
                            <div class="mb-2 text-red-800"><?php echo htmlspecialchars($error); ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="text-center mt-8">
                    <a href="admin_students.php" class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-arrow-left ml-2"></i>
                        الذهاب إلى إدارة الطلاب
                    </a>
                    
                    <a href="admin_dashboard.php" class="inline-block bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors mr-4">
                        <i class="fas fa-home ml-2"></i>
                        لوحة الإدارة
                    </a>
                </div>
                
                <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 class="font-semibold text-blue-800 mb-2">ملاحظات مهمة:</h3>
                    <ul class="text-blue-700 text-sm space-y-1">
                        <li>• تم إضافة الأعمدة المطلوبة لجدول الطلاب</li>
                        <li>• تم تحديث بيانات الطلاب من جدول الحسابات</li>
                        <li>• تم إنشاء فهارس لتحسين الأداء</li>
                        <li>• يمكنك الآن استخدام صفحة إدارة الطلاب بشكل طبيعي</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
