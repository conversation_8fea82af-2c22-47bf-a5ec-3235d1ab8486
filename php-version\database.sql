-- GPA Calculator Database Schema
-- Arab Open University System

CREATE DATABASE IF NOT EXISTS gpa_calculator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE gpa_calculator;

-- Universities table
CREATE TABLE universities (
    id VARCHAR(50) PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL,
    logo VARCHAR(10) DEFAULT '🏛️',
    country_ar VARCHAR(100) NOT NULL,
    country_en VARCHAR(100) NOT NULL,
    established YEAR NOT NULL,
    students VARCHAR(20) DEFAULT '0',
    grading_system VARCHAR(50) NOT NULL,
    website VARCHAR(255),
    description_ar TEXT,
    description_en TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Grading systems table
CREATE TABLE grading_systems (
    id VARCHAR(50) PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Grades table
CREATE TABLE grades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    grading_system_id VARCHAR(50) NOT NULL,
    grade VARCHAR(10) NOT NULL,
    points DECIMAL(3,2) NOT NULL,
    min_percentage INT NOT NULL,
    max_percentage INT NOT NULL,
    description_ar VARCHAR(255),
    description_en VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (grading_system_id) REFERENCES grading_systems(id) ON DELETE CASCADE,
    UNIQUE KEY unique_grade_system (grading_system_id, grade)
);

-- Students table
CREATE TABLE students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    university_id VARCHAR(50) NOT NULL,
    student_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (university_id) REFERENCES universities(id),
    INDEX idx_phone (phone),
    INDEX idx_student_id (student_id)
);

-- GPA calculations table
CREATE TABLE gpa_calculations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    calculation_type ENUM('semester', 'cumulative') DEFAULT 'semester',
    semester_gpa DECIMAL(4,2) NOT NULL,
    cumulative_gpa DECIMAL(4,2),
    total_hours INT NOT NULL,
    total_points DECIMAL(8,2) NOT NULL,
    classification_ar VARCHAR(100),
    classification_en VARCHAR(100),
    previous_gpa DECIMAL(4,2),
    previous_hours INT DEFAULT 0,
    grading_system_id VARCHAR(50) NOT NULL,
    calculation_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (grading_system_id) REFERENCES grading_systems(id),
    INDEX idx_student_date (student_id, calculation_date),
    INDEX idx_calculation_date (calculation_date)
);

-- Courses table
CREATE TABLE courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    calculation_id INT NOT NULL,
    course_name VARCHAR(255) NOT NULL,
    credit_hours INT NOT NULL,
    grade VARCHAR(10) NOT NULL,
    grade_points DECIMAL(3,2) NOT NULL,
    course_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (calculation_id) REFERENCES gpa_calculations(id) ON DELETE CASCADE,
    INDEX idx_calculation (calculation_id)
);

-- Shared links table
CREATE TABLE shared_links (
    id VARCHAR(100) PRIMARY KEY,
    calculation_id INT NOT NULL,
    expires_at TIMESTAMP NULL,
    access_count INT DEFAULT 0,
    max_access INT DEFAULT 100,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (calculation_id) REFERENCES gpa_calculations(id) ON DELETE CASCADE,
    INDEX idx_expires (expires_at),
    INDEX idx_active (is_active)
);

-- Admin users table
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    full_name VARCHAR(255),
    role ENUM('admin', 'moderator') DEFAULT 'admin',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_active (is_active)
);

-- System settings table
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description_ar TEXT,
    description_en TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
);

-- Course catalog table
CREATE TABLE course_catalog (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_code VARCHAR(20) NOT NULL,
    course_title_ar VARCHAR(255) NOT NULL,
    course_title_en VARCHAR(255) NOT NULL,
    prerequisites TEXT,
    credit_hours INT NOT NULL DEFAULT 3,
    course_description_ar TEXT,
    course_description_en TEXT,
    course_objectives_ar TEXT,
    course_objectives_en TEXT,
    course_outcomes_ar TEXT,
    course_outcomes_en TEXT,
    university_id VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (university_id) REFERENCES universities(id) ON DELETE SET NULL,
    UNIQUE KEY unique_course_code (course_code, university_id),
    INDEX idx_course_code (course_code),
    INDEX idx_university (university_id),
    INDEX idx_active (is_active)
);

-- Course catalog table
CREATE TABLE course_catalog (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_code VARCHAR(20) NOT NULL,
    course_title_ar VARCHAR(255) NOT NULL,
    course_title_en VARCHAR(255) NOT NULL,
    prerequisites TEXT,
    credit_hours INT NOT NULL DEFAULT 3,
    course_description_ar TEXT,
    course_description_en TEXT,
    course_objectives_ar TEXT,
    course_objectives_en TEXT,
    course_outcomes_ar TEXT,
    course_outcomes_en TEXT,
    university_id VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (university_id) REFERENCES universities(id) ON DELETE SET NULL,
    UNIQUE KEY unique_course_code (course_code, university_id),
    INDEX idx_course_code (course_code),
    INDEX idx_university (university_id),
    INDEX idx_active (is_active)
);

-- Insert default universities
INSERT INTO universities (id, name_ar, name_en, logo, country_ar, country_en, established, students, grading_system, website, description_ar, description_en) VALUES
('aou', 'الجامعة العربية المفتوحة', 'Arab Open University', '🏛️', 'السعودية', 'Saudi Arabia', 2002, '40,000+', 'aou', 'https://www.aou.edu.sa', 'مؤسسة تعليمية رائدة تأسست عام 2002 وتقدم برامج أكاديمية متميزة', 'Leading educational institution established in 2002 offering distinguished academic programs'),
('ksu', 'جامعة الملك سعود', 'King Saud University', '👑', 'السعودية', 'Saudi Arabia', 1957, '65,000+', 'aou', 'https://www.ksu.edu.sa', 'أول جامعة سعودية تأسست عام 1957 وتعتبر من أعرق الجامعات', 'First Saudi university established in 1957, one of the most prestigious universities'),
('kau', 'جامعة الملك عبدالعزيز', 'King Abdulaziz University', '🕌', 'السعودية', 'Saudi Arabia', 1967, '80,000+', 'aou', 'https://www.kau.edu.sa', 'جامعة حكومية تأسست عام 1967 في جدة', 'Public university established in 1967 in Jeddah'),
('uae_university', 'جامعة الإمارات العربية المتحدة', 'United Arab Emirates University', '🇦🇪', 'الإمارات', 'UAE', 1976, '14,000+', 'standard', 'https://www.uaeu.ac.ae', 'أول جامعة وطنية في دولة الإمارات العربية المتحدة', 'First national university in the United Arab Emirates'),
('auc', 'الجامعة الأمريكية بالقاهرة', 'American University in Cairo', '🏺', 'مصر', 'Egypt', 1919, '6,500+', 'standard', 'https://www.aucegypt.edu', 'جامعة أمريكية خاصة في القاهرة تأسست عام 1919', 'Private American university in Cairo established in 1919'),
('ju', 'الجامعة الأردنية', 'University of Jordan', '🏛️', 'الأردن', 'Jordan', 1962, '47,000+', 'simple', 'https://www.ju.edu.jo', 'أول جامعة أردنية تأسست عام 1962', 'First Jordanian university established in 1962');

-- Insert grading systems
INSERT INTO grading_systems (id, name_ar, name_en, description_ar, description_en) VALUES
('aou', 'الجامعة العربية المفتوحة', 'Arab Open University', 'نظام التقدير المستخدم في الجامعة العربية المفتوحة', 'Grading system used in Arab Open University'),
('standard', 'النظام الأمريكي القياسي', 'Standard American System', 'النظام الأمريكي القياسي للتقدير', 'Standard American grading system'),
('simple', 'النظام المبسط', 'Simple System', 'نظام تقدير مبسط', 'Simplified grading system');

-- Insert grades for AOU system
INSERT INTO grades (grading_system_id, grade, points, min_percentage, max_percentage, description_ar, description_en) VALUES
('aou', 'A', 4.00, 90, 100, 'ممتاز', 'Excellent'),
('aou', 'B+', 3.50, 85, 89, 'جيد جداً مرتفع', 'Very Good High'),
('aou', 'B', 3.00, 80, 84, 'جيد جداً', 'Very Good'),
('aou', 'C+', 2.50, 75, 79, 'جيد مرتفع', 'Good High'),
('aou', 'C', 2.00, 70, 74, 'جيد', 'Good'),
('aou', 'D', 1.50, 60, 69, 'مقبول', 'Pass'),
('aou', 'F', 0.00, 0, 59, 'راسب', 'Fail');

-- Insert grades for Standard system
INSERT INTO grades (grading_system_id, grade, points, min_percentage, max_percentage, description_ar, description_en) VALUES
('standard', 'A', 4.00, 90, 100, 'ممتاز', 'Excellent'),
('standard', 'B', 3.00, 80, 89, 'جيد', 'Good'),
('standard', 'C', 2.00, 70, 79, 'مقبول', 'Average'),
('standard', 'D', 1.00, 60, 69, 'ضعيف', 'Below Average'),
('standard', 'F', 0.00, 0, 59, 'راسب', 'Fail');

-- Insert grades for Simple system
INSERT INTO grades (grading_system_id, grade, points, min_percentage, max_percentage, description_ar, description_en) VALUES
('simple', 'A', 4.00, 90, 100, 'ممتاز', 'Excellent'),
('simple', 'B', 3.00, 80, 89, 'جيد', 'Good'),
('simple', 'C', 2.00, 70, 79, 'مقبول', 'Pass'),
('simple', 'D', 1.00, 60, 69, 'ضعيف', 'Weak'),
('simple', 'F', 0.00, 0, 59, 'راسب', 'Fail');

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, password_hash, email, full_name, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'مدير النظام', 'admin');

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description_ar, description_en, is_public) VALUES
('app_name_ar', 'حاسبة المعدل التراكمي', 'string', 'اسم التطبيق بالعربية', 'Application name in Arabic', TRUE),
('app_name_en', 'GPA Calculator', 'string', 'اسم التطبيق بالإنجليزية', 'Application name in English', TRUE),
('default_language', 'ar', 'string', 'اللغة الافتراضية', 'Default language', TRUE),
('default_university', 'aou', 'string', 'الجامعة الافتراضية', 'Default university', TRUE),
('max_courses_per_calculation', '20', 'number', 'الحد الأقصى للمواد في الحساب الواحد', 'Maximum courses per calculation', TRUE),
('enable_sharing', 'true', 'boolean', 'تفعيل المشاركة', 'Enable sharing', TRUE),
('share_link_expiry_days', '30', 'number', 'مدة انتهاء رابط المشاركة بالأيام', 'Share link expiry in days', FALSE);

-- Insert sample courses for AOU
INSERT INTO course_catalog (course_code, course_title_ar, course_title_en, prerequisites, credit_hours, course_description_ar, course_description_en, course_objectives_ar, course_objectives_en, course_outcomes_ar, course_outcomes_en, university_id) VALUES
('B207-A', 'تشكيل الفرص التجارية', 'Shaping Business Opportunities', 'BUS110', 8, 'B207A هي مادة 8 ساعات معتمدة (30 نقطة)، المستوى 5 UK-OU مقدمة من خلال برنامج إدارة الأعمال في الجامعة العربية المفتوحة كمادة إجبارية لجميع الطلاب المسجلين في جميع المسارات في البرنامج. الدخول في هذه المادة مشروط بإنجاز مادة BUS110 بنجاح. وحدة B207 في هذه الخطة الدراسية الجديدة هي نسخة محدثة من OU لما يعادلها وحدة B203A.', 'B207A is an 8-credit (30 points), Level 5 UK-OU based course offered through the Business Program at the Arab Open University as a compulsory course for all students enrolled in all tracks in the program. Entry into this course is contingent upon the successful completion of BUS110. The B207 module in this new study plan is an OU updated version of its equivalent B203A module.', 'تم تصميم هذه الوحدة لتوفير التعلم المفاهيمي والعملي المتوسط للطلاب في إدارة العمليات والتسويق وإدارة الموارد البشرية. تتكون الوحدة من 16 أسبوع دراسي (بما في ذلك التقييم النهائي).', 'This module is designed to provide intermediate conceptual and practical learning to students in operations management, marketing and human resource management. The module comprises 16 study weeks (including final assessment).', 'أ. المعرفة والفهم\n\nفي نهاية الوحدة، من المتوقع أن يكون المتعلمون قادرين على:\n\n1. تطوير تقدير نقدي للتفاعلات بين وظائف الأعمال المختلفة (إدارة العمليات والتسويق وإدارة الموارد البشرية) والتعقيد التكاملي الذي يشكل الابتكار التجاري.\n2. تطوير فهم نقدي لسبب كون المنتجات والخدمات الجديدة ضرورية لبقاء الأعمال ونموها.', 'A. Knowledge and understanding\n\nAt the end of the module, learners will be expected to:\n\n1. Develop a critical appreciation of the interactions between various business functions (operations management, marketing and human resource management) and the integrative complexity that shapes business innovation.\n2. Develop a critical understanding of why new products and services are imperative to business survival and growth.', 'aou'),
('BUS110', 'مقدمة في إدارة الأعمال', 'Introduction to Business', NULL, 6, 'مقدمة شاملة في مبادئ إدارة الأعمال والمفاهيم الأساسية', 'Comprehensive introduction to business management principles and fundamental concepts', 'تعريف الطلاب بأساسيات إدارة الأعمال', 'Introduce students to business management fundamentals', 'فهم المبادئ الأساسية لإدارة الأعمال', 'Understanding fundamental principles of business management', 'aou'),
('M150', 'الرياضيات التطبيقية', 'Applied Mathematics', NULL, 6, 'مقدمة في الرياضيات التطبيقية للأعمال', 'Introduction to applied mathematics for business', 'تطوير المهارات الرياضية للطلاب', 'Develop mathematical skills for students', 'إتقان المفاهيم الرياضية الأساسية', 'Master basic mathematical concepts', 'aou'),
('T175', 'الشبكات في العمل', 'Networked Living', NULL, 6, 'مقدمة في تكنولوجيا المعلومات والشبكات', 'Introduction to information technology and networks', 'فهم أساسيات تكنولوجيا المعلومات', 'Understanding IT fundamentals', 'تطبيق المعرفة التقنية في بيئة العمل', 'Apply technical knowledge in work environment', 'aou'),
('EL111', 'اللغة الإنجليزية للأغراض الأكاديمية', 'English for Academic Purposes', NULL, 6, 'تطوير مهارات اللغة الإنجليزية الأكاديمية', 'Develop academic English language skills', 'تحسين مهارات القراءة والكتابة', 'Improve reading and writing skills', 'إتقان اللغة الإنجليزية الأكاديمية', 'Master academic English language', 'aou');

-- Create indexes for better performance
CREATE INDEX idx_students_university ON students(university_id);
CREATE INDEX idx_calculations_student ON gpa_calculations(student_id);
CREATE INDEX idx_calculations_date ON gpa_calculations(calculation_date);
CREATE INDEX idx_courses_calculation ON courses(calculation_id);
CREATE INDEX idx_shared_links_active ON shared_links(is_active, expires_at);

-- Create views for easier data access
CREATE VIEW student_statistics AS
SELECT 
    u.name_ar as university_name_ar,
    u.name_en as university_name_en,
    COUNT(DISTINCT s.id) as total_students,
    AVG(gc.semester_gpa) as avg_semester_gpa,
    AVG(gc.cumulative_gpa) as avg_cumulative_gpa,
    COUNT(gc.id) as total_calculations
FROM universities u
LEFT JOIN students s ON u.id = s.university_id
LEFT JOIN gpa_calculations gc ON s.id = gc.student_id
GROUP BY u.id, u.name_ar, u.name_en;

CREATE VIEW recent_calculations AS
SELECT 
    s.name as student_name,
    s.phone,
    u.name_ar as university_name,
    gc.semester_gpa,
    gc.cumulative_gpa,
    gc.total_hours,
    gc.classification_ar,
    gc.calculation_date,
    gc.created_at
FROM gpa_calculations gc
JOIN students s ON gc.student_id = s.id
JOIN universities u ON s.university_id = u.id
ORDER BY gc.created_at DESC
LIMIT 100;
