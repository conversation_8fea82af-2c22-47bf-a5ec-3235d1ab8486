<?php
/**
 * Core functions for GPA Calculator
 */

require_once 'config.php';

class GPACalculator {
    private $courses = [];
    private $gradingSystem;
    
    public function __construct($system = 'aou') {
        $this->gradingSystem = getGradingSystem($system);
    }
    
    /**
     * Add a course to the calculation
     */
    public function addCourse($name, $creditHours, $grade) {
        $gradeInfo = $this->getGradeInfo($grade);
        if ($gradeInfo === null) {
            return false;
        }
        
        $this->courses[] = [
            'name' => $name,
            'credit_hours' => (float)$creditHours,
            'grade' => $grade,
            'points' => $gradeInfo['points'],
            'quality_points' => (float)$creditHours * $gradeInfo['points']
        ];
        
        return true;
    }
    
    /**
     * Remove a course by index
     */
    public function removeCourse($index) {
        if (isset($this->courses[$index])) {
            unset($this->courses[$index]);
            $this->courses = array_values($this->courses); // Re-index array
            return true;
        }
        return false;
    }
    
    /**
     * Calculate GPA
     */
    public function calculateGPA() {
        if (empty($this->courses)) {
            return [
                'gpa' => 0,
                'total_hours' => 0,
                'total_points' => 0,
                'courses_count' => 0
            ];
        }
        
        $totalHours = 0;
        $totalQualityPoints = 0;
        
        foreach ($this->courses as $course) {
            $totalHours += $course['credit_hours'];
            $totalQualityPoints += $course['quality_points'];
        }
        
        $gpa = $totalHours > 0 ? $totalQualityPoints / $totalHours : 0;
        
        return [
            'gpa' => round($gpa, 2),
            'total_hours' => $totalHours,
            'total_points' => round($totalQualityPoints, 2),
            'courses_count' => count($this->courses)
        ];
    }
    
    /**
     * Get grade information
     */
    private function getGradeInfo($grade) {
        return $this->gradingSystem['grades'][$grade] ?? null;
    }
    
    /**
     * Get all available grades
     */
    public function getAvailableGrades() {
        return array_keys($this->gradingSystem['grades']);
    }
    
    /**
     * Get grading scale for display
     */
    public function getGradingScale() {
        return $this->gradingSystem['grades'];
    }
    
    /**
     * Get all courses
     */
    public function getCourses() {
        return $this->courses;
    }
    
    /**
     * Clear all courses
     */
    public function clearCourses() {
        $this->courses = [];
    }
    
    /**
     * Load courses from array
     */
    public function loadCourses($courses) {
        $this->courses = [];
        foreach ($courses as $course) {
            $this->addCourse(
                $course['name'],
                $course['credit_hours'],
                $course['grade']
            );
        }
    }
    
    /**
     * Export courses to JSON
     */
    public function exportToJSON() {
        return json_encode([
            'courses' => $this->courses,
            'calculation' => $this->calculateGPA(),
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * Import courses from JSON
     */
    public function importFromJSON($json) {
        $data = json_decode($json, true);
        if ($data && isset($data['courses'])) {
            $this->loadCourses($data['courses']);
            return true;
        }
        return false;
    }
    
    /**
     * Save to session
     */
    public function saveToSession() {
        $_SESSION['gpa_courses'] = $this->courses;
    }
    
    /**
     * Load from session
     */
    public function loadFromSession() {
        if (isset($_SESSION['gpa_courses'])) {
            $this->courses = $_SESSION['gpa_courses'];
        }
    }
    
    /**
     * Save to file
     */
    public function saveToFile($filename) {
        $filepath = DATA_DIR . $filename . '.json';
        return file_put_contents($filepath, $this->exportToJSON()) !== false;
    }
    
    /**
     * Load from file
     */
    public function loadFromFile($filename) {
        $filepath = DATA_DIR . $filename . '.json';
        if (file_exists($filepath)) {
            $json = file_get_contents($filepath);
            return $this->importFromJSON($json);
        }
        return false;
    }
    
    /**
     * Get grade letter from percentage
     */
    public function getGradeFromPercentage($percentage) {
        foreach ($this->gradingSystem['grades'] as $grade => $info) {
            if ($percentage >= $info['min'] && $percentage <= $info['max']) {
                return $grade;
            }
        }
        return 'F';
    }
    
    /**
     * Validate course data
     */
    public function validateCourse($name, $creditHours, $grade) {
        $errors = [];
        
        if (empty(trim($name))) {
            $errors[] = 'Course name is required';
        }
        
        if (!is_numeric($creditHours) || $creditHours <= 0) {
            $errors[] = 'Credit hours must be a positive number';
        }
        
        if (!isset($this->gradingSystem['grades'][$grade])) {
            $errors[] = 'Invalid grade';
        }
        
        return $errors;
    }
}

/**
 * Handle AJAX requests
 */
function handleAjaxRequest() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        return;
    }
    
    $action = $_POST['action'] ?? '';
    $calculator = new GPACalculator();
    
    // Load existing courses from session
    $calculator->loadFromSession();
    
    switch ($action) {
        case 'add_course':
            $name = $_POST['name'] ?? '';
            $creditHours = $_POST['credit_hours'] ?? 0;
            $grade = $_POST['grade'] ?? '';
            
            $errors = $calculator->validateCourse($name, $creditHours, $grade);
            if (empty($errors)) {
                $calculator->addCourse($name, $creditHours, $grade);
                $calculator->saveToSession();
                echo json_encode(['success' => true, 'calculation' => $calculator->calculateGPA()]);
            } else {
                echo json_encode(['success' => false, 'errors' => $errors]);
            }
            break;
            
        case 'remove_course':
            $index = (int)($_POST['index'] ?? -1);
            if ($calculator->removeCourse($index)) {
                $calculator->saveToSession();
                echo json_encode(['success' => true, 'calculation' => $calculator->calculateGPA()]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Course not found']);
            }
            break;
            
        case 'calculate':
            echo json_encode(['success' => true, 'calculation' => $calculator->calculateGPA()]);
            break;
            
        case 'clear':
            $calculator->clearCourses();
            $calculator->saveToSession();
            echo json_encode(['success' => true]);
            break;
            
        case 'export':
            echo json_encode(['success' => true, 'data' => $calculator->exportToJSON()]);
            break;
            
        case 'import':
            $data = $_POST['data'] ?? '';
            if ($calculator->importFromJSON($data)) {
                $calculator->saveToSession();
                echo json_encode(['success' => true, 'calculation' => $calculator->calculateGPA()]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Invalid data format']);
            }
            break;
            
        case 'change_language':
            $lang = $_POST['language'] ?? 'ar';
            $_SESSION['language'] = in_array($lang, ['ar', 'en']) ? $lang : 'ar';
            echo json_encode(['success' => true]);
            break;
    }
    exit;
}

// Handle AJAX requests if this is an AJAX call
if (isset($_POST['action'])) {
    handleAjaxRequest();
}
?>
