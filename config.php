<?php
/**
 * Configuration file for GPA Calculator
 * Arab Open University System
 */

// Application settings
define('APP_NAME', 'حاسبة المعدل التراكمي');
define('APP_VERSION', '2.0');
define('DEFAULT_LANGUAGE', 'ar');

// University grading systems
$grading_systems = [
    'aou' => [
        'name' => [
            'ar' => 'الجامعة العربية المفتوحة',
            'en' => 'Arab Open University'
        ],
        'grades' => [
            'A' => ['points' => 4.0, 'min' => 90, 'max' => 100],
            'B+' => ['points' => 3.5, 'min' => 85, 'max' => 89],
            'B' => ['points' => 3.0, 'min' => 80, 'max' => 84],
            'C+' => ['points' => 2.5, 'min' => 75, 'max' => 79],
            'C' => ['points' => 2.0, 'min' => 70, 'max' => 74],
            'D' => ['points' => 1.5, 'min' => 60, 'max' => 69],
            'F' => ['points' => 0.0, 'min' => 0, 'max' => 59]
        ]
    ]
];

// Language translations
$translations = [
    'ar' => [
        'app_title' => 'حاسبة المعدل التراكمي',
        'university_name' => 'الجامعة العربية المفتوحة',
        'course_name' => 'اسم المادة',
        'credit_hours' => 'الساعات المعتمدة',
        'grade' => 'الدرجة',
        'add_course' => 'إضافة مادة',
        'calculate_gpa' => 'احسب المعدل',
        'gpa_result' => 'المعدل التراكمي',
        'total_hours' => 'إجمالي الساعات',
        'total_points' => 'إجمالي النقاط',
        'share' => 'مشاركة',
        'admin' => 'الإدارة',
        'language' => 'English',
        'about_university' => 'نبذة عن الجامعة العربية المفتوحة',
        'university_description' => 'الجامعة العربية المفتوحة هي مؤسسة تعليمية رائدة تأسست عام 2002 وتقدم برامج أكاديمية متميزة في مختلف التخصصات. تتميز الجامعة بنظام التعليم المدمج الذي يجمع بين التعليم الإلكتروني والتعليم التقليدي.',
        'students_count' => 'الطلاب',
        'established' => 'تأسست',
        'countries' => 'الفروع',
        'programs' => 'البرامج',
        'grading_scale' => 'سلم الدرجات',
        'cumulative_gpa' => 'حاسبة المعدل التراكمي',
        'course_type' => 'نوع المساق',
        'semester' => 'المعدل الفصلي',
        'subjects' => 'المواد الدراسية',
        'remove' => 'حذف',
        'save' => 'حفظ',
        'load' => 'تحميل',
        'export' => 'تصدير',
        'import' => 'استيراد'
    ],
    'en' => [
        'app_title' => 'GPA Calculator',
        'university_name' => 'Arab Open University',
        'course_name' => 'Course Name',
        'credit_hours' => 'Credit Hours',
        'grade' => 'Grade',
        'add_course' => 'Add Course',
        'calculate_gpa' => 'Calculate GPA',
        'gpa_result' => 'GPA Result',
        'total_hours' => 'Total Hours',
        'total_points' => 'Total Points',
        'share' => 'Share',
        'admin' => 'Admin',
        'language' => 'العربية',
        'about_university' => 'About Arab Open University',
        'university_description' => 'Arab Open University is a leading educational institution established in 2002, offering distinguished academic programs in various specializations. The university is characterized by a blended learning system that combines e-learning and traditional education.',
        'students_count' => 'Students',
        'established' => 'Established',
        'countries' => 'Branches',
        'programs' => 'Programs',
        'grading_scale' => 'Grading Scale',
        'cumulative_gpa' => 'Cumulative GPA Calculator',
        'course_type' => 'Course Type',
        'semester' => 'Semester GPA',
        'subjects' => 'Subjects',
        'remove' => 'Remove',
        'save' => 'Save',
        'load' => 'Load',
        'export' => 'Export',
        'import' => 'Import'
    ]
];

// Database settings (if using database)
define('DB_HOST', 'localhost');
define('DB_NAME', 'gpa_calculator');
define('DB_USER', 'root');
define('DB_PASS', '');

// File paths
define('DATA_DIR', __DIR__ . '/data/');
define('ASSETS_DIR', __DIR__ . '/assets/');

// Create data directory if it doesn't exist
if (!file_exists(DATA_DIR)) {
    mkdir(DATA_DIR, 0755, true);
}

// Session settings
session_start();

// Set default language if not set
if (!isset($_SESSION['language'])) {
    $_SESSION['language'] = DEFAULT_LANGUAGE;
}

// Helper function to get translation
function t($key, $lang = null) {
    global $translations;
    if ($lang === null) {
        $lang = $_SESSION['language'] ?? DEFAULT_LANGUAGE;
    }
    return $translations[$lang][$key] ?? $key;
}

// Helper function to get grading system
function getGradingSystem($system = 'aou') {
    global $grading_systems;
    return $grading_systems[$system] ?? $grading_systems['aou'];
}
?>
