<?php
/**
 * Simple Database Fix
 * إصلاح مبسط لقاعدة البيانات
 */

session_start();

// Simple authentication
if (!isset($_SESSION['fix_auth']) && !isset($_POST['password'])) {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إصلاح قاعدة البيانات</title>
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-100">
        <div class="min-h-screen flex items-center justify-center">
            <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
                <h1 class="text-2xl font-bold text-center mb-6">إصلاح قاعدة البيانات</h1>
                <form method="POST">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                        <input type="password" name="password" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700">
                        بدء الإصلاح
                    </button>
                </form>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Check password
if (isset($_POST['password'])) {
    if ($_POST['password'] === 'admin123') {
        $_SESSION['fix_auth'] = true;
    } else {
        echo "<script>alert('كلمة مرور خاطئة'); window.location.href = 'simple_fix.php';</script>";
        exit;
    }
}

$updates = [];
$errors = [];

try {
    // Direct database connection
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $updates[] = "✅ تم الاتصال بقاعدة البيانات";

    // 1. Drop existing tables if they exist (clean start)
    $updates[] = "<h3>1. تنظيف الجداول الموجودة</h3>";
    
    try {
        $pdo->exec("DROP TABLE IF EXISTS grading_scales");
        $updates[] = "✅ تم حذف جدول grading_scales القديم";
    } catch (Exception $e) {
        $updates[] = "⚠️ لم يكن هناك جدول grading_scales";
    }
    
    try {
        $pdo->exec("DROP TABLE IF EXISTS grading_systems");
        $updates[] = "✅ تم حذف جدول grading_systems القديم";
    } catch (Exception $e) {
        $updates[] = "⚠️ لم يكن هناك جدول grading_systems";
    }

    // 2. Create grading_systems table
    $updates[] = "<h3>2. إنشاء جدول أنظمة التقدير</h3>";
    
    $sql = "CREATE TABLE grading_systems (
        id VARCHAR(50) PRIMARY KEY,
        name_ar VARCHAR(255) NOT NULL,
        name_en VARCHAR(255) NOT NULL,
        description_ar TEXT,
        description_en TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    $updates[] = "✅ تم إنشاء جدول grading_systems";

    // 3. Create grading_scales table
    $sql = "CREATE TABLE grading_scales (
        id INT AUTO_INCREMENT PRIMARY KEY,
        grading_system_id VARCHAR(50) NOT NULL,
        grade VARCHAR(10) NOT NULL,
        points DECIMAL(3,2) NOT NULL,
        description_ar VARCHAR(255) NOT NULL,
        description_en VARCHAR(255) NOT NULL,
        min_percentage DECIMAL(5,2) NOT NULL,
        max_percentage DECIMAL(5,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_grade_system (grading_system_id, grade)
    )";
    
    $pdo->exec($sql);
    $updates[] = "✅ تم إنشاء جدول grading_scales";

    // 4. Insert grading systems
    $updates[] = "<h3>3. إضافة أنظمة التقدير</h3>";
    
    $gradingSystems = [
        ['aou', 'نظام الجامعة العربية المفتوحة', 'Arab Open University System', 'نظام التقدير المعتمد في الجامعة العربية المفتوحة', 'Grading system used by Arab Open University'],
        ['standard', 'النظام الأكاديمي المعياري', 'Standard Academic System', 'النظام الأكاديمي المعياري المستخدم في معظم الجامعات السعودية', 'Standard academic system used in most Saudi universities'],
        ['simple', 'النظام المبسط', 'Simple System', 'نظام تقدير مبسط (A=4, B=3, C=2, D=1, F=0)', 'Simple grading system (A=4, B=3, C=2, D=1, F=0)']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO grading_systems (id, name_ar, name_en, description_ar, description_en) VALUES (?, ?, ?, ?, ?)");
    
    foreach ($gradingSystems as $system) {
        $stmt->execute($system);
        $updates[] = "✅ تم إضافة نظام: {$system[1]}";
    }

    // 5. Insert grading scales
    $updates[] = "<h3>4. إضافة سلالم الدرجات</h3>";
    
    // AOU System grades
    $aouGrades = [
        ['A', 4.00, 'ممتاز', 'Excellent', 90, 100],
        ['B+', 3.50, 'جيد جداً مرتفع', 'Very Good High', 85, 89],
        ['B', 3.00, 'جيد جداً', 'Very Good', 80, 84],
        ['C+', 2.50, 'جيد مرتفع', 'Good High', 75, 79],
        ['C', 2.00, 'جيد', 'Good', 70, 74],
        ['D+', 1.50, 'مقبول مرتفع', 'Pass High', 65, 69],
        ['D', 1.00, 'مقبول', 'Pass', 60, 64],
        ['F', 0.00, 'راسب', 'Fail', 0, 59]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO grading_scales (grading_system_id, grade, points, description_ar, description_en, min_percentage, max_percentage) VALUES ('aou', ?, ?, ?, ?, ?, ?)");
    
    foreach ($aouGrades as $grade) {
        $stmt->execute($grade);
    }
    $updates[] = "✅ تم إضافة درجات نظام الجامعة العربية المفتوحة (8 درجات)";

    // Standard System grades
    $standardGrades = [
        ['A+', 4.00, 'ممتاز مرتفع', 'Excellent Plus', 95, 100],
        ['A', 4.00, 'ممتاز', 'Excellent', 90, 94],
        ['B+', 3.50, 'جيد جداً مرتفع', 'Very Good Plus', 85, 89],
        ['B', 3.00, 'جيد جداً', 'Very Good', 80, 84],
        ['C+', 2.50, 'جيد مرتفع', 'Good Plus', 75, 79],
        ['C', 2.00, 'جيد', 'Good', 70, 74],
        ['D+', 1.50, 'مقبول مرتفع', 'Pass Plus', 65, 69],
        ['D', 1.00, 'مقبول', 'Pass', 60, 64],
        ['F', 0.00, 'راسب', 'Fail', 0, 59]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO grading_scales (grading_system_id, grade, points, description_ar, description_en, min_percentage, max_percentage) VALUES ('standard', ?, ?, ?, ?, ?, ?)");
    
    foreach ($standardGrades as $grade) {
        $stmt->execute($grade);
    }
    $updates[] = "✅ تم إضافة درجات النظام المعياري (9 درجات)";

    // Simple System grades
    $simpleGrades = [
        ['A', 4.00, 'ممتاز', 'Excellent', 90, 100],
        ['B', 3.00, 'جيد جداً', 'Very Good', 80, 89],
        ['C', 2.00, 'جيد', 'Good', 70, 79],
        ['D', 1.00, 'مقبول', 'Pass', 60, 69],
        ['F', 0.00, 'راسب', 'Fail', 0, 59]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO grading_scales (grading_system_id, grade, points, description_ar, description_en, min_percentage, max_percentage) VALUES ('simple', ?, ?, ?, ?, ?, ?)");
    
    foreach ($simpleGrades as $grade) {
        $stmt->execute($grade);
    }
    $updates[] = "✅ تم إضافة درجات النظام المبسط (5 درجات)";

    // 6. Update universities table
    $updates[] = "<h3>5. تحديث جدول الجامعات</h3>";
    
    // Check if grading_system column exists
    $result = $pdo->query("SHOW COLUMNS FROM universities LIKE 'grading_system'");
    if ($result->rowCount() == 0) {
        $pdo->exec("ALTER TABLE universities ADD COLUMN grading_system VARCHAR(50) DEFAULT 'aou'");
        $updates[] = "✅ تم إضافة عمود grading_system";
    } else {
        $updates[] = "⚠️ عمود grading_system موجود مسبقاً";
    }
    
    // Update universities with proper grading system IDs
    $universityUpdates = [
        ['aou', 'الجامعة العربية المفتوحة', 'aou'],
        ['ksu', 'جامعة الملك سعود', 'standard'],
        ['kau', 'جامعة الملك عبدالعزيز', 'standard'],
        ['simple_uni', 'جامعة بنظام مبسط', 'simple']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO universities (id, name_ar, name_en, grading_system, is_active) VALUES (?, ?, ?, ?, 1) ON DUPLICATE KEY UPDATE grading_system = VALUES(grading_system), name_ar = VALUES(name_ar)");
    
    foreach ($universityUpdates as $uni) {
        $stmt->execute([$uni[0], $uni[1], $uni[1], $uni[2]]);
        $updates[] = "✅ تم تحديث جامعة: {$uni[1]} - نظام التقدير: {$uni[2]}";
    }

    // 7. Final verification
    $updates[] = "<h3>6. التحقق النهائي</h3>";
    
    $systemsCount = $pdo->query("SELECT COUNT(*) FROM grading_systems")->fetchColumn();
    $scalesCount = $pdo->query("SELECT COUNT(*) FROM grading_scales")->fetchColumn();
    $universitiesCount = $pdo->query("SELECT COUNT(*) FROM universities WHERE is_active = 1")->fetchColumn();
    
    $updates[] = "✅ عدد أنظمة التقدير: $systemsCount";
    $updates[] = "✅ عدد الدرجات: $scalesCount";
    $updates[] = "✅ عدد الجامعات النشطة: $universitiesCount";
    
    // Test one grading system
    $testSystem = $pdo->query("SELECT gs.name_ar, COUNT(gsc.id) as grades_count FROM grading_systems gs LEFT JOIN grading_scales gsc ON gs.id = gsc.grading_system_id WHERE gs.id = 'aou' GROUP BY gs.id")->fetch();
    
    if ($testSystem) {
        $updates[] = "✅ اختبار نظام AOU: {$testSystem['name_ar']} - {$testSystem['grades_count']} درجة";
    }
    
    $updates[] = "🎉 تم إصلاح نظام التقدير بنجاح!";

} catch (Exception $e) {
    $errors[] = "❌ خطأ: " . $e->getMessage();
    $errors[] = "تفاصيل: " . $e->getFile() . " في السطر " . $e->getLine();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح قاعدة البيانات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h1 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-database ml-2"></i>
                    إصلاح قاعدة البيانات
                </h1>
                
                <?php if (!empty($updates)): ?>
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-green-600">
                        <i class="fas fa-check-circle ml-2"></i>
                        نتائج الإصلاح
                    </h2>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 max-h-96 overflow-y-auto">
                        <?php foreach ($updates as $update): ?>
                            <div class="mb-2 text-green-800"><?php echo $update; ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-red-600">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        الأخطاء
                    </h2>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <?php foreach ($errors as $error): ?>
                            <div class="mb-2 text-red-800"><?php echo htmlspecialchars($error); ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="text-center mt-8">
                    <a href="index.php" class="inline-block bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors mr-4">
                        <i class="fas fa-home ml-2"></i>
                        الصفحة الرئيسية
                    </a>
                    
                    <a href="admin_universities.php" class="inline-block bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors mr-4">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة الجامعات
                    </a>
                    
                    <a href="admin_students.php" class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-users ml-2"></i>
                        إدارة الطلاب
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
