<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['admin_id']) || $_SESSION['admin_role'] !== 'admin') {
    header('Location: simple_admin_login.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

$message = '';
$error = '';

// Create table if not exists
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS university_grades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            university_id VARCHAR(50) NOT NULL,
            grade VARCHAR(10) NOT NULL,
            points DECIMAL(3,2) NOT NULL,
            description_ar VARCHAR(100) NOT NULL,
            description_en VARCHAR(100) NOT NULL,
            min_percentage DECIMAL(5,2) DEFAULT 0,
            max_percentage DECIMAL(5,2) DEFAULT 100,
            color_code VARCHAR(7) DEFAULT '#3B82F6',
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_university_grade (university_id, grade),
            INDEX idx_university_id (university_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
} catch (PDOException $e) {
    $error = 'خطأ في إنشاء الجدول: ' . $e->getMessage();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_grade':
                    $universityId = $_POST['university_id'] ?? '';
                    $grade = trim($_POST['grade'] ?? '');
                    $points = (float)($_POST['points'] ?? 0);
                    $descriptionAr = trim($_POST['description_ar'] ?? '');
                    $descriptionEn = trim($_POST['description_en'] ?? '');
                    $minPercentage = (float)($_POST['min_percentage'] ?? 0);
                    $maxPercentage = (float)($_POST['max_percentage'] ?? 100);
                    $colorCode = $_POST['color_code'] ?? '#3B82F6';

                    if (empty($universityId) || empty($grade) || empty($descriptionAr) || empty($descriptionEn)) {
                        $error = 'يرجى ملء جميع الحقول المطلوبة';
                    } elseif ($points < 0 || $points > 4) {
                        $error = 'النقاط يجب أن تكون بين 0 و 4';
                    } elseif ($minPercentage < 0 || $maxPercentage > 100 || $minPercentage >= $maxPercentage) {
                        $error = 'النسب المئوية غير صحيحة';
                    } else {
                        $stmt = $pdo->prepare("
                            INSERT INTO university_grades (university_id, grade, points, description_ar, description_en, min_percentage, max_percentage, color_code)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([$universityId, $grade, $points, $descriptionAr, $descriptionEn, $minPercentage, $maxPercentage, $colorCode]);
                        $message = 'تم إضافة التقدير بنجاح';
                    }
                    break;

                case 'edit_grade':
                    $id = (int)($_POST['grade_id'] ?? 0);
                    $universityId = $_POST['university_id'] ?? '';
                    $grade = trim($_POST['grade'] ?? '');
                    $points = (float)($_POST['points'] ?? 0);
                    $descriptionAr = trim($_POST['description_ar'] ?? '');
                    $descriptionEn = trim($_POST['description_en'] ?? '');
                    $minPercentage = (float)($_POST['min_percentage'] ?? 0);
                    $maxPercentage = (float)($_POST['max_percentage'] ?? 100);
                    $colorCode = $_POST['color_code'] ?? '#3B82F6';

                    if ($id <= 0 || empty($universityId) || empty($grade) || empty($descriptionAr) || empty($descriptionEn)) {
                        $error = 'يرجى ملء جميع الحقول المطلوبة';
                    } elseif ($points < 0 || $points > 4) {
                        $error = 'النقاط يجب أن تكون بين 0 و 4';
                    } elseif ($minPercentage < 0 || $maxPercentage > 100 || $minPercentage >= $maxPercentage) {
                        $error = 'النسب المئوية غير صحيحة';
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE university_grades 
                            SET university_id = ?, grade = ?, points = ?, description_ar = ?, description_en = ?, 
                                min_percentage = ?, max_percentage = ?, color_code = ?
                            WHERE id = ?
                        ");
                        $stmt->execute([$universityId, $grade, $points, $descriptionAr, $descriptionEn, $minPercentage, $maxPercentage, $colorCode, $id]);
                        $message = 'تم تحديث التقدير بنجاح';
                    }
                    break;

                case 'delete_grade':
                    $id = (int)($_POST['grade_id'] ?? 0);
                    if ($id <= 0) {
                        $error = 'معرف التقدير غير صحيح';
                    } else {
                        $stmt = $pdo->prepare("DELETE FROM university_grades WHERE id = ?");
                        $stmt->execute([$id]);
                        $message = 'تم حذف التقدير بنجاح';
                    }
                    break;

                case 'add_default_grades':
                    $universityId = $_POST['university_id'] ?? '';
                    
                    if (empty($universityId)) {
                        $error = 'يرجى اختيار الجامعة';
                        break;
                    }

                    // Default grades for Saudi universities
                    $defaultGrades = [
                        ['A+', 4.00, 'ممتاز مرتفع', 'Excellent Plus', 95, 100, '#10B981'],
                        ['A', 4.00, 'ممتاز', 'Excellent', 90, 94, '#059669'],
                        ['B+', 3.50, 'جيد جداً مرتفع', 'Very Good Plus', 85, 89, '#3B82F6'],
                        ['B', 3.00, 'جيد جداً', 'Very Good', 80, 84, '#1D4ED8'],
                        ['C+', 2.50, 'جيد مرتفع', 'Good Plus', 75, 79, '#F59E0B'],
                        ['C', 2.00, 'جيد', 'Good', 70, 74, '#D97706'],
                        ['D+', 1.50, 'مقبول مرتفع', 'Pass Plus', 65, 69, '#F97316'],
                        ['D', 1.00, 'مقبول', 'Pass', 60, 64, '#EA580C'],
                        ['F', 0.00, 'راسب', 'Fail', 0, 59, '#EF4444']
                    ];

                    $stmt = $pdo->prepare("
                        INSERT INTO university_grades (university_id, grade, points, description_ar, description_en, min_percentage, max_percentage, color_code)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE 
                        points = VALUES(points),
                        description_ar = VALUES(description_ar),
                        description_en = VALUES(description_en),
                        min_percentage = VALUES(min_percentage),
                        max_percentage = VALUES(max_percentage),
                        color_code = VALUES(color_code)
                    ");

                    foreach ($defaultGrades as $gradeData) {
                        $stmt->execute([
                            $universityId,
                            $gradeData[0], // grade
                            $gradeData[1], // points
                            $gradeData[2], // description_ar
                            $gradeData[3], // description_en
                            $gradeData[4], // min_percentage
                            $gradeData[5], // max_percentage
                            $gradeData[6]  // color_code
                        ]);
                    }

                    $message = 'تم إضافة التقديرات الافتراضية بنجاح';
                    break;

                case 'copy_grades':
                    $sourceUniversityId = $_POST['source_university_id'] ?? '';
                    $targetUniversityId = $_POST['target_university_id'] ?? '';
                    
                    if (empty($sourceUniversityId) || empty($targetUniversityId)) {
                        $error = 'يرجى اختيار الجامعة المصدر والهدف';
                        break;
                    }

                    if ($sourceUniversityId === $targetUniversityId) {
                        $error = 'لا يمكن نسخ التقديرات لنفس الجامعة';
                        break;
                    }

                    // Get grades from source university
                    $stmt = $pdo->prepare("SELECT * FROM university_grades WHERE university_id = ?");
                    $stmt->execute([$sourceUniversityId]);
                    $sourceGrades = $stmt->fetchAll();

                    if (empty($sourceGrades)) {
                        $error = 'لا توجد تقديرات في الجامعة المصدر';
                        break;
                    }

                    // Copy grades to target university
                    $stmt = $pdo->prepare("
                        INSERT INTO university_grades (university_id, grade, points, description_ar, description_en, min_percentage, max_percentage, color_code)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE 
                        points = VALUES(points),
                        description_ar = VALUES(description_ar),
                        description_en = VALUES(description_en),
                        min_percentage = VALUES(min_percentage),
                        max_percentage = VALUES(max_percentage),
                        color_code = VALUES(color_code)
                    ");

                    foreach ($sourceGrades as $grade) {
                        $stmt->execute([
                            $targetUniversityId,
                            $grade['grade'],
                            $grade['points'],
                            $grade['description_ar'],
                            $grade['description_en'],
                            $grade['min_percentage'],
                            $grade['max_percentage'],
                            $grade['color_code']
                        ]);
                    }

                    $message = 'تم نسخ التقديرات بنجاح';
                    break;
            }
        }
    } catch (PDOException $e) {
        $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// Get data
try {
    // Get universities
    $universities = $pdo->query("SELECT id, name_ar FROM universities WHERE id != 'disabled' ORDER BY name_ar")->fetchAll();

    // Get grades with university names
    $grades = $pdo->query("
        SELECT ug.*, u.name_ar as university_name 
        FROM university_grades ug 
        LEFT JOIN universities u ON ug.university_id = u.id 
        ORDER BY u.name_ar, ug.points DESC
    ")->fetchAll();

} catch (PDOException $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
    $universities = [];
    $grades = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة تقديرات الجامعات - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-graduation-cap text-blue-600 ml-3"></i>
                            إدارة تقديرات الجامعات
                        </h1>
                        <p class="text-gray-600 mt-1">إدارة وتخصيص التقديرات لكل جامعة بشكل منفصل</p>
                    </div>
                    <button onclick="showAddModal()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة تقدير جديد
                    </button>
                </div>
            </header>

            <!-- Content -->
            <div class="p-6">
                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        <i class="fas fa-check-circle ml-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <i class="fas fa-exclamation-circle ml-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-4">إجراءات سريعة</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Add Default Grades -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">إضافة تقديرات افتراضية</h3>
                            <form method="POST" class="space-y-3">
                                <input type="hidden" name="action" value="add_default_grades">
                                <select name="university_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="">اختر الجامعة</option>
                                    <?php foreach ($universities as $university): ?>
                                        <option value="<?php echo htmlspecialchars($university['id']); ?>">
                                            <?php echo htmlspecialchars($university['name_ar']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="submit" class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-plus ml-2"></i>
                                    إضافة التقديرات الافتراضية
                                </button>
                            </form>
                        </div>

                        <!-- Copy Grades -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">نسخ التقديرات</h3>
                            <form method="POST" class="space-y-3">
                                <input type="hidden" name="action" value="copy_grades">
                                <select name="source_university_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="">من الجامعة</option>
                                    <?php foreach ($universities as $university): ?>
                                        <option value="<?php echo htmlspecialchars($university['id']); ?>">
                                            <?php echo htmlspecialchars($university['name_ar']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <select name="target_university_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="">إلى الجامعة</option>
                                    <?php foreach ($universities as $university): ?>
                                        <option value="<?php echo htmlspecialchars($university['id']); ?>">
                                            <?php echo htmlspecialchars($university['name_ar']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="submit" class="w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition-colors">
                                    <i class="fas fa-copy ml-2"></i>
                                    نسخ التقديرات
                                </button>
                            </form>
                        </div>

                        <!-- Statistics -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">إحصائيات</h3>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span>إجمالي الجامعات:</span>
                                    <span class="font-semibold"><?php echo count($universities); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span>إجمالي التقديرات:</span>
                                    <span class="font-semibold"><?php echo count($grades); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span>متوسط التقديرات لكل جامعة:</span>
                                    <span class="font-semibold"><?php echo count($universities) > 0 ? round(count($grades) / count($universities), 1) : 0; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Grades Table -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-bold text-gray-800">التقديرات المسجلة</h2>
                        <p class="text-gray-600 text-sm mt-1">جميع التقديرات المسجلة لكل جامعة</p>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الجامعة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التقدير</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النقاط</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النسبة المئوية</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اللون</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($grades)): ?>
                                    <tr>
                                        <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                                            <i class="fas fa-graduation-cap text-4xl mb-4 text-gray-300"></i>
                                            <p class="text-lg font-medium">لا توجد تقديرات مسجلة</p>
                                            <p class="text-sm">ابدأ بإضافة التقديرات الافتراضية أو إضافة تقدير جديد</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($grades as $grade): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($grade['university_name'] ?? 'غير محدد'); ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white"
                                                      style="background-color: <?php echo htmlspecialchars($grade['color_code']); ?>">
                                                    <?php echo htmlspecialchars($grade['grade']); ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($grade['points'], 2); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo htmlspecialchars($grade['description_ar']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($grade['min_percentage'], 1); ?>% - <?php echo number_format($grade['max_percentage'], 1); ?>%
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="w-6 h-6 rounded-full border border-gray-300"
                                                     style="background-color: <?php echo htmlspecialchars($grade['color_code']); ?>"></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button onclick="editGrade(<?php echo htmlspecialchars(json_encode($grade)); ?>)"
                                                        class="text-blue-600 hover:text-blue-900 ml-3">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button onclick="deleteGrade(<?php echo $grade['id']; ?>)"
                                                        class="text-red-600 hover:text-red-900">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Grade Modal -->
    <div id="gradeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 id="modalTitle" class="text-lg font-bold text-gray-900">إضافة تقدير جديد</h3>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form id="gradeForm" method="POST" class="space-y-4">
                        <input type="hidden" id="gradeId" name="grade_id">
                        <input type="hidden" id="formAction" name="action" value="add_grade">

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الجامعة</label>
                            <select id="universityId" name="university_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">اختر الجامعة</option>
                                <?php foreach ($universities as $university): ?>
                                    <option value="<?php echo htmlspecialchars($university['id']); ?>">
                                        <?php echo htmlspecialchars($university['name_ar']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">التقدير</label>
                                <input type="text" id="grade" name="grade" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                       placeholder="A+">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">النقاط</label>
                                <input type="number" id="points" name="points" required step="0.01" min="0" max="4"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                       placeholder="4.00">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الوصف بالعربية</label>
                            <input type="text" id="descriptionAr" name="description_ar" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                   placeholder="ممتاز">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الوصف بالإنجليزية</label>
                            <input type="text" id="descriptionEn" name="description_en" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                   placeholder="Excellent">
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">النسبة الدنيا (%)</label>
                                <input type="number" id="minPercentage" name="min_percentage" required step="0.1" min="0" max="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                       placeholder="90">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">النسبة العليا (%)</label>
                                <input type="number" id="maxPercentage" name="max_percentage" required step="0.1" min="0" max="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                       placeholder="100">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">لون التقدير</label>
                            <input type="color" id="colorCode" name="color_code"
                                   class="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                   value="#3B82F6">
                        </div>

                        <div class="flex gap-4 pt-4">
                            <button type="submit" class="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-save ml-2"></i>
                                حفظ
                            </button>
                            <button type="button" onclick="closeModal()" class="flex-1 bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 transition-colors">
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center ml-3">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">تأكيد الحذف</h3>
                    </div>

                    <p class="text-gray-600 mb-6">هل أنت متأكد من حذف هذا التقدير؟ لا يمكن التراجع عن هذا الإجراء.</p>

                    <form id="deleteForm" method="POST" class="flex gap-4">
                        <input type="hidden" name="action" value="delete_grade">
                        <input type="hidden" id="deleteGradeId" name="grade_id">

                        <button type="submit" class="flex-1 bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash ml-2"></i>
                            حذف
                        </button>
                        <button type="button" onclick="closeDeleteModal()" class="flex-1 bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 transition-colors">
                            إلغاء
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة تقدير جديد';
            document.getElementById('formAction').value = 'add_grade';
            document.getElementById('gradeForm').reset();
            document.getElementById('gradeId').value = '';
            document.getElementById('gradeModal').classList.remove('hidden');
        }

        function editGrade(grade) {
            document.getElementById('modalTitle').textContent = 'تعديل التقدير';
            document.getElementById('formAction').value = 'edit_grade';
            document.getElementById('gradeId').value = grade.id;
            document.getElementById('universityId').value = grade.university_id;
            document.getElementById('grade').value = grade.grade;
            document.getElementById('points').value = grade.points;
            document.getElementById('descriptionAr').value = grade.description_ar;
            document.getElementById('descriptionEn').value = grade.description_en;
            document.getElementById('minPercentage').value = grade.min_percentage;
            document.getElementById('maxPercentage').value = grade.max_percentage;
            document.getElementById('colorCode').value = grade.color_code;
            document.getElementById('gradeModal').classList.remove('hidden');
        }

        function deleteGrade(gradeId) {
            document.getElementById('deleteGradeId').value = gradeId;
            document.getElementById('deleteModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('gradeModal').classList.add('hidden');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.add('hidden');
        }

        // Close modals when clicking outside
        document.getElementById('gradeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });
    </script>
</body>
</html>
