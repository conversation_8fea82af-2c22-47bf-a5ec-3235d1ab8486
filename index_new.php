<?php
require_once 'functions.php';

// Initialize calculator
$calculator = new GPACalculator();
$calculator->loadFromSession();

// Get current language
$lang = $_SESSION['language'] ?? 'ar';
$isRTL = $lang === 'ar';
$dir = $isRTL ? 'rtl' : 'ltr';

// Get grading scale
$gradingScale = $calculator->getGradingScale();
$availableGrades = $calculator->getAvailableGrades();
$courses = $calculator->getCourses();
$calculation = $calculator->calculateGPA();
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('app_title'); ?> - <?php echo t('university_name'); ?></title>
    
    <!-- Optimized CSS -->
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: <?php echo $dir; ?>;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .controls {
            padding: 20px 30px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            padding: 30px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
        }
        
        .courses-section {
            background: white;
        }
        
        .sidebar {
            background: #f8fafc;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e2e8f0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .course-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .course-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .course-name {
            font-weight: 600;
            color: #1f2937;
            font-size: 1.1rem;
        }
        
        .course-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            color: #6b7280;
        }
        
        .detail-item {
            text-align: center;
            padding: 10px;
            background: #f9fafb;
            border-radius: 8px;
        }
        
        .detail-label {
            font-size: 0.875rem;
            margin-bottom: 4px;
        }
        
        .detail-value {
            font-weight: 600;
            color: #1f2937;
        }
        
        .gpa-result {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 25px;
        }
        
        .gpa-value {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .gpa-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e5e7eb;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .grading-scale {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        
        .grading-scale h3 {
            background: #3b82f6;
            color: white;
            padding: 15px 20px;
            margin: 0;
            font-size: 1.1rem;
        }
        
        .grade-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .grade-item:last-child {
            border-bottom: none;
        }
        
        .grade-letter {
            font-weight: 700;
            color: #1f2937;
        }
        
        .grade-points {
            color: #3b82f6;
            font-weight: 600;
        }
        
        .grade-range {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }
        
        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #374151;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid;
        }
        
        .alert-success {
            background: #d1fae5;
            border-color: #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border-color: #ef4444;
            color: #991b1b;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><?php echo t('app_title'); ?></h1>
            <p><?php echo t('university_name'); ?></p>
        </div>
        
        <!-- Controls -->
        <div class="controls">
            <div>
                <button class="btn btn-secondary" onclick="toggleLanguage()">
                    🌐 <?php echo t('language'); ?>
                </button>
                <button class="btn btn-secondary" onclick="exportData()">
                    📤 <?php echo t('export'); ?>
                </button>
                <button class="btn btn-secondary" onclick="importData()">
                    📁 <?php echo t('import'); ?>
                </button>
            </div>
            <div>
                <button class="btn btn-primary" onclick="clearAll()">
                    🗑️ مسح الكل
                </button>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Courses Section -->
            <div class="courses-section">
                <div id="alert-container"></div>
                
                <div id="courses-list">
                    <?php if (empty($courses)): ?>
                        <div class="empty-state">
                            <h3>لا توجد مواد مضافة</h3>
                            <p>ابدأ بإضافة المواد الدراسية لحساب المعدل التراكمي</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($courses as $index => $course): ?>
                            <div class="course-item" data-index="<?php echo $index; ?>">
                                <div class="course-header">
                                    <div class="course-name"><?php echo htmlspecialchars($course['name']); ?></div>
                                    <button class="btn btn-secondary" onclick="removeCourse(<?php echo $index; ?>)">
                                        🗑️ <?php echo t('remove'); ?>
                                    </button>
                                </div>
                                <div class="course-details">
                                    <div class="detail-item">
                                        <div class="detail-label"><?php echo t('credit_hours'); ?></div>
                                        <div class="detail-value"><?php echo $course['credit_hours']; ?></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label"><?php echo t('grade'); ?></div>
                                        <div class="detail-value"><?php echo $course['grade']; ?></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">النقاط</div>
                                        <div class="detail-value"><?php echo $course['points']; ?></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">نقاط الجودة</div>
                                        <div class="detail-value"><?php echo number_format($course['quality_points'], 2); ?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>جاري المعالجة...</p>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="sidebar">
                <!-- GPA Result -->
                <div class="gpa-result">
                    <div class="gpa-value" id="gpa-value"><?php echo number_format($calculation['gpa'], 2); ?></div>
                    <div class="gpa-label"><?php echo t('gpa_result'); ?></div>
                </div>
                
                <!-- Statistics -->
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="total-hours"><?php echo $calculation['total_hours']; ?></div>
                        <div class="stat-label"><?php echo t('total_hours'); ?></div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="total-points"><?php echo number_format($calculation['total_points'], 1); ?></div>
                        <div class="stat-label"><?php echo t('total_points'); ?></div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="courses-count"><?php echo $calculation['courses_count']; ?></div>
                        <div class="stat-label">المواد</div>
                    </div>
                </div>
                
                <!-- Add Course Form -->
                <form id="course-form" style="margin-top: 30px;">
                    <h3 style="margin-bottom: 20px; color: #374151;"><?php echo t('add_course'); ?></h3>
                    
                    <div class="form-group">
                        <label for="course-name"><?php echo t('course_name'); ?></label>
                        <input type="text" id="course-name" name="name" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="credit-hours"><?php echo t('credit_hours'); ?></label>
                        <input type="number" id="credit-hours" name="credit_hours" class="form-control" min="1" max="6" step="1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="grade"><?php echo t('grade'); ?></label>
                        <select id="grade" name="grade" class="form-control" required>
                            <option value="">اختر الدرجة</option>
                            <?php foreach ($availableGrades as $grade): ?>
                                <option value="<?php echo $grade; ?>"><?php echo $grade; ?> (<?php echo $gradingScale[$grade]['points']; ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        ➕ <?php echo t('add_course'); ?>
                    </button>
                </form>
                
                <!-- Grading Scale -->
                <div class="grading-scale" style="margin-top: 30px;">
                    <h3><?php echo t('grading_scale'); ?></h3>
                    <?php foreach ($gradingScale as $grade => $info): ?>
                        <div class="grade-item">
                            <span class="grade-letter"><?php echo $grade; ?></span>
                            <span class="grade-points"><?php echo $info['points']; ?></span>
                            <span class="grade-range"><?php echo $info['min']; ?>-<?php echo $info['max']; ?>%</span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <input type="file" id="import-file" accept=".json" style="display: none;">
    
    <script>
        // Optimized JavaScript for GPA Calculator

        function showLoading(show = true) {
            const loading = document.getElementById('loading');
            const coursesList = document.getElementById('courses-list');
            if (show) {
                loading.style.display = 'block';
                coursesList.style.opacity = '0.5';
            } else {
                loading.style.display = 'none';
                coursesList.style.opacity = '1';
            }
        }

        function showAlert(message, type = 'success') {
            const container = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            container.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        function updateStats(calculation) {
            document.getElementById('gpa-value').textContent = calculation.gpa.toFixed(2);
            document.getElementById('total-hours').textContent = calculation.total_hours;
            document.getElementById('total-points').textContent = calculation.total_points.toFixed(1);
            document.getElementById('courses-count').textContent = calculation.courses_count;
        }

        function makeRequest(action, data = {}) {
            showLoading(true);

            const formData = new FormData();
            formData.append('action', action);

            for (const key in data) {
                formData.append(key, data[key]);
            }

            return fetch('functions.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                showLoading(false);
                return result;
            })
            .catch(error => {
                showLoading(false);
                console.error('Error:', error);
                showAlert('حدث خطأ في الاتصال', 'error');
                throw error;
            });
        }

        // Add course form handler
        document.getElementById('course-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = {
                name: formData.get('name'),
                credit_hours: formData.get('credit_hours'),
                grade: formData.get('grade')
            };

            makeRequest('add_course', data)
                .then(result => {
                    if (result.success) {
                        showAlert('تم إضافة المادة بنجاح');
                        updateStats(result.calculation);
                        this.reset();
                        location.reload(); // Refresh to show new course
                    } else {
                        showAlert(result.errors ? result.errors.join(', ') : 'فشل في إضافة المادة', 'error');
                    }
                });
        });

        function removeCourse(index) {
            if (confirm('هل أنت متأكد من حذف هذه المادة؟')) {
                makeRequest('remove_course', { index: index })
                    .then(result => {
                        if (result.success) {
                            showAlert('تم حذف المادة بنجاح');
                            updateStats(result.calculation);
                            location.reload(); // Refresh to update list
                        } else {
                            showAlert('فشل في حذف المادة', 'error');
                        }
                    });
            }
        }

        function clearAll() {
            if (confirm('هل أنت متأكد من حذف جميع المواد؟')) {
                makeRequest('clear')
                    .then(result => {
                        if (result.success) {
                            showAlert('تم مسح جميع المواد');
                            location.reload();
                        }
                    });
            }
        }

        function toggleLanguage() {
            const currentLang = document.documentElement.lang;
            const newLang = currentLang === 'ar' ? 'en' : 'ar';

            makeRequest('change_language', { language: newLang })
                .then(result => {
                    if (result.success) {
                        location.reload();
                    }
                });
        }

        function exportData() {
            makeRequest('export')
                .then(result => {
                    if (result.success) {
                        const blob = new Blob([result.data], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `gpa_data_${new Date().toISOString().split('T')[0]}.json`;
                        a.click();
                        URL.revokeObjectURL(url);
                        showAlert('تم تصدير البيانات بنجاح');
                    }
                });
        }

        function importData() {
            document.getElementById('import-file').click();
        }

        document.getElementById('import-file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    makeRequest('import', { data: e.target.result })
                        .then(result => {
                            if (result.success) {
                                showAlert('تم استيراد البيانات بنجاح');
                                updateStats(result.calculation);
                                location.reload();
                            } else {
                                showAlert('فشل في استيراد البيانات', 'error');
                            }
                        });
                };
                reader.readAsText(file);
            }
        });

        // Auto-calculate on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('GPA Calculator loaded successfully');
        });
    </script>
</body>
</html>
