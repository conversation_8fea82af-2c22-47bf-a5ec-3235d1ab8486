/**
 * Enhanced GPA Calculator JavaScript
 * Arab Open University System
 */

class GPACalculator {
    constructor() {
        this.courses = [];
        this.currentLanguage = window.currentLanguage || 'ar';
        this.gradingSystem = window.gradingSystem || {};
        this.currentResults = null;
        this.adminData = {
            students: JSON.parse(localStorage.getItem('gpaStudents') || '[]'),
            stats: JSON.parse(localStorage.getItem('gpaStats') || '{}')
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.addInitialCourse();
        this.updateLanguage();
        this.checkForSharedGPA();
        this.loadSavedData();
        this.addInitialCourse();
        this.loadSavedData();
        this.checkForSharedGPA();
        this.setupCalculationType();
    }

    setupEventListeners() {
        // Add course button
        const addCourseBtn = document.getElementById('addCourseBtn');
        if (addCourseBtn) {
            addCourseBtn.addEventListener('click', () => this.addCourse());
        }

        // Calculate button
        const calculateBtn = document.getElementById('calculateBtn');
        if (calculateBtn) {
            calculateBtn.addEventListener('click', () => this.calculateGPA());
        }

        // Language toggle
        const languageBtn = document.getElementById('languageBtn');
        const languageToggle = document.getElementById('languageToggle');
        if (languageBtn) {
            languageBtn.addEventListener('click', () => this.toggleLanguage());
        }
        if (languageToggle) {
            languageToggle.addEventListener('click', () => this.toggleLanguage());
        }

        // Share button
        const shareBtn = document.getElementById('shareBtn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.openShareModal());
        }

        // Admin button
        const adminBtn = document.getElementById('adminBtn');
        if (adminBtn) {
            adminBtn.addEventListener('click', () => this.openAdminModal());
        }

        // Save/Load buttons
        const saveBtn = document.getElementById('saveBtn');
        const loadBtn = document.getElementById('loadBtn');
        
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveCourses());
        }
        
        if (loadBtn) {
            loadBtn.addEventListener('click', () => this.loadCourses());
        }

        // Grading system selector
        const gradingSystemSelect = document.getElementById('gradingSystemSelect');
        if (gradingSystemSelect) {
            gradingSystemSelect.addEventListener('change', (e) => {
                this.changeGradingSystem(e.target.value);
            });
        }

        // University selector
        const universitySelect = document.getElementById('universitySelect');
        if (universitySelect) {
            universitySelect.addEventListener('change', (e) => {
                this.changeUniversity(e.target.value);
            });
        }

        // Calculation type selector
        const calculationType = document.getElementById('calculationType');
        if (calculationType) {
            calculationType.addEventListener('change', (e) => {
                this.togglePreviousGpaSection(e.target.value);
            });
        }

        // Share form
        const shareForm = document.getElementById('shareForm');
        if (shareForm) {
            shareForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleShareForm();
            });
        }

        // Chat functionality
        const sendChatBtn = document.getElementById('sendChatBtn');
        const chatInput = document.getElementById('chatInput');

        if (sendChatBtn) {
            sendChatBtn.addEventListener('click', () => this.sendChatMessage());
        }

        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendChatMessage();
                }
            });
        }

        // Data buttons
        const saveDataBtn = document.getElementById('saveDataBtn');
        const loadDataBtn = document.getElementById('loadDataBtn');

        if (saveDataBtn) {
            saveDataBtn.addEventListener('click', () => this.saveCourses());
        }

        if (loadDataBtn) {
            loadDataBtn.addEventListener('click', () => this.loadCourses());
        }
    }

    openShareModal() {
        document.getElementById('shareModal').classList.remove('hidden');
    }

    openAdminModal() {
        document.getElementById('adminModal').classList.remove('hidden');
        this.updateAdminStats();
        this.updateStudentsTable();
    }

    updateAdminStats() {
        const students = this.adminData.students;
        const totalUsers = students.length;
        const avgGPA = students.length > 0 ?
            (students.reduce((sum, student) => sum + (student.gpa || 0), 0) / students.length).toFixed(2) :
            '0.00';

        const today = new Date().toDateString();
        const todayCalculations = students.filter(student =>
            new Date(student.timestamp).toDateString() === today
        ).length;

        document.getElementById('totalUsers').textContent = totalUsers;
        document.getElementById('avgGPA').textContent = avgGPA;
        document.getElementById('todayCalculations').textContent = todayCalculations;
    }

    updateStudentsTable() {
        const tbody = document.getElementById('studentsTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        this.adminData.students.forEach((student, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.name}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.phone}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.gpa || 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.classification || 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${new Date(student.timestamp).toLocaleDateString()}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="deleteStudent(${index})" class="text-red-600 hover:text-red-900">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    addCourse(courseData = null) {
        const container = document.getElementById('coursesContainer');
        if (!container) return;

        const courseId = 'course_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        const courseDiv = document.createElement('div');
        courseDiv.className = 'course-item bg-gray-50 rounded-lg p-4 border border-gray-200 mb-4';
        courseDiv.id = courseId;

        courseDiv.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        ${this.currentLanguage === 'ar' ? 'اسم المادة' : 'Course Name'}
                    </label>
                    <input type="text"
                           class="course-name w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="${this.currentLanguage === 'ar' ? 'مثال: الرياضيات' : 'e.g., Mathematics'}"
                           value="${courseData?.name || ''}">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        ${this.currentLanguage === 'ar' ? 'عدد الساعات' : 'Credit Hours'}
                    </label>
                    <input type="number"
                           class="course-hours w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           min="1" max="6"
                           placeholder="3"
                           value="${courseData?.hours || ''}">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        ${this.currentLanguage === 'ar' ? 'التقدير' : 'Grade'}
                    </label>
                    <select class="course-grade w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">${this.currentLanguage === 'ar' ? 'اختر التقدير' : 'Select Grade'}</option>
                        ${this.getGradeOptions(courseData?.grade)}
                    </select>
                </div>

                <div class="flex justify-center">
                    <button type="button"
                            class="remove-course bg-red-500 text-white p-2 rounded-lg hover:bg-red-600 transition-colors duration-300"
                            onclick="gpaCalculator.removeCourse('${courseId}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        container.appendChild(courseDiv);

        // Add animation
        courseDiv.style.opacity = '0';
        courseDiv.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            courseDiv.style.transition = 'all 0.3s ease';
            courseDiv.style.opacity = '1';
            courseDiv.style.transform = 'translateY(0)';
        }, 10);
    }

    removeCourse(courseId) {
        const courseElement = document.getElementById(courseId);
        if (courseElement) {
            courseElement.remove();
            this.courses = this.courses.filter(course => course.id !== courseId);
        }
    }

    getGradeOptions(selectedGrade = '') {
        // This will be populated by PHP, but for now return empty
        // The actual grades will be loaded from the server
        return '';
    }

    addInitialCourse() {
        if (this.courses.length === 0) {
            this.addCourse();
        }
    }

    async calculateGPA() {
        const calculateBtn = document.getElementById('calculateBtn');

        // Show loading
        calculateBtn.disabled = true;
        calculateBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>' +
            (this.currentLanguage === 'ar' ? 'جاري الحساب...' : 'Calculating...');

        try {
            // Collect course data
            const coursesData = this.collectCoursesData();

            if (coursesData.length === 0) {
                throw new Error(this.currentLanguage === 'ar' ? 'يرجى إضافة مادة واحدة على الأقل' : 'Please add at least one course');
            }

            // Get calculation type and previous data
            const calculationType = document.getElementById('calculationType').value;
            const previousGpa = parseFloat(document.getElementById('previousGpa')?.value || 0);
            const previousHours = parseInt(document.getElementById('previousHours')?.value || 0);

            // Send to server
            const response = await fetch('index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'calculate_gpa',
                    courses: JSON.stringify(coursesData),
                    previous_gpa: previousGpa,
                    previous_hours: previousHours
                })
            });

            const result = await response.json();

            if (result.error) {
                throw new Error(result.error);
            }

            this.currentResults = result;
            this.displayResults(result);

        } catch (error) {
            console.error('Error calculating GPA:', error);
            this.showAlert(error.message || 'حدث خطأ في حساب المعدل', 'error');
        } finally {
            // Hide loading
            calculateBtn.disabled = false;
            calculateBtn.innerHTML = '<i class="fas fa-calculator ' +
                (this.currentLanguage === 'ar' ? 'ml-2' : 'mr-2') + '"></i>' +
                '<span>' + (this.currentLanguage === 'ar' ? 'احسب المعدل' : 'Calculate GPA') + '</span>';
        }
    }

    collectCoursesData() {
        const coursesData = [];
        const courseElements = document.querySelectorAll('.course-item');
        
        courseElements.forEach(element => {
            const name = element.querySelector('.course-name').value.trim();
            const hours = parseInt(element.querySelector('.course-hours').value);
            const grade = element.querySelector('.course-grade').value;
            
            if (name && hours && grade) {
                coursesData.push({ name, hours, grade });
            }
        });
        
        return coursesData;
    }

    displayResults(result) {
        const resultsSection = document.getElementById('resultsSection');
        const semesterGpaValue = document.getElementById('semesterGpaValue');
        const cumulativeGpaValue = document.getElementById('cumulativeGpaValue');
        const totalHoursValue = document.getElementById('totalHoursValue');

        // Update values with animation
        this.animateNumber(semesterGpaValue, result.semester_gpa || result.gpa);
        this.animateNumber(cumulativeGpaValue, result.cumulative_gpa || result.gpa);
        this.animateNumber(totalHoursValue, result.total_hours);

        // Show results with animation
        resultsSection.classList.remove('hidden');
        resultsSection.classList.add('fade-in');
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    animateNumber(element, targetValue) {
        const startValue = 0;
        const duration = 1000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const currentValue = startValue + (targetValue - startValue) * progress;

            if (element.id.includes('Gpa')) {
                element.textContent = currentValue.toFixed(2);
            } else {
                element.textContent = Math.floor(currentValue);
            }

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    async toggleLanguage() {
        const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        
        try {
            const response = await fetch('index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'change_language',
                    language: newLanguage
                })
            });

            const result = await response.json();
            
            if (result.success) {
                location.reload(); // Reload to apply language changes
            }
            
        } catch (error) {
            console.error('Error changing language:', error);
        }
    }

    async saveCourses() {
        try {
            const coursesData = this.collectCoursesData();
            
            const response = await fetch('index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'save_courses',
                    courses: JSON.stringify(coursesData)
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('تم حفظ البيانات بنجاح', 'success');
            } else {
                throw new Error('Failed to save');
            }
            
        } catch (error) {
            console.error('Error saving courses:', error);
            this.showAlert('حدث خطأ في حفظ البيانات', 'error');
        }
    }

    async loadCourses() {
        try {
            const response = await fetch('index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'load_courses'
                })
            });

            const result = await response.json();
            
            if (result.courses && result.courses.length > 0) {
                // Clear existing courses
                this.clearCourses();
                
                // Add loaded courses
                result.courses.forEach(course => {
                    this.addCourse(course);
                });
                
                this.showAlert('تم تحميل البيانات بنجاح', 'success');
            } else {
                this.showAlert('لا توجد بيانات محفوظة', 'info');
            }
            
        } catch (error) {
            console.error('Error loading courses:', error);
            this.showAlert('حدث خطأ في تحميل البيانات', 'error');
        }
    }

    async loadSavedCourses() {
        // Load courses on page load
        await this.loadCourses();
    }

    clearCourses() {
        const container = document.getElementById('coursesContainer');
        if (container) {
            container.innerHTML = '';
        }
        this.courses = [];
    }

    openShareModal() {
        if (!this.currentResults) {
            this.showAlert('يرجى حساب المعدل أولاً', 'error');
            return;
        }

        document.getElementById('shareModal').classList.remove('hidden');
    }

    async changeGradingSystem(system) {
        try {
            const response = await fetch('index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'change_grading_system',
                    system: system
                })
            });

            const result = await response.json();

            if (result.success) {
                location.reload(); // Reload to apply system changes
            }

        } catch (error) {
            console.error('Error changing grading system:', error);
        }
    }

    async changeUniversity(universityId) {
        try {
            const response = await fetch('universities.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'select_university',
                    university_id: universityId
                })
            });

            const result = await response.json();

            if (result.success) {
                location.reload(); // Reload to apply university changes
            }

        } catch (error) {
            console.error('Error changing university:', error);
        }
    }

    showAlert(message, type = 'info') {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            'bg-blue-500'
        }`;
        alert.innerHTML = `
            <div class="flex items-center gap-2">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                }"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(alert);

        // Remove after 3 seconds
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }

    // Additional methods from original version
    setupCalculationType() {
        const calculationType = document.getElementById('calculationType');
        if (calculationType) {
            this.togglePreviousGpaSection(calculationType.value);
        }
    }

    togglePreviousGpaSection(type) {
        const section = document.getElementById('previousGpaSection');
        if (section) {
            if (type === 'cumulative') {
                section.classList.remove('hidden');
            } else {
                section.classList.add('hidden');
            }
        }
    }

    updateLanguage() {
        // Language update logic handled by PHP reload
    }

    async handleShareForm() {
        const name = document.getElementById('studentName').value.trim();
        const phone = document.getElementById('studentPhone').value.trim();

        if (!name || !phone) {
            this.showAlert('يرجى ملء جميع الحقول', 'error');
            return;
        }

        if (!this.currentResults) {
            this.showAlert('يرجى حساب المعدل أولاً', 'error');
            return;
        }

        try {
            // Show loading
            this.showLoadingOverlay(true);

            // Collect current data
            const coursesData = this.collectCoursesData();
            const calculationType = document.getElementById('calculationType').value;
            const previousGpa = parseFloat(document.getElementById('previousGpa')?.value || 0);
            const previousHours = parseInt(document.getElementById('previousHours')?.value || 0);

            // Create student data
            const studentData = {
                name: name,
                phone: phone,
                gpa: this.currentResults.gpa || this.currentResults.cumulative_gpa,
                classification: this.currentResults.classification?.name || 'غير محدد',
                courses: coursesData,
                calculation_type: calculationType,
                previous_gpa: previousGpa,
                previous_hours: previousHours,
                timestamp: new Date().toISOString()
            };

            // Save student data to server
            const response = await fetch('share_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'save_student_data',
                    name: name,
                    phone: phone,
                    gpa: this.currentResults.cumulative_gpa || this.currentResults.gpa,
                    courses: JSON.stringify(coursesData),
                    calculation_type: calculationType,
                    previous_gpa: previousGpa,
                    previous_hours: previousHours
                })
            });

            const result = await response.json();

            if (result.success) {
                // Generate URL
                const shareUrl = `${window.location.origin}${window.location.pathname}?shared=${result.link_id}`;

                // Show generated link
                document.getElementById('shareUrl').value = shareUrl;
                document.getElementById('generatedLink').classList.remove('hidden');

                // Save to localStorage for admin panel
                this.adminData.students.push(studentData);
                localStorage.setItem('gpaStudents', JSON.stringify(this.adminData.students));

                this.showAlert('تم إنشاء الرابط وحفظ البيانات بنجاح', 'success');
            } else {
                throw new Error(result.error || 'فشل في حفظ البيانات');
            }

        } catch (error) {
            console.error('Error in share form:', error);
            this.showAlert('حدث خطأ في حفظ البيانات: ' + error.message, 'error');
        } finally {
            this.showLoadingOverlay(false);
        }
    }

    checkForSharedGPA() {
        const urlParams = new URLSearchParams(window.location.search);
        const sharedId = urlParams.get('shared');
        if (sharedId) {
            this.loadSharedGPA(sharedId);
        }
    }

    loadSharedGPA(sharedId) {
        try {
            const sharedData = localStorage.getItem(`shared_${sharedId}`);
            if (sharedData) {
                const data = JSON.parse(sharedData);

                this.clearCourses();

                // Load courses
                if (data.courses) {
                    data.courses.forEach(course => {
                        this.addCourse({
                            name: course.name,
                            hours: course.hours,
                            grade: course.grade
                        });
                    });
                }

                // Set previous GPA data if available
                if (data.previous_gpa > 0) {
                    document.getElementById('calculationType').value = 'cumulative';
                    this.togglePreviousGpaSection('cumulative');
                    document.getElementById('previousGpa').value = data.previous_gpa;
                    document.getElementById('previousHours').value = data.previous_hours;
                }

                this.showAlert('تم تحميل البيانات المشاركة بنجاح', 'success');
            } else {
                this.showAlert('الرابط غير صحيح أو منتهي الصلاحية', 'error');
            }

        } catch (error) {
            console.error('Error loading shared data:', error);
            this.showAlert('خطأ في تحميل البيانات المشاركة', 'error');
        }
    }

    loadSavedData() {
        // Load from localStorage for compatibility
        const savedData = localStorage.getItem('gpaCalculatorData');
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                if (data.courses && data.courses.length > 0) {
                    this.clearCourses();
                    data.courses.forEach(course => {
                        this.addCourse(course);
                    });
                }
            } catch (error) {
                console.error('Error loading saved data:', error);
            }
        }
    }

    openAdminModal() {
        document.getElementById('adminModal').classList.remove('hidden');
        this.loadAdminData();
    }

    async loadAdminData() {
        try {
            // Load statistics
            const statsResponse = await fetch('share_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'get_admin_stats'
                })
            });

            const stats = await statsResponse.json();
            this.updateAdminStats(stats);

            // Load students data
            const studentsResponse = await fetch('share_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'get_students_data'
                })
            });

            const students = await studentsResponse.json();
            this.updateStudentsTable(students);

        } catch (error) {
            console.error('Error loading admin data:', error);
            this.showAlert('خطأ في تحميل بيانات الإدارة', 'error');
        }
    }

    updateAdminStats(stats) {
        // Update statistics display
        document.getElementById('totalUsersValue').textContent = stats.total_users || 0;
        document.getElementById('totalCalculationsValue').textContent = stats.total_calculations || 0;
        document.getElementById('avgGpaValue').textContent = (stats.avg_gpa || 0).toFixed(2);
        document.getElementById('todayCalculationsValue').textContent = stats.today_calculations || 0;

        // Update grade distribution chart (simple version)
        const gradeDistribution = stats.grade_distribution || {};
        let distributionHtml = '';
        for (const [grade, count] of Object.entries(gradeDistribution)) {
            const percentage = stats.total_users > 0 ? ((count / stats.total_users) * 100).toFixed(1) : 0;
            distributionHtml += `
                <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span class="font-medium">${grade}</span>
                    <span class="text-sm text-gray-600">${count} (${percentage}%)</span>
                </div>
            `;
        }
        document.getElementById('gradeDistributionChart').innerHTML = distributionHtml || '<p class="text-gray-500 text-center">لا توجد بيانات</p>';
    }

    updateStudentsTable(students) {
        const tbody = document.getElementById('studentsTableBody');

        if (!students || students.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4 text-gray-500">لا توجد بيانات طلاب</td></tr>';
            return;
        }

        tbody.innerHTML = students.map((student, index) => `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${student.name}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${student.phone}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.gpa}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${student.classification}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${student.date}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="deleteStudent('${student.link_id || index}')" class="text-red-600 hover:text-red-900">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    showLoadingOverlay(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }
    }

    sendChatMessage() {
        const input = document.getElementById('chatInput');
        const container = document.getElementById('chatContainer');

        if (!input.value.trim()) return;

        const message = input.value.trim();
        input.value = '';

        // Add user message
        this.addChatMessage(message, 'user');

        // Simulate AI response
        setTimeout(() => {
            const response = this.generateAIResponse(message);
            this.addChatMessage(response, 'ai');
        }, 1000);
    }

    addChatMessage(message, sender) {
        const container = document.getElementById('chatContainer');

        // Remove welcome message if exists
        const welcomeMessage = container.querySelector('#welcomeMessage');
        if (welcomeMessage) {
            welcomeMessage.parentElement.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `mb-4 ${sender === 'user' ? 'text-right' : 'text-left'}`;

        messageDiv.innerHTML = `
            <div class="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                sender === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-800'
            }">
                ${message}
            </div>
        `;

        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
    }

    generateAIResponse(message) {
        const responses = {
            ar: [
                'يمكنني مساعدتك في حساب معدلك التراكمي. أضف مواد دراسية واضغط على احسب المعدل.',
                'لتحسين معدلك، ركز على المواد ذات الساعات الأكثر واحرص على الحصول على درجات عالية.',
                'المعدل التراكمي يحسب بضرب نقاط كل مادة في عدد ساعاتها ثم القسمة على إجمالي الساعات.',
                'إذا كان معدلك أقل من 2.0، فأنت بحاجة لتحسين أدائك الأكاديمي.',
                'يمكنك حفظ بياناتك ومشاركتها مع الآخرين باستخدام الأزرار المتاحة.',
                'نظام التقدير في الجامعة العربية المفتوحة يتراوح من A (4.0) إلى F (0.0).',
                'للحصول على تقدير ممتاز تحتاج معدل 3.75 أو أكثر.'
            ],
            en: [
                'I can help you calculate your GPA. Add courses and click Calculate GPA.',
                'To improve your GPA, focus on courses with more credit hours and aim for high grades.',
                'GPA is calculated by multiplying grade points by credit hours, then dividing by total hours.',
                'If your GPA is below 2.0, you need to improve your academic performance.',
                'You can save your data and share it with others using the available buttons.',
                'The grading system ranges from A (4.0) to F (0.0).',
                'To get an excellent grade, you need a GPA of 3.75 or higher.'
            ]
        };

        const langResponses = responses[this.currentLanguage] || responses.ar;

        // Simple keyword matching for more relevant responses
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('معدل') || lowerMessage.includes('gpa')) {
            return langResponses[0];
        } else if (lowerMessage.includes('تحسين') || lowerMessage.includes('improve')) {
            return langResponses[1];
        } else if (lowerMessage.includes('كيف') || lowerMessage.includes('how')) {
            return langResponses[2];
        } else {
            return langResponses[Math.floor(Math.random() * langResponses.length)];
        }
    }

    loadSavedData() {
        // Load from localStorage for compatibility
        const savedData = localStorage.getItem('gpaCalculatorData');
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                if (data.courses && data.courses.length > 0) {
                    this.clearCourses();
                    data.courses.forEach(course => {
                        this.addCourse(course);
                    });
                }
            } catch (error) {
                console.error('Error loading saved data:', error);
            }
        }
    }

    checkForSharedGPA() {
        const urlParams = new URLSearchParams(window.location.search);
        const sharedId = urlParams.get('shared');
        if (sharedId) {
            this.loadSharedGPA(sharedId);
        }
    }

    loadSharedGPA(sharedId) {
        // Load shared GPA data
        const sharedData = localStorage.getItem(`shared_${sharedId}`);
        if (sharedData) {
            try {
                const data = JSON.parse(sharedData);
                this.clearCourses();
                data.courses.forEach(course => {
                    this.addCourse(course);
                });
                this.showAlert('تم تحميل البيانات المشاركة بنجاح', 'success');
            } catch (error) {
                console.error('Error loading shared data:', error);
                this.showAlert('خطأ في تحميل البيانات المشاركة', 'error');
            }
        }
    }

    async handleShareForm() {
        const name = document.getElementById('studentName').value;
        const phone = document.getElementById('studentPhone').value;

        if (!name || !phone) {
            this.showAlert('يرجى ملء جميع الحقول', 'error');
            return;
        }

        // Collect current data
        const coursesData = this.collectCoursesData();

        if (coursesData.length === 0) {
            this.showAlert('يرجى إضافة مواد دراسية أولاً', 'error');
            return;
        }

        // Calculate GPA first
        try {
            const response = await fetch('index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'calculate_gpa',
                    courses: JSON.stringify(coursesData)
                })
            });

            const gpaResult = await response.json();

            if (gpaResult.error) {
                throw new Error(gpaResult.error);
            }

            // Save student data to server
            const saveResponse = await fetch('index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'save_student_data',
                    name: name,
                    phone: phone,
                    gpa: gpaResult.gpa,
                    courses: JSON.stringify(coursesData)
                })
            });

            const saveResult = await response.json();

            if (saveResult.success) {
                // Generate URL
                const shareUrl = `${window.location.origin}${window.location.pathname}?shared=${saveResult.id}`;

                // Show generated link
                document.getElementById('shareUrl').value = shareUrl;
                document.getElementById('generatedLink').classList.remove('hidden');

                this.showAlert('تم إنشاء الرابط وحفظ البيانات بنجاح', 'success');
            } else {
                throw new Error(saveResult.error || 'فشل في حفظ البيانات');
            }

        } catch (error) {
            console.error('Error in share form:', error);
            this.showAlert('حدث خطأ في حفظ البيانات', 'error');
        }
    }

    sendChatMessage() {
        const input = document.getElementById('chatInput');
        const container = document.getElementById('chatContainer');

        if (!input.value.trim()) return;

        const message = input.value.trim();
        input.value = '';

        // Add user message
        this.addChatMessage(message, 'user');

        // Simulate AI response
        setTimeout(() => {
            const response = this.generateAIResponse(message);
            this.addChatMessage(response, 'ai');
        }, 1000);
    }

    addChatMessage(message, sender) {
        const container = document.getElementById('chatContainer');
        const messageDiv = document.createElement('div');
        messageDiv.className = `mb-4 ${sender === 'user' ? 'text-right' : 'text-left'}`;

        messageDiv.innerHTML = `
            <div class="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                sender === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-800'
            }">
                ${message}
            </div>
        `;

        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
    }

    generateAIResponse(message) {
        const responses = {
            ar: [
                'يمكنني مساعدتك في حساب معدلك التراكمي. أضف مواد دراسية واضغط على احسب المعدل.',
                'لتحسين معدلك، ركز على المواد ذات الساعات الأكثر واحرص على الحصول على درجات عالية.',
                'المعدل التراكمي يحسب بضرب نقاط كل مادة في عدد ساعاتها ثم القسمة على إجمالي الساعات.',
                'إذا كان معدلك أقل من 2.0، فأنت بحاجة لتحسين أدائك الأكاديمي.',
                'يمكنك حفظ بياناتك ومشاركتها مع الآخرين باستخدام الأزرار المتاحة.'
            ],
            en: [
                'I can help you calculate your GPA. Add courses and click Calculate GPA.',
                'To improve your GPA, focus on courses with more credit hours and aim for high grades.',
                'GPA is calculated by multiplying grade points by credit hours, then dividing by total hours.',
                'If your GPA is below 2.0, you need to improve your academic performance.',
                'You can save your data and share it with others using the available buttons.'
            ]
        };

        const langResponses = responses[this.currentLanguage] || responses.ar;
        return langResponses[Math.floor(Math.random() * langResponses.length)];
    }
}

// Global functions for modal handling
function closeShareModal() {
    document.getElementById('shareModal').classList.add('hidden');
}

function closeAdminModal() {
    document.getElementById('adminModal').classList.add('hidden');
}

function copyLink() {
    const shareUrl = document.getElementById('shareUrl');
    shareUrl.select();
    document.execCommand('copy');
    window.gpaCalculator.showAlert('تم نسخ الرابط', 'success');
}

function showAdminTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.admin-content').forEach(tab => {
        tab.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.admin-tab').forEach(btn => {
        btn.classList.remove('active', 'border-purple-500', 'text-purple-600');
        btn.classList.add('border-transparent', 'text-gray-500');
    });

    // Show selected tab
    document.getElementById(tabName + 'Tab').classList.remove('hidden');

    // Add active class to selected button
    event.target.closest('.admin-tab').classList.add('active', 'border-purple-500', 'text-purple-600');
    event.target.closest('.admin-tab').classList.remove('border-transparent', 'text-gray-500');
}

function deleteStudent(index) {
    if (confirm('هل أنت متأكد من حذف بيانات هذا الطالب؟')) {
        if (window.gpaCalculator) {
            window.gpaCalculator.adminData.students.splice(index, 1);
            localStorage.setItem('gpaStudents', JSON.stringify(window.gpaCalculator.adminData.students));
            window.gpaCalculator.updateStudentsTable();
            window.gpaCalculator.updateAdminStats();
            window.gpaCalculator.showAlert('تم حذف بيانات الطالب بنجاح', 'success');
        }
    }
}
}

// Global functions for modal handling
function closeShareModal() {
    document.getElementById('shareModal').classList.add('hidden');
    document.getElementById('generatedLink').classList.add('hidden');
    document.getElementById('shareForm').reset();
}

function closeAdminModal() {
    document.getElementById('adminModal').classList.add('hidden');
}

function copyLink() {
    const shareUrl = document.getElementById('shareUrl');
    shareUrl.select();
    document.execCommand('copy');

    if (window.gpaCalculator) {
        window.gpaCalculator.showAlert('تم نسخ الرابط', 'success');
    }
}

function showAdminTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.admin-content').forEach(tab => {
        tab.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.admin-tab').forEach(btn => {
        btn.classList.remove('active', 'border-purple-500', 'text-purple-600');
        btn.classList.add('border-transparent', 'text-gray-500');
    });

    // Show selected tab
    document.getElementById(tabName + 'Tab').classList.remove('hidden');

    // Add active class to selected tab button
    const activeBtn = document.querySelector(`[onclick="showAdminTab('${tabName}')"]`);
    if (activeBtn) {
        activeBtn.classList.add('active', 'border-purple-500', 'text-purple-600');
        activeBtn.classList.remove('border-transparent', 'text-gray-500');
    }
}

function deleteStudent(index) {
    if (confirm('هل أنت متأكد من حذف بيانات هذا الطالب؟')) {
        if (window.gpaCalculator) {
            window.gpaCalculator.adminData.students.splice(index, 1);
            localStorage.setItem('gpaStudents', JSON.stringify(window.gpaCalculator.adminData.students));
            window.gpaCalculator.updateStudentsTable();
            window.gpaCalculator.updateAdminStats();
            window.gpaCalculator.showAlert('تم حذف بيانات الطالب', 'success');
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.gpaCalculator = new GPACalculator();
});
