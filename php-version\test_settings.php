<?php
/**
 * Test Settings Save/Load
 * اختبار حفظ وتحميل الإعدادات
 */

session_start();

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$results = [];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $results[] = "✅ اتصال قاعدة البيانات ناجح";
    
    // Test 1: Check if settings table exists
    $tables = $pdo->query("SHOW TABLES LIKE 'settings'")->fetchAll();
    if (count($tables) > 0) {
        $results[] = "✅ جدول الإعدادات موجود";
        
        // Test 2: Get current settings
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
        $settings = [];
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        $results[] = "✅ تم تحميل " . count($settings) . " إعداد";
        
        // Test 3: Try to insert/update a test setting
        $testKey = 'test_setting_' . time();
        $testValue = 'test_value_' . rand(1000, 9999);
        
        $stmt = $pdo->prepare("
            INSERT INTO settings (setting_key, setting_value, updated_by) 
            VALUES (?, ?, ?) 
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value), 
            updated_by = VALUES(updated_by)
        ");
        
        if ($stmt->execute([$testKey, $testValue, 1])) {
            $results[] = "✅ تم حفظ إعداد تجريبي بنجاح";
            
            // Test 4: Verify the setting was saved
            $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
            $stmt->execute([$testKey]);
            $savedValue = $stmt->fetchColumn();
            
            if ($savedValue === $testValue) {
                $results[] = "✅ تم التحقق من حفظ الإعداد بنجاح";
            } else {
                $results[] = "❌ فشل في التحقق من حفظ الإعداد";
            }
            
            // Clean up test setting
            $pdo->prepare("DELETE FROM settings WHERE setting_key = ?")->execute([$testKey]);
            $results[] = "🧹 تم حذف الإعداد التجريبي";
        } else {
            $results[] = "❌ فشل في حفظ الإعداد التجريبي";
        }
        
        // Test 5: Test checkbox handling
        $checkboxSettings = ['enable_registration', 'enable_sharing', 'maintenance_mode'];
        foreach ($checkboxSettings as $checkbox) {
            $stmt = $pdo->prepare("
                INSERT INTO settings (setting_key, setting_value, updated_by) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                updated_by = VALUES(updated_by)
            ");
            $stmt->execute([$checkbox, '1', 1]);
        }
        $results[] = "✅ تم اختبار إعدادات الـ checkboxes";
        
    } else {
        $results[] = "❌ جدول الإعدادات غير موجود";
    }
    
} catch (PDOException $e) {
    $results[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $results[] = "❌ خطأ عام: " . $e->getMessage();
}

// Test form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_save'])) {
    try {
        $testSettings = [
            'site_name' => $_POST['site_name'] ?? 'اختبار',
            'default_language' => $_POST['default_language'] ?? 'ar',
            'enable_registration' => isset($_POST['enable_registration']) ? '1' : '0'
        ];
        
        $savedCount = 0;
        foreach ($testSettings as $key => $value) {
            $stmt = $pdo->prepare("
                INSERT INTO settings (setting_key, setting_value, updated_by) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                updated_by = VALUES(updated_by)
            ");
            if ($stmt->execute([$key, $value, 1])) {
                $savedCount++;
            }
        }
        
        $results[] = "✅ تم حفظ $savedCount إعداد من النموذج";
        
    } catch (Exception $e) {
        $results[] = "❌ خطأ في حفظ إعدادات النموذج: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإعدادات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-4">
                <i class="fas fa-cog text-blue-600 ml-2"></i>
                اختبار نظام الإعدادات
            </h1>
            
            <div class="space-y-2">
                <?php foreach ($results as $result): ?>
                    <div class="p-3 rounded-lg <?php echo strpos($result, '✅') !== false ? 'bg-green-100 text-green-800' : (strpos($result, '❌') !== false ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'); ?>">
                        <?php echo htmlspecialchars($result); ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Test Form -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">اختبار حفظ الإعدادات</h2>
            
            <form method="POST" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم الموقع</label>
                    <input type="text" name="site_name" value="حاسبة المعدل التراكمي - اختبار" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اللغة الافتراضية</label>
                    <select name="default_language" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                    </select>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="enable_registration" value="1" checked
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200">
                        <span class="mr-2 text-sm text-gray-700">تفعيل تسجيل الطلاب</span>
                    </label>
                </div>
                
                <button type="submit" name="test_save" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    اختبار الحفظ
                </button>
            </form>
        </div>
        
        <!-- Current Settings Display -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">الإعدادات الحالية</h2>
            
            <?php
            try {
                $stmt = $pdo->query("SELECT setting_key, setting_value, updated_at FROM settings ORDER BY setting_key");
                $currentSettings = $stmt->fetchAll();
                
                if ($currentSettings) {
                    echo '<div class="overflow-x-auto">';
                    echo '<table class="w-full border border-gray-200">';
                    echo '<thead class="bg-gray-50">';
                    echo '<tr>';
                    echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">المفتاح</th>';
                    echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">القيمة</th>';
                    echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">آخر تحديث</th>';
                    echo '</tr>';
                    echo '</thead>';
                    echo '<tbody>';
                    
                    foreach ($currentSettings as $setting) {
                        echo '<tr class="border-t">';
                        echo '<td class="px-4 py-2 text-sm font-medium">' . htmlspecialchars($setting['setting_key']) . '</td>';
                        echo '<td class="px-4 py-2 text-sm">' . htmlspecialchars($setting['setting_value']) . '</td>';
                        echo '<td class="px-4 py-2 text-sm text-gray-600">' . htmlspecialchars($setting['updated_at']) . '</td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody>';
                    echo '</table>';
                    echo '</div>';
                } else {
                    echo '<p class="text-gray-500">لا توجد إعدادات محفوظة</p>';
                }
                
            } catch (Exception $e) {
                echo '<p class="text-red-600">خطأ في تحميل الإعدادات: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
            ?>
        </div>
        
        <!-- Navigation -->
        <div class="mt-6 flex gap-4">
            <a href="admin_settings.php" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-cog ml-2"></i>
                صفحة الإعدادات
            </a>
            
            <a href="admin_dashboard.php" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-tachometer-alt ml-2"></i>
                لوحة الإدارة
            </a>
            
            <button onclick="location.reload()" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-redo ml-2"></i>
                إعادة الاختبار
            </button>
        </div>
    </div>
</body>
</html>
