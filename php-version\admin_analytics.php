<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['admin_id']) || $_SESSION['admin_role'] !== 'admin') {
    header('Location: simple_admin_login.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

$error = '';

// Handle export request
if (isset($_GET['export']) && $_GET['export'] == '1') {
    try {
        // Collect all analytics data for export
        $exportData = [
            'report_date' => date('Y-m-d H:i:s'),
            'period' => 'آخر 30 يوم',
            'summary' => [
                'total_calculations' => $totalCalculations ?? 0,
                'monthly_calculations' => $monthlyCalculations ?? 0,
                'total_courses' => $totalCourses ?? 0,
                'active_courses' => $activeCourses ?? 0,
                'total_universities' => $totalUniversities ?? 0,
                'active_universities' => $activeUniversities ?? 0
            ],
            'daily_usage' => $dailyCalculations ?? [],
            'top_universities' => $topUniversities ?? [],
            'gpa_trends' => $gpatrends ?? [],
            'engagement_metrics' => $engagement ?? []
        ];

        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="analytics_report_' . date('Y-m-d') . '.json"');
        echo json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    } catch (Exception $e) {
        $error = 'خطأ في تصدير التقرير: ' . $e->getMessage();
    }
}

// Get analytics data
try {
    // Total calculations
    $stmt = $pdo->query("SELECT COUNT(*) as total_calculations FROM activity_logs WHERE action = 'gpa_calculated'");
    $totalCalculations = $stmt->fetch()['total_calculations'] ?? 0;

    // Calculations this month
    $stmt = $pdo->query("SELECT COUNT(*) as monthly_calculations FROM activity_logs WHERE action = 'gpa_calculated' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)");
    $monthlyCalculations = $stmt->fetch()['monthly_calculations'] ?? 0;

    // Daily calculations (last 7 days)
    $stmt = $pdo->query("
        SELECT DATE(created_at) as date, COUNT(*) as count 
        FROM activity_logs 
        WHERE action = 'gpa_calculated' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at) 
        ORDER BY date DESC
    ");
    $dailyCalculations = $stmt->fetchAll();

    // Most used universities
    $stmt = $pdo->query("
        SELECT u.name_ar, COUNT(*) as usage_count 
        FROM activity_logs al 
        LEFT JOIN universities u ON al.details LIKE CONCAT('%', u.id, '%')
        WHERE al.action = 'gpa_calculated' AND u.name_ar IS NOT NULL
        GROUP BY u.id 
        ORDER BY usage_count DESC 
        LIMIT 5
    ");
    $topUniversities = $stmt->fetchAll();

    // Average GPA trends
    $stmt = $pdo->query("
        SELECT DATE(created_at) as date, 
               AVG(CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(details, 'gpa:', -1), ',', 1) AS DECIMAL(3,2))) as avg_gpa
        FROM activity_logs 
        WHERE action = 'gpa_calculated' AND details LIKE '%gpa:%'
        AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at) 
        ORDER BY date DESC
        LIMIT 30
    ");
    $gpatrends = $stmt->fetchAll();

    // User engagement metrics
    $stmt = $pdo->query("
        SELECT 
            COUNT(DISTINCT DATE(created_at)) as active_days,
            COUNT(*) as total_actions,
            AVG(actions_per_day) as avg_actions_per_day
        FROM (
            SELECT DATE(created_at) as date, COUNT(*) as actions_per_day
            FROM activity_logs 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(created_at)
        ) daily_stats
    ");
    $engagement = $stmt->fetch();

    // Course statistics
    $stmt = $pdo->query("SELECT COUNT(*) as total_courses FROM courses");
    $totalCourses = $stmt->fetch()['total_courses'] ?? 0;

    $stmt = $pdo->query("SELECT COUNT(*) as active_courses FROM courses WHERE is_active = 1");
    $activeCourses = $stmt->fetch()['active_courses'] ?? 0;

    // University statistics
    $stmt = $pdo->query("SELECT COUNT(*) as total_universities FROM universities WHERE id != 'disabled'");
    $totalUniversities = $stmt->fetch()['total_universities'] ?? 0;

    $stmt = $pdo->query("SELECT COUNT(*) as active_universities FROM universities WHERE is_active = 1 AND id != 'disabled'");
    $activeUniversities = $stmt->fetch()['active_universities'] ?? 0;

} catch (PDOException $e) {
    $error = 'خطأ في تحميل الإحصائيات: ' . $e->getMessage();
    // Set default values
    $totalCalculations = $monthlyCalculations = $totalCourses = $activeCourses = $totalUniversities = $activeUniversities = 0;
    $dailyCalculations = $topUniversities = $gpatrends = [];
    $engagement = ['active_days' => 0, 'total_actions' => 0, 'avg_actions_per_day' => 0];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات الاستخدام - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-chart-bar text-purple-500 ml-3"></i>
                            إحصائيات الاستخدام
                        </h1>
                        <p class="text-gray-600 mt-1">تحليل شامل لاستخدام نظام حساب المعدل والاقتراحات الذكية</p>
                    </div>
                    <div class="flex space-x-3 space-x-reverse">
                        <button onclick="exportAnalytics()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-download ml-2"></i>
                            تصدير التقرير
                        </button>
                        <button onclick="refreshData()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-sync-alt ml-2"></i>
                            تحديث البيانات
                        </button>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="p-6">
                <!-- Error Message -->
                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <i class="fas fa-exclamation-circle ml-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Overview Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Total Calculations -->
                    <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">إجمالي الحسابات</p>
                                <p class="text-3xl font-bold"><?php echo number_format($totalCalculations); ?></p>
                            </div>
                            <div class="bg-blue-400 bg-opacity-30 rounded-full p-3">
                                <i class="fas fa-calculator text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <i class="fas fa-arrow-up text-green-300 ml-1"></i>
                            <span class="text-blue-100 text-sm">منذ بداية الخدمة</span>
                        </div>
                    </div>

                    <!-- Monthly Calculations -->
                    <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">حسابات هذا الشهر</p>
                                <p class="text-3xl font-bold"><?php echo number_format($monthlyCalculations); ?></p>
                            </div>
                            <div class="bg-green-400 bg-opacity-30 rounded-full p-3">
                                <i class="fas fa-chart-line text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <i class="fas fa-calendar text-green-300 ml-1"></i>
                            <span class="text-green-100 text-sm">آخر 30 يوم</span>
                        </div>
                    </div>

                    <!-- Total Courses -->
                    <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">إجمالي المواد</p>
                                <p class="text-3xl font-bold"><?php echo number_format($totalCourses); ?></p>
                            </div>
                            <div class="bg-purple-400 bg-opacity-30 rounded-full p-3">
                                <i class="fas fa-book text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <span class="text-purple-100 text-sm"><?php echo number_format($activeCourses); ?> مادة نشطة</span>
                        </div>
                    </div>

                    <!-- Total Universities -->
                    <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-orange-100 text-sm">إجمالي الجامعات</p>
                                <p class="text-3xl font-bold"><?php echo number_format($totalUniversities); ?></p>
                            </div>
                            <div class="bg-orange-400 bg-opacity-30 rounded-full p-3">
                                <i class="fas fa-university text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <span class="text-orange-100 text-sm"><?php echo number_format($activeUniversities); ?> جامعة نشطة</span>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Daily Usage Chart -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">
                            <i class="fas fa-chart-area text-blue-600 ml-2"></i>
                            الاستخدام اليومي (آخر 7 أيام)
                        </h3>
                        <canvas id="dailyUsageChart" height="300"></canvas>
                    </div>

                    <!-- Top Universities Chart -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-bold text-gray-800 mb-4">
                            <i class="fas fa-trophy text-yellow-600 ml-2"></i>
                            أكثر الجامعات استخداماً
                        </h3>
                        <canvas id="topUniversitiesChart" height="300"></canvas>
                    </div>
                </div>

                <!-- GPA Trends Chart -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">
                        <i class="fas fa-line-chart text-green-600 ml-2"></i>
                        اتجاهات المعدل التراكمي (آخر 30 يوم)
                    </h3>
                    <canvas id="gpaTrendsChart" height="200"></canvas>
                </div>

                <!-- Engagement Metrics -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-6">
                        <i class="fas fa-users text-indigo-600 ml-2"></i>
                        مقاييس التفاعل
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-3xl font-bold text-indigo-600"><?php echo $engagement['active_days'] ?? 0; ?></div>
                            <div class="text-sm text-gray-600 mt-1">أيام نشطة</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-3xl font-bold text-green-600"><?php echo number_format($engagement['total_actions'] ?? 0); ?></div>
                            <div class="text-sm text-gray-600 mt-1">إجمالي الإجراءات</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-3xl font-bold text-blue-600"><?php echo number_format($engagement['avg_actions_per_day'] ?? 0, 1); ?></div>
                            <div class="text-sm text-gray-600 mt-1">متوسط الإجراءات يومياً</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Daily Usage Chart
        const dailyData = <?php echo json_encode(array_reverse($dailyCalculations)); ?>;
        const dailyLabels = dailyData.map(item => {
            const date = new Date(item.date);
            return date.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' });
        });
        const dailyValues = dailyData.map(item => item.count);

        new Chart(document.getElementById('dailyUsageChart'), {
            type: 'line',
            data: {
                labels: dailyLabels,
                datasets: [{
                    label: 'عدد الحسابات',
                    data: dailyValues,
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        // Top Universities Chart
        const universityData = <?php echo json_encode($topUniversities); ?>;
        const universityLabels = universityData.map(item => item.name_ar || 'غير محدد');
        const universityValues = universityData.map(item => item.usage_count);

        new Chart(document.getElementById('topUniversitiesChart'), {
            type: 'doughnut',
            data: {
                labels: universityLabels,
                datasets: [{
                    data: universityValues,
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(139, 92, 246, 0.8)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // GPA Trends Chart
        const gpaData = <?php echo json_encode(array_reverse($gpatrends)); ?>;
        const gpaLabels = gpaData.map(item => {
            const date = new Date(item.date);
            return date.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' });
        });
        const gpaValues = gpaData.map(item => parseFloat(item.avg_gpa) || 0);

        new Chart(document.getElementById('gpaTrendsChart'), {
            type: 'line',
            data: {
                labels: gpaLabels,
                datasets: [{
                    label: 'متوسط المعدل',
                    data: gpaValues,
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 4,
                        ticks: {
                            stepSize: 0.5
                        }
                    }
                }
            }
        });

        function exportAnalytics() {
            window.open('admin_analytics.php?export=1', '_blank');
        }

        function refreshData() {
            location.reload();
        }
    </script>
</body>
</html>
