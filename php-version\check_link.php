<?php
/**
 * Check Share Link Debug
 * فحص رابط المشاركة
 */

require_once 'db_config.php';

$link_id = $_GET['link'] ?? '685756f2bdf67_1750554354';

echo "<h2>فحص رابط المشاركة: $link_id</h2>";

try {
    $db = getDB();
    
    // Check if students table exists
    echo "<h3>1. فحص وجود جدول students:</h3>";
    $tables = fetchAll("SHOW TABLES LIKE 'students'");
    if (empty($tables)) {
        echo "❌ جدول students غير موجود!<br>";
        exit;
    }
    echo "✅ جدول students موجود<br>";
    
    // Check table structure
    echo "<h3>2. هيكل جدول students:</h3>";
    $columns = fetchAll("DESCRIBE students");
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})<br>";
    }
    
    // Check all students
    echo "<h3>3. جميع الطلاب في قاعدة البيانات:</h3>";
    $students = fetchAll("SELECT id, name, phone, share_link, link_views, created_at FROM students LIMIT 10");
    if (empty($students)) {
        echo "❌ لا توجد بيانات طلاب!<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Phone</th><th>Share Link</th><th>Views</th><th>Created</th></tr>";
        foreach ($students as $student) {
            echo "<tr>";
            echo "<td>{$student['id']}</td>";
            echo "<td>{$student['name']}</td>";
            echo "<td>{$student['phone']}</td>";
            echo "<td>{$student['share_link']}</td>";
            echo "<td>{$student['link_views']}</td>";
            echo "<td>{$student['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Search for specific link
    echo "<h3>4. البحث عن الرابط المحدد:</h3>";
    $student = fetchOne("SELECT * FROM students WHERE share_link = ?", [$link_id]);
    if ($student) {
        echo "✅ تم العثور على الطالب:<br>";
        echo "<pre>" . print_r($student, true) . "</pre>";
        
        // Check courses
        echo "<h3>5. المواد الدراسية:</h3>";
        $courses = fetchAll("SELECT * FROM courses WHERE student_id = ?", [$student['id']]);
        if (empty($courses)) {
            echo "❌ لا توجد مواد دراسية لهذا الطالب<br>";
            
            // Check in student_courses table
            $courses2 = fetchAll("SELECT * FROM student_courses WHERE student_id = ?", [$student['id']]);
            if (!empty($courses2)) {
                echo "✅ وجدت مواد في جدول student_courses:<br>";
                echo "<pre>" . print_r($courses2, true) . "</pre>";
            }
        } else {
            echo "✅ المواد الدراسية:<br>";
            echo "<pre>" . print_r($courses, true) . "</pre>";
        }
    } else {
        echo "❌ لم يتم العثور على الطالب بهذا الرابط<br>";
        
        // Search with LIKE
        echo "<h3>البحث بـ LIKE:</h3>";
        $similar = fetchAll("SELECT share_link FROM students WHERE share_link LIKE ?", ["%$link_id%"]);
        if (!empty($similar)) {
            echo "روابط مشابهة:<br>";
            foreach ($similar as $s) {
                echo "- {$s['share_link']}<br>";
            }
        } else {
            echo "لا توجد روابط مشابهة<br>";
        }
    }
    
    // Check all share links
    echo "<h3>6. جميع روابط المشاركة:</h3>";
    $allLinks = fetchAll("SELECT id, name, share_link FROM students WHERE share_link IS NOT NULL AND share_link != ''");
    if (!empty($allLinks)) {
        foreach ($allLinks as $link) {
            echo "- ID: {$link['id']}, Name: {$link['name']}, Link: {$link['share_link']}<br>";
        }
    } else {
        echo "لا توجد روابط مشاركة<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background-color: #f8f8f8; padding: 10px; border-radius: 4px; }
</style>
