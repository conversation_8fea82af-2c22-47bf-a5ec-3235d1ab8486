<?php
// Add a test notification

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Clear existing notification views for testing
    $pdo->exec("DELETE FROM notification_views");
    echo "Cleared existing notification views<br>";
    
    // Add a test popup notification
    $stmt = $pdo->prepare("
        INSERT INTO notifications (
            title_ar, title_en, message_ar, message_en, type, display_type, 
            target_audience, priority, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        'إشعار تجريبي منبثق',
        'Test Popup Notification',
        'هذا إشعار تجريبي منبثق للتأكد من عمل النظام بشكل صحيح.',
        'This is a test popup notification to ensure the system works correctly.',
        'info',
        'popup',
        'all',
        1,
        1
    ]);
    
    echo "Added test popup notification<br>";
    
    // Add a test inline notification
    $stmt->execute([
        'إشعار تجريبي مدمج',
        'Test Inline Notification',
        'هذا إشعار تجريبي مدمج يظهر في الجانب الأيمن من الصفحة.',
        'This is a test inline notification that appears on the right side of the page.',
        'success',
        'inline',
        'all',
        2,
        1
    ]);
    
    echo "Added test inline notification<br>";
    
    // Check total notifications
    $stmt = $pdo->query("SELECT COUNT(*) FROM notifications WHERE is_active = 1");
    $count = $stmt->fetchColumn();
    echo "Total active notifications: $count<br>";
    
    echo "<br><a href='get_notifications.php'>Test get_notifications.php</a><br>";
    echo "<a href='index.php'>Go to main page</a><br>";
    echo "<a href='test_notifications.php'>Test notifications system</a>";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
