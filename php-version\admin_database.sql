-- قاعدة بيانات لوحة الإدارة - حاسبة المعدل التراكمي
-- Admin Panel Database - GPA Calculator

-- جدول المديرين
CREATE TABLE IF NOT EXISTS admins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    avatar VARCHAR(255) NULL,
    phone VARCHAR(20) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ج<PERSON><PERSON><PERSON> الطلاب المحفوظين
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100) NULL,
    student_id VARCHAR(50) NULL,
    university VARCHAR(50) NOT NULL DEFAULT 'aou',
    grading_system VARCHAR(50) NOT NULL DEFAULT 'aou',
    semester_gpa DECIMAL(3,2) NULL,
    cumulative_gpa DECIMAL(3,2) NULL,
    total_hours INT DEFAULT 0,
    previous_gpa DECIMAL(3,2) NULL,
    previous_hours INT DEFAULT 0,
    classification VARCHAR(50) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    share_link VARCHAR(100) UNIQUE NULL,
    link_expires_at TIMESTAMP NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_university (university),
    INDEX idx_created_at (created_at),
    INDEX idx_share_link (share_link),
    INDEX idx_gpa (cumulative_gpa)
);

-- جدول المواد الدراسية
CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_name VARCHAR(100) NOT NULL,
    course_code VARCHAR(20) NULL,
    credit_hours INT NOT NULL,
    grade VARCHAR(5) NOT NULL,
    grade_points DECIMAL(3,2) NOT NULL,
    semester VARCHAR(20) NULL,
    year VARCHAR(10) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    INDEX idx_student_id (student_id),
    INDEX idx_grade (grade)
);

-- جدول سجل العمليات
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NULL,
    student_id INT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT NULL,
    old_data JSON NULL,
    new_data JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE SET NULL,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE SET NULL,
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_admin_id (admin_id)
);

-- جدول الإعدادات
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NULL,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    description TEXT NULL,
    category VARCHAR(50) DEFAULT 'general',
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_category (category)
);

-- جدول الإحصائيات اليومية
CREATE TABLE IF NOT EXISTS daily_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE UNIQUE NOT NULL,
    total_calculations INT DEFAULT 0,
    unique_users INT DEFAULT 0,
    avg_gpa DECIMAL(3,2) NULL,
    total_students INT DEFAULT 0,
    new_students INT DEFAULT 0,
    universities_data JSON NULL,
    grades_distribution JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_date (date)
);

-- جدول التقارير المحفوظة
CREATE TABLE IF NOT EXISTS saved_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    report_type VARCHAR(50) NOT NULL,
    filters JSON NULL,
    created_by INT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_type (report_type),
    INDEX idx_created_by (created_by)
);

-- جدول الإشعارات
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_admin_read (admin_id, is_read),
    INDEX idx_created_at (created_at)
);

-- إدراج بيانات المدير الافتراضي
-- كلمة المرور: admin123
INSERT INTO admins (username, email, password, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام الرئيسي', 'super_admin'),
('moderator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مشرف النظام', 'moderator'),
('viewer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مراقب النظام', 'admin')
ON DUPLICATE KEY UPDATE username=username;

-- إدراج الإعدادات الافتراضية
INSERT INTO settings (setting_key, setting_value, setting_type, description, category, is_public) VALUES 
('site_name', 'حاسبة المعدل التراكمي - الجامعة العربية المفتوحة', 'text', 'اسم الموقع', 'general', TRUE),
('site_description', 'نظام متطور لحساب المعدل التراكمي للطلاب', 'text', 'وصف الموقع', 'general', TRUE),
('admin_email', '<EMAIL>', 'text', 'البريد الإلكتروني للمدير', 'general', FALSE),
('default_language', 'ar', 'text', 'اللغة الافتراضية', 'general', TRUE),
('default_university', 'aou', 'text', 'الجامعة الافتراضية', 'general', TRUE),
('max_courses_per_calculation', '20', 'number', 'الحد الأقصى للمواد في الحساب الواحد', 'limits', TRUE),
('link_expiry_days', '30', 'number', 'مدة انتهاء صلاحية الروابط بالأيام', 'security', TRUE),
('enable_registration', '1', 'boolean', 'تفعيل التسجيل للطلاب', 'features', TRUE),
('enable_sharing', '1', 'boolean', 'تفعيل مشاركة النتائج', 'features', TRUE),
('maintenance_mode', '0', 'boolean', 'وضع الصيانة', 'system', FALSE),
('max_login_attempts', '5', 'number', 'الحد الأقصى لمحاولات تسجيل الدخول', 'security', FALSE),
('lockout_duration', '30', 'number', 'مدة الحظر بالدقائق', 'security', FALSE),
('session_timeout', '120', 'number', 'انتهاء صلاحية الجلسة بالدقائق', 'security', FALSE),
('backup_frequency', 'daily', 'text', 'تكرار النسخ الاحتياطي', 'system', FALSE),
('email_notifications', '1', 'boolean', 'تفعيل الإشعارات بالبريد الإلكتروني', 'notifications', FALSE)
ON DUPLICATE KEY UPDATE setting_key=setting_key;

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_students_gpa_university ON students(cumulative_gpa, university);
CREATE INDEX idx_students_date_university ON students(created_at, university);
CREATE INDEX idx_courses_grade_points ON courses(grade_points);
CREATE INDEX idx_activity_logs_admin_action ON activity_logs(admin_id, action);
CREATE INDEX idx_settings_category_public ON settings(category, is_public);

-- إنشاء views للتقارير السريعة
CREATE OR REPLACE VIEW admin_dashboard_stats AS
SELECT 
    (SELECT COUNT(*) FROM students) as total_students,
    (SELECT COUNT(*) FROM students WHERE DATE(created_at) = CURDATE()) as today_students,
    (SELECT COUNT(*) FROM students WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) as week_students,
    (SELECT COUNT(*) FROM students WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as month_students,
    (SELECT ROUND(AVG(cumulative_gpa), 2) FROM students WHERE cumulative_gpa IS NOT NULL) as avg_gpa,
    (SELECT COUNT(DISTINCT university) FROM students) as total_universities,
    (SELECT COUNT(*) FROM courses) as total_courses,
    (SELECT COUNT(*) FROM activity_logs WHERE DATE(created_at) = CURDATE()) as today_activities;

CREATE OR REPLACE VIEW university_stats AS
SELECT 
    university,
    COUNT(*) as student_count,
    ROUND(AVG(cumulative_gpa), 2) as avg_gpa,
    MAX(cumulative_gpa) as max_gpa,
    MIN(cumulative_gpa) as min_gpa,
    SUM(CASE WHEN cumulative_gpa >= 3.75 THEN 1 ELSE 0 END) as excellent_count,
    SUM(CASE WHEN cumulative_gpa >= 3.25 AND cumulative_gpa < 3.75 THEN 1 ELSE 0 END) as very_good_high_count,
    SUM(CASE WHEN cumulative_gpa >= 2.75 AND cumulative_gpa < 3.25 THEN 1 ELSE 0 END) as very_good_count,
    SUM(CASE WHEN cumulative_gpa >= 2.25 AND cumulative_gpa < 2.75 THEN 1 ELSE 0 END) as good_high_count,
    SUM(CASE WHEN cumulative_gpa >= 2.0 AND cumulative_gpa < 2.25 THEN 1 ELSE 0 END) as good_count,
    SUM(CASE WHEN cumulative_gpa < 2.0 THEN 1 ELSE 0 END) as below_good_count,
    DATE(MAX(created_at)) as last_activity
FROM students 
WHERE cumulative_gpa IS NOT NULL
GROUP BY university
ORDER BY student_count DESC;

CREATE OR REPLACE VIEW recent_activities AS
SELECT 
    al.id,
    al.action,
    al.description,
    al.created_at,
    a.full_name as admin_name,
    s.name as student_name,
    al.ip_address
FROM activity_logs al
LEFT JOIN admins a ON al.admin_id = a.id
LEFT JOIN students s ON al.student_id = s.id
ORDER BY al.created_at DESC
LIMIT 50;

-- Stored procedures and triggers removed for compatibility

-- بيانات تجريبية للاختبار
INSERT INTO students (name, phone, university, cumulative_gpa, total_hours, classification, ip_address) VALUES
('أحمد محمد علي', '0501234567', 'aou', 3.85, 45, 'ممتاز', '*************'),
('فاطمة أحمد', '0507654321', 'aou', 3.45, 60, 'جيد جداً مرتفع', '*************'),
('محمد سالم', '0509876543', 'ksu', 2.95, 75, 'جيد جداً', '*************'),
('نورا عبدالله', '0502468135', 'aou', 3.95, 30, 'ممتاز', '*************'),
('خالد أحمد', '0501357924', 'kau', 2.25, 90, 'جيد مرتفع', '*************');

-- كلمة المرور الافتراضية للجميع: admin123
