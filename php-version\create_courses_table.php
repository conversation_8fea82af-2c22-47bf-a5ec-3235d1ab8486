<?php
// Create course catalog table and insert sample data

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Creating course catalog table...\n";
    
    // Create course catalog table
    $sql = "CREATE TABLE IF NOT EXISTS course_catalog (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_code VARCHAR(20) NOT NULL,
        course_title_ar VARCHAR(255) NOT NULL,
        course_title_en VARCHAR(255) NOT NULL,
        prerequisites TEXT,
        credit_hours INT NOT NULL DEFAULT 3,
        course_description_ar TEXT,
        course_description_en TEXT,
        course_objectives_ar TEXT,
        course_objectives_en TEXT,
        course_outcomes_ar TEXT,
        course_outcomes_en TEXT,
        university_id VARCHAR(50),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_course_code (course_code, university_id),
        INDEX idx_course_code (course_code),
        INDEX idx_university (university_id),
        INDEX idx_active (is_active)
    )";
    
    $pdo->exec($sql);
    echo "Course catalog table created successfully!\n";
    
    // Check if data already exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM course_catalog");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "Inserting sample course data...\n";
        
        // Insert sample courses for AOU
        $courses = [
            [
                'course_code' => 'B207-A',
                'course_title_ar' => 'تشكيل الفرص التجارية',
                'course_title_en' => 'Shaping Business Opportunities',
                'prerequisites' => 'BUS110',
                'credit_hours' => 8,
                'course_description_ar' => 'B207A هي مادة 8 ساعات معتمدة (30 نقطة)، المستوى 5 UK-OU مقدمة من خلال برنامج إدارة الأعمال في الجامعة العربية المفتوحة كمادة إجبارية لجميع الطلاب المسجلين في جميع المسارات في البرنامج.',
                'course_description_en' => 'B207A is an 8-credit (30 points), Level 5 UK-OU based course offered through the Business Program at the Arab Open University as a compulsory course for all students enrolled in all tracks in the program.',
                'course_objectives_ar' => 'تم تصميم هذه الوحدة لتوفير التعلم المفاهيمي والعملي المتوسط للطلاب في إدارة العمليات والتسويق وإدارة الموارد البشرية.',
                'course_objectives_en' => 'This module is designed to provide intermediate conceptual and practical learning to students in operations management, marketing and human resource management.',
                'course_outcomes_ar' => 'تطوير تقدير نقدي للتفاعلات بين وظائف الأعمال المختلفة والتعقيد التكاملي الذي يشكل الابتكار التجاري.',
                'course_outcomes_en' => 'Develop a critical appreciation of the interactions between various business functions and the integrative complexity that shapes business innovation.',
                'university_id' => 'aou'
            ],
            [
                'course_code' => 'BUS110',
                'course_title_ar' => 'مقدمة في إدارة الأعمال',
                'course_title_en' => 'Introduction to Business',
                'prerequisites' => null,
                'credit_hours' => 6,
                'course_description_ar' => 'مقدمة شاملة في مبادئ إدارة الأعمال والمفاهيم الأساسية',
                'course_description_en' => 'Comprehensive introduction to business management principles and fundamental concepts',
                'course_objectives_ar' => 'تعريف الطلاب بأساسيات إدارة الأعمال',
                'course_objectives_en' => 'Introduce students to business management fundamentals',
                'course_outcomes_ar' => 'فهم المبادئ الأساسية لإدارة الأعمال',
                'course_outcomes_en' => 'Understanding fundamental principles of business management',
                'university_id' => 'aou'
            ],
            [
                'course_code' => 'M150',
                'course_title_ar' => 'الرياضيات التطبيقية',
                'course_title_en' => 'Applied Mathematics',
                'prerequisites' => null,
                'credit_hours' => 6,
                'course_description_ar' => 'مقدمة في الرياضيات التطبيقية للأعمال',
                'course_description_en' => 'Introduction to applied mathematics for business',
                'course_objectives_ar' => 'تطوير المهارات الرياضية للطلاب',
                'course_objectives_en' => 'Develop mathematical skills for students',
                'course_outcomes_ar' => 'إتقان المفاهيم الرياضية الأساسية',
                'course_outcomes_en' => 'Master basic mathematical concepts',
                'university_id' => 'aou'
            ],
            [
                'course_code' => 'T175',
                'course_title_ar' => 'الشبكات في العمل',
                'course_title_en' => 'Networked Living',
                'prerequisites' => null,
                'credit_hours' => 6,
                'course_description_ar' => 'مقدمة في تكنولوجيا المعلومات والشبكات',
                'course_description_en' => 'Introduction to information technology and networks',
                'course_objectives_ar' => 'فهم أساسيات تكنولوجيا المعلومات',
                'course_objectives_en' => 'Understanding IT fundamentals',
                'course_outcomes_ar' => 'تطبيق المعرفة التقنية في بيئة العمل',
                'course_outcomes_en' => 'Apply technical knowledge in work environment',
                'university_id' => 'aou'
            ],
            [
                'course_code' => 'EL111',
                'course_title_ar' => 'اللغة الإنجليزية للأغراض الأكاديمية',
                'course_title_en' => 'English for Academic Purposes',
                'prerequisites' => null,
                'credit_hours' => 6,
                'course_description_ar' => 'تطوير مهارات اللغة الإنجليزية الأكاديمية',
                'course_description_en' => 'Develop academic English language skills',
                'course_objectives_ar' => 'تحسين مهارات القراءة والكتابة',
                'course_objectives_en' => 'Improve reading and writing skills',
                'course_outcomes_ar' => 'إتقان اللغة الإنجليزية الأكاديمية',
                'course_outcomes_en' => 'Master academic English language',
                'university_id' => 'aou'
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO course_catalog (
                course_code, course_title_ar, course_title_en, prerequisites, 
                credit_hours, course_description_ar, course_description_en, 
                course_objectives_ar, course_objectives_en, course_outcomes_ar, 
                course_outcomes_en, university_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($courses as $course) {
            $stmt->execute([
                $course['course_code'],
                $course['course_title_ar'],
                $course['course_title_en'],
                $course['prerequisites'],
                $course['credit_hours'],
                $course['course_description_ar'],
                $course['course_description_en'],
                $course['course_objectives_ar'],
                $course['course_objectives_en'],
                $course['course_outcomes_ar'],
                $course['course_outcomes_en'],
                $course['university_id']
            ]);
        }
        
        echo "Sample course data inserted successfully!\n";
    } else {
        echo "Course data already exists. Skipping insertion.\n";
    }
    
    echo "Setup completed successfully!\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
