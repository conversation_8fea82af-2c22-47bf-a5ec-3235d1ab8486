<?php
session_start();

// Log the logout activity if user was logged in
if (isset($_SESSION['admin_id']) && $_SESSION['admin_role'] === 'publisher') {
    try {
        // Database configuration
        $host = 'localhost';
        $database = 'gpa_calculator';
        $username = 'root';
        $password = '';
        
        $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Log the logout activity
        $stmt = $pdo->prepare("INSERT INTO activity_logs (admin_id, action, description, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([
            $_SESSION['admin_id'],
            'logout',
            'تسجيل خروج ناشر المواد',
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // Ignore logging errors during logout
    }
}

// Clear all session data
session_unset();
session_destroy();

// Start a new session for the success message
session_start();
$_SESSION['logout_success'] = true;

// Redirect to publisher login page
header('Location: publisher_login.php');
exit;
?>
