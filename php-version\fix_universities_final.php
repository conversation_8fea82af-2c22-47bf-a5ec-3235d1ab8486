<?php
/**
 * Final Fix for Universities Table
 * إصلاح نهائي لجدول الجامعات
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$fixes = [];
$errors = [];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $fixes[] = "✅ اتصال قاعدة البيانات ناجح";
    
    // Disable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    $fixes[] = "✅ تم تعطيل فحص المفاتيح الخارجية مؤقتاً";
    
    // Check if universities table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'universities'");
    if ($stmt->rowCount() > 0) {
        // Table exists, check structure
        $stmt = $pdo->query("DESCRIBE universities");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $fixes[] = "✅ جدول universities موجود، الأعمدة: " . implode(', ', $columns);
        
        // Drop and recreate table to ensure correct structure
        $pdo->exec("DROP TABLE universities");
        $fixes[] = "✅ تم حذف جدول universities القديم";
    }
    
    // Create universities table with correct structure
    $pdo->exec("
        CREATE TABLE universities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(10) UNIQUE NOT NULL,
            name_ar VARCHAR(100) NOT NULL,
            name_en VARCHAR(100) NOT NULL,
            grading_system JSON NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_code (code),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $fixes[] = "✅ تم إنشاء جدول universities بالهيكل الصحيح";
    
    // Insert default universities
    $defaultUniversities = [
        ['aou', 'الجامعة العربية المفتوحة', 'Arab Open University', '{"A": 4.0, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D": 1.0, "F": 0.0}'],
        ['ksu', 'جامعة الملك سعود', 'King Saud University', '{"A+": 4.0, "A": 3.75, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D+": 1.5, "D": 1.0, "F": 0.0}'],
        ['kau', 'جامعة الملك عبدالعزيز', 'King Abdulaziz University', '{"A+": 4.0, "A": 3.75, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D+": 1.5, "D": 1.0, "F": 0.0}'],
        ['kfupm', 'جامعة الملك فهد للبترول والمعادن', 'King Fahd University of Petroleum and Minerals', '{"A": 4.0, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D+": 1.5, "D": 1.0, "F": 0.0}'],
        ['imamu', 'جامعة الإمام محمد بن سعود الإسلامية', 'Imam Mohammad Ibn Saud Islamic University', '{"A+": 4.0, "A": 3.75, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D+": 1.5, "D": 1.0, "F": 0.0}']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO universities (code, name_ar, name_en, grading_system) VALUES (?, ?, ?, ?)");
    $insertedCount = 0;
    foreach ($defaultUniversities as $uni) {
        if ($stmt->execute($uni)) {
            $insertedCount++;
        }
    }
    $fixes[] = "✅ تم إدراج $insertedCount جامعة افتراضية";
    
    // Test the query that was failing
    try {
        $testQuery = $pdo->query("
            SELECT u.*, 
                   COALESCE((SELECT COUNT(*) FROM students s WHERE s.university = u.code), 0) as student_count
            FROM universities u 
            ORDER BY u.name_ar
        ");
        $universities = $testQuery->fetchAll();
        $fixes[] = "✅ تم اختبار استعلام الجامعات بنجاح - عدد الجامعات: " . count($universities);
    } catch (PDOException $e) {
        $errors[] = "❌ فشل في اختبار استعلام الجامعات: " . $e->getMessage();
    }
    
    // Test student insertion with university reference
    $testStudentData = [
        'طالب اختبار جامعة ' . date('H:i:s'),
        '050' . rand(1000000, 9999999),
        'aou', // university code
        'aou', // grading system
        3.75,
        3.75,
        15,
        0,
        0,
        'ممتاز',
        '127.0.0.1',
        'Test Browser',
        uniqid() . '_' . time(),
        date('Y-m-d H:i:s', strtotime('+30 days'))
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO students (name, phone, university, grading_system, semester_gpa, cumulative_gpa, 
                            total_hours, previous_gpa, previous_hours, classification, ip_address, 
                            user_agent, share_link, link_expires_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    if ($stmt->execute($testStudentData)) {
        $studentId = $pdo->lastInsertId();
        $fixes[] = "✅ تم اختبار إدراج طالب مع مرجع الجامعة بنجاح (ID: $studentId)";
        
        // Test the join query
        try {
            $joinTest = $pdo->query("
                SELECT s.name, u.name_ar as university_name 
                FROM students s 
                JOIN universities u ON s.university = u.code 
                WHERE s.id = $studentId
            ");
            $result = $joinTest->fetch();
            if ($result) {
                $fixes[] = "✅ تم اختبار ربط الطالب بالجامعة: " . $result['name'] . " - " . $result['university_name'];
            }
        } catch (PDOException $e) {
            $errors[] = "❌ فشل في اختبار ربط الطالب بالجامعة: " . $e->getMessage();
        }
    } else {
        $errors[] = "❌ فشل في اختبار إدراج طالب مع مرجع الجامعة";
    }
    
    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    $fixes[] = "✅ تم إعادة تفعيل فحص المفاتيح الخارجية";
    
} catch (PDOException $e) {
    $errors[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح نهائي لجدول الجامعات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-university text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">إصلاح نهائي لجدول الجامعات</h1>
                <p class="text-gray-600">حل مشكلة عمود code في جدول universities</p>
            </div>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Fixes -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-green-700 mb-4">
                    <i class="fas fa-check-circle ml-2"></i>
                    الإصلاحات المكتملة (<?php echo count($fixes); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php foreach ($fixes as $index => $fix): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg text-sm" 
                             style="animation: slideIn 0.5s ease-out <?php echo $index * 0.1; ?>s both;">
                            <?php echo htmlspecialchars($fix); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Errors -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-red-700 mb-4">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    المشاكل المتبقية (<?php echo count($errors); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($errors)): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-thumbs-up ml-2"></i>
                                <span>تم إصلاح جميع المشاكل بنجاح!</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($errors as $error): ?>
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg text-sm">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Universities Table Structure -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">هيكل جدول الجامعات الجديد</h2>
            
            <?php
            try {
                $stmt = $pdo->query("DESCRIBE universities");
                $columns = $stmt->fetchAll();
                
                echo '<div class="overflow-x-auto">';
                echo '<table class="w-full border border-gray-200">';
                echo '<thead class="bg-gray-50">';
                echo '<tr>';
                echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">اسم العمود</th>';
                echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">النوع</th>';
                echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">Null</th>';
                echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">Key</th>';
                echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">Default</th>';
                echo '</tr>';
                echo '</thead>';
                echo '<tbody>';
                
                foreach ($columns as $column) {
                    echo '<tr class="border-t">';
                    echo '<td class="px-4 py-2 text-sm font-medium">' . htmlspecialchars($column['Field']) . '</td>';
                    echo '<td class="px-4 py-2 text-sm">' . htmlspecialchars($column['Type']) . '</td>';
                    echo '<td class="px-4 py-2 text-sm">' . htmlspecialchars($column['Null']) . '</td>';
                    echo '<td class="px-4 py-2 text-sm">' . htmlspecialchars($column['Key']) . '</td>';
                    echo '<td class="px-4 py-2 text-sm">' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
                    echo '</tr>';
                }
                
                echo '</tbody>';
                echo '</table>';
                echo '</div>';
                
            } catch (Exception $e) {
                echo '<p class="text-red-600">خطأ في عرض هيكل الجدول: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
            ?>
        </div>

        <!-- Current Universities -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">الجامعات المتاحة</h2>
            
            <?php
            try {
                $stmt = $pdo->query("SELECT * FROM universities ORDER BY name_ar");
                $universities = $stmt->fetchAll();
                
                if ($universities) {
                    echo '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';
                    foreach ($universities as $uni) {
                        echo '<div class="border rounded-lg p-4">';
                        echo '<h3 class="font-semibold text-gray-800">' . htmlspecialchars($uni['name_ar']) . '</h3>';
                        echo '<p class="text-sm text-gray-600">' . htmlspecialchars($uni['name_en']) . '</p>';
                        echo '<p class="text-xs text-blue-600 mt-2">الرمز: ' . htmlspecialchars($uni['code']) . '</p>';
                        echo '<span class="inline-block mt-2 px-2 py-1 text-xs rounded-full ' . 
                             ($uni['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') . '">';
                        echo $uni['is_active'] ? 'نشط' : 'غير نشط';
                        echo '</span>';
                        echo '</div>';
                    }
                    echo '</div>';
                } else {
                    echo '<p class="text-gray-500 text-center py-8">لا توجد جامعات</p>';
                }
                
            } catch (Exception $e) {
                echo '<p class="text-red-600">خطأ في تحميل الجامعات: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
            ?>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
            <?php if (empty($errors)): ?>
                <a href="admin_universities.php" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center">
                    <i class="fas fa-university ml-2"></i>
                    إدارة الجامعات
                </a>
                
                <a href="test_student_save.php" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center">
                    <i class="fas fa-test-tube ml-2"></i>
                    اختبار حفظ الطلاب
                </a>
                
                <a href="index.php" class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center">
                    <i class="fas fa-home ml-2"></i>
                    الصفحة الرئيسية
                </a>
            <?php else: ?>
                <button onclick="location.reload()" class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة المحاولة
                </button>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="mt-8 text-center text-sm text-gray-500">
            <p>تم إصلاح جدول الجامعات في: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p class="mt-1">الآن يمكن استخدام جميع وظائف الجامعات بدون مشاكل!</p>
        </div>
    </div>

    <style>
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>

    <script>
        // Show success message
        <?php if (empty($errors)): ?>
        setTimeout(() => {
            alert('تم إصلاح جدول الجامعات بنجاح!\nجميع الاستعلامات والوظائف تعمل الآن بشكل صحيح.');
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
