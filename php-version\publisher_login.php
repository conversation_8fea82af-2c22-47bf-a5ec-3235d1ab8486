<?php
session_start();

// Database configuration
$host = 'localhost';
$database = 'gpa_calculator';
$username = 'root';
$password = '';

$error = '';
$success = '';

// Check for logout success message
if (isset($_SESSION['logout_success'])) {
    $success = 'تم تسجيل الخروج بنجاح';
    unset($_SESSION['logout_success']);
}

// Redirect if already logged in as publisher
if (isset($_SESSION['admin_id']) && $_SESSION['admin_role'] === 'publisher') {
    header('Location: publisher_dashboard.php');
    exit;
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $loginUsername = trim($_POST['username'] ?? '');
    $loginPassword = $_POST['password'] ?? '';
    
    if (empty($loginUsername) || empty($loginPassword)) {
        $error = 'يرجى ملء جميع الحقول';
    } else {
        try {
            // Connect to database
            $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Get user first
            $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? AND is_active = 1");
            $stmt->execute([$loginUsername]);
            $admin = $stmt->fetch();

            // Check if user exists, password is correct, and role is publisher
            if ($admin && password_verify($loginPassword, $admin['password']) && $admin['role'] === 'publisher') {
                // Login successful
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_role'] = $admin['role'];
                $_SESSION['admin_name'] = $admin['full_name'];
                $_SESSION['last_activity'] = time();
                
                // Update last login
                $updateStmt = $pdo->prepare("UPDATE admins SET last_login = NOW() WHERE id = ?");
                $updateStmt->execute([$admin['id']]);
                
                // Log activity
                try {
                    $logStmt = $pdo->prepare("INSERT INTO activity_logs (admin_id, action, description, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())");
                    $logStmt->execute([
                        $admin['id'], 
                        'login', 
                        'تسجيل دخول ناشر المواد', 
                        $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                    ]);
                } catch (Exception $e) {
                    // Log error but don't fail login
                }
                
                header('Location: publisher_dashboard.php');
                exit;
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة أو ليس لديك صلاحية ناشر';
                
                // Log failed attempt
                try {
                    $logStmt = $pdo->prepare("INSERT INTO activity_logs (admin_id, action, description, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())");
                    $logStmt->execute([
                        null,
                        'failed_login',
                        "محاولة تسجيل دخول فاشلة للناشر: $loginUsername",
                        $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                    ]);
                } catch (Exception $e) {
                    // Ignore logging errors
                }
            }
        } catch (PDOException $e) {
            $error = 'خطأ في الاتصال بقاعدة البيانات';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول ناشر المواد - حاسبة المعدل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-blue-50 to-purple-100 min-h-screen">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <div class="mx-auto h-20 w-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-6">
                    <i class="fas fa-book text-white text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-2">ناشر المواد</h2>
                <p class="text-gray-600">تسجيل دخول لنشر وإدارة المواد الدراسية</p>
            </div>

            <!-- Login Form -->
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <?php if ($error): ?>
                    <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle ml-2"></i>
                            <span><?php echo htmlspecialchars($error); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle ml-2"></i>
                            <span><?php echo htmlspecialchars($success); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <form method="POST" class="space-y-6">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user ml-2"></i>
                            اسم المستخدم أو البريد الإلكتروني
                        </label>
                        <input type="text" id="username" name="username" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                               placeholder="أدخل اسم المستخدم"
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock ml-2"></i>
                            كلمة المرور
                        </label>
                        <div class="relative">
                            <input type="password" id="password" name="password" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                   placeholder="أدخل كلمة المرور">
                            <button type="button" onclick="togglePassword()" 
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <button type="submit" 
                                class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-sign-in-alt ml-2"></i>
                            تسجيل الدخول
                        </button>
                    </div>
                </form>

                <!-- Demo Credentials -->
                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">بيانات تجريبية:</h4>
                    <div class="text-xs text-gray-600 space-y-1">
                        <div>اسم المستخدم: <code class="bg-gray-200 px-1 rounded">publisher</code></div>
                        <div>كلمة المرور: <code class="bg-gray-200 px-1 rounded">publisher123</code></div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center">
                <p class="text-sm text-gray-600">
                    هل أنت مشرف؟ 
                    <a href="simple_admin_login.php" class="text-blue-600 hover:text-blue-800 font-medium">
                        تسجيل دخول المشرف
                    </a>
                </p>
                <p class="text-xs text-gray-500 mt-2">
                    <a href="index.php" class="hover:text-gray-700">
                        <i class="fas fa-arrow-right ml-1"></i>
                        العودة للموقع الرئيسي
                    </a>
                </p>
            </div>
        </div>
    </div>

    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Auto-fill demo credentials when clicking on them
        document.addEventListener('DOMContentLoaded', function() {
            const demoSection = document.querySelector('.bg-gray-50');
            if (demoSection) {
                demoSection.style.cursor = 'pointer';
                demoSection.addEventListener('click', function() {
                    document.getElementById('username').value = 'publisher';
                    document.getElementById('password').value = 'publisher123';
                });
            }
        });
    </script>
</body>
</html>
