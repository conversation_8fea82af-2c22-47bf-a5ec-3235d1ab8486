<?php
/**
 * Fix Universities Table
 * إصلاح جدول الجامعات
 */

require_once 'db_config.php';

try {
    $db = getDB();
    
    echo "<h2>إصلاح جدول الجامعات</h2>";
    
    // Check if universities table exists
    $universitiesExist = fetchOne("SHOW TABLES LIKE 'universities'");
    
    if (!$universitiesExist) {
        echo "إنشاء جدول الجامعات...<br>";
        
        // Create universities table
        executeQuery("
            CREATE TABLE universities (
                id VARCHAR(50) PRIMARY KEY,
                name_ar VARCHAR(255) NOT NULL,
                name_en VARCHAR(255) NOT NULL,
                logo VARCHAR(10) DEFAULT '🏛️',
                country_ar VARCHAR(100) DEFAULT 'السعودية',
                country_en VARCHAR(100) DEFAULT 'Saudi Arabia',
                established INT DEFAULT 2000,
                students VARCHAR(20) DEFAULT '1000+',
                grading_system VARCHAR(50) DEFAULT 'standard',
                website VARCHAR(255),
                description_ar TEXT,
                description_en TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        echo "✅ تم إنشاء جدول الجامعات<br>";
    } else {
        echo "✅ جدول الجامعات موجود<br>";
    }
    
    // Check if universities table has data
    $universityCount = fetchOne("SELECT COUNT(*) as count FROM universities")['count'];
    echo "عدد الجامعات الحالي: $universityCount<br>";
    
    if ($universityCount == 0) {
        echo "إضافة جامعات افتراضية...<br>";
        
        // Insert default universities
        $universities = [
            ['aou', 'الجامعة العربية المفتوحة', 'Arab Open University', '🏛️', 'السعودية', 'Saudi Arabia', 2002, '40,000+', 'aou', 'https://www.aou.edu.sa', 'مؤسسة تعليمية رائدة تأسست عام 2002 وتقدم برامج أكاديمية متميزة', 'Leading educational institution established in 2002 offering distinguished academic programs'],
            ['ksu', 'جامعة الملك سعود', 'King Saud University', '👑', 'السعودية', 'Saudi Arabia', 1957, '65,000+', 'standard', 'https://www.ksu.edu.sa', 'أول جامعة سعودية تأسست عام 1957 وتعتبر من أعرق الجامعات', 'First Saudi university established in 1957, one of the most prestigious universities'],
            ['kau', 'جامعة الملك عبدالعزيز', 'King Abdulaziz University', '🕌', 'السعودية', 'Saudi Arabia', 1967, '80,000+', 'standard', 'https://www.kau.edu.sa', 'جامعة حكومية تأسست عام 1967 في جدة', 'Public university established in 1967 in Jeddah'],
            ['uae_university', 'جامعة الإمارات العربية المتحدة', 'United Arab Emirates University', '🇦🇪', 'الإمارات', 'UAE', 1976, '14,000+', 'standard', 'https://www.uaeu.ac.ae', 'أول جامعة وطنية في دولة الإمارات العربية المتحدة', 'First national university in the United Arab Emirates'],
            ['auc', 'الجامعة الأمريكية بالقاهرة', 'American University in Cairo', '🏺', 'مصر', 'Egypt', 1919, '6,500+', 'standard', 'https://www.aucegypt.edu', 'جامعة أمريكية خاصة في القاهرة تأسست عام 1919', 'Private American university in Cairo established in 1919'],
            ['ju', 'الجامعة الأردنية', 'University of Jordan', '🏛️', 'الأردن', 'Jordan', 1962, '47,000+', 'simple', 'https://www.ju.edu.jo', 'أول جامعة أردنية تأسست عام 1962', 'First Jordanian university established in 1962']
        ];
        
        foreach ($universities as $uni) {
            executeQuery("
                INSERT INTO universities (id, name_ar, name_en, logo, country_ar, country_en, established, students, grading_system, website, description_ar, description_en) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ", $uni);
        }
        
        echo "✅ تم إضافة " . count($universities) . " جامعات<br>";
    }
    
    // Now update students table
    echo "<br><h3>تحديث جدول الطلاب</h3>";
    
    // Check students without university names
    $studentsWithoutUni = fetchOne("SELECT COUNT(*) as count FROM students WHERE university IS NULL OR university = ''")['count'];
    echo "عدد الطلاب بدون اسم جامعة: $studentsWithoutUni<br>";
    
    if ($studentsWithoutUni > 0) {
        // Try to update from university_id
        $studentsWithUniId = fetchOne("SELECT COUNT(*) as count FROM students WHERE university_id IS NOT NULL AND university_id != ''")['count'];
        
        if ($studentsWithUniId > 0) {
            executeQuery("
                UPDATE students s 
                JOIN universities u ON s.university_id = u.id 
                SET s.university = u.name_ar 
                WHERE s.university IS NULL OR s.university = ''
            ");
            echo "✅ تم تحديث أسماء الجامعات من university_id<br>";
        } else {
            // Set default university
            executeQuery("UPDATE students SET university = 'الجامعة العربية المفتوحة' WHERE university IS NULL OR university = ''");
            echo "✅ تم تعيين الجامعة العربية المفتوحة كافتراضية<br>";
        }
    }
    
    // Final check
    $finalUniversityCount = fetchOne("SELECT COUNT(*) as count FROM universities")['count'];
    $studentsWithUni = fetchOne("SELECT COUNT(*) as count FROM students WHERE university IS NOT NULL AND university != ''")['count'];
    
    echo "<br><h3>النتائج النهائية:</h3>";
    echo "✅ عدد الجامعات: $finalUniversityCount<br>";
    echo "✅ عدد الطلاب مع أسماء جامعات: $studentsWithUni<br>";
    echo "✅ تم الإصلاح بنجاح!<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
</style>

<br><br>
<a href="update_database.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">العودة لصفحة التحديث</a>
<a href="index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">تجربة الصفحة الرئيسية</a>
