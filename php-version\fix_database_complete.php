<?php
/**
 * Complete Database Fix
 * إصلاح شامل لقاعدة البيانات
 */

session_start();

// Simple authentication
if (!isset($_SESSION['fix_auth']) && !isset($_POST['password'])) {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إصلاح شامل لقاعدة البيانات</title>
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-100">
        <div class="min-h-screen flex items-center justify-center">
            <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
                <h1 class="text-2xl font-bold text-center mb-6">إصلاح شامل لقاعدة البيانات</h1>
                <form method="POST">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                        <input type="password" name="password" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700">
                        بدء الإصلاح
                    </button>
                </form>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Check password
if (isset($_POST['password'])) {
    if ($_POST['password'] === 'admin123') {
        $_SESSION['fix_auth'] = true;
    } else {
        echo "<script>alert('كلمة مرور خاطئة'); window.location.href = 'fix_database_complete.php';</script>";
        exit;
    }
}

$updates = [];
$errors = [];

try {
    // Direct PDO connection
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $updates[] = "✅ تم الاتصال بقاعدة البيانات بنجاح";
    
    // 1. Check and fix students table structure
    $updates[] = "<h3>1. فحص وإصلاح جدول students</h3>";
    
    // Get current columns
    $stmt = $pdo->query("DESCRIBE students");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $updates[] = "الأعمدة الحالية: " . implode(', ', $columns);
    
    // Add missing columns one by one
    $columnsToAdd = [
        'university' => "VARCHAR(255) DEFAULT 'غير محدد'",
        'cumulative_gpa' => "DECIMAL(4,2) DEFAULT NULL",
        'semester_gpa' => "DECIMAL(4,2) DEFAULT NULL", 
        'total_hours' => "INT DEFAULT 0",
        'classification' => "VARCHAR(100) DEFAULT NULL",
        'share_link' => "VARCHAR(100) DEFAULT NULL",
        'link_views' => "INT DEFAULT 0",
        'link_expires_at' => "TIMESTAMP NULL",
        'is_verified' => "BOOLEAN DEFAULT FALSE"
    ];
    
    foreach ($columnsToAdd as $column => $definition) {
        if (!in_array($column, $columns)) {
            try {
                $pdo->exec("ALTER TABLE students ADD COLUMN $column $definition");
                $updates[] = "✅ تم إضافة العمود: $column";
            } catch (Exception $e) {
                $errors[] = "❌ خطأ في إضافة العمود $column: " . $e->getMessage();
            }
        } else {
            $updates[] = "⚠️ العمود $column موجود مسبقاً";
        }
    }
    
    // 2. Create universities table if not exists
    $updates[] = "<h3>2. إنشاء جدول الجامعات</h3>";
    
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS universities (
                id VARCHAR(50) PRIMARY KEY,
                name_ar VARCHAR(255) NOT NULL,
                name_en VARCHAR(255) NOT NULL,
                logo VARCHAR(10) DEFAULT '🏛️',
                country_ar VARCHAR(100) DEFAULT 'السعودية',
                country_en VARCHAR(100) DEFAULT 'Saudi Arabia',
                established INT DEFAULT 2000,
                students VARCHAR(20) DEFAULT '1000+',
                grading_system VARCHAR(50) DEFAULT 'standard',
                website VARCHAR(255),
                description_ar TEXT,
                description_en TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        $updates[] = "✅ تم إنشاء/فحص جدول universities";
        
        // Check if universities table has data
        $stmt = $pdo->query("SELECT COUNT(*) FROM universities");
        $uniCount = $stmt->fetchColumn();
        
        if ($uniCount == 0) {
            // Insert default universities
            $universities = [
                ['aou', 'الجامعة العربية المفتوحة', 'Arab Open University'],
                ['ksu', 'جامعة الملك سعود', 'King Saud University'],
                ['kau', 'جامعة الملك عبدالعزيز', 'King Abdulaziz University'],
                ['other', 'جامعة أخرى', 'Other University']
            ];
            
            $stmt = $pdo->prepare("INSERT INTO universities (id, name_ar, name_en) VALUES (?, ?, ?)");
            foreach ($universities as $uni) {
                $stmt->execute($uni);
            }
            $updates[] = "✅ تم إضافة " . count($universities) . " جامعات افتراضية";
        } else {
            $updates[] = "✅ جدول الجامعات يحتوي على $uniCount جامعة";
        }
        
    } catch (Exception $e) {
        $errors[] = "❌ خطأ في إنشاء جدول الجامعات: " . $e->getMessage();
    }
    
    // 3. Add indexes safely
    $updates[] = "<h3>3. إضافة الفهارس</h3>";
    
    $indexes = [
        'idx_students_share_link' => 'share_link',
        'idx_students_university_name' => 'university',
        'idx_students_phone' => 'phone'
    ];
    
    foreach ($indexes as $indexName => $column) {
        try {
            // Check if index exists first
            $stmt = $pdo->query("SHOW INDEX FROM students WHERE Key_name = '$indexName'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("CREATE INDEX $indexName ON students($column)");
                $updates[] = "✅ تم إنشاء الفهرس: $indexName";
            } else {
                $updates[] = "⚠️ الفهرس $indexName موجود مسبقاً";
            }
        } catch (Exception $e) {
            $updates[] = "⚠️ تخطي الفهرس $indexName: " . $e->getMessage();
        }
    }
    
    // 4. Update existing data
    $updates[] = "<h3>4. تحديث البيانات الموجودة</h3>";
    
    // Update students without university names
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM students WHERE university IS NULL OR university = '' OR university = 'غير محدد'");
        $studentsToUpdate = $stmt->fetchColumn();
        
        if ($studentsToUpdate > 0) {
            // Try to update from university_id if exists
            try {
                $pdo->exec("
                    UPDATE students s 
                    JOIN universities u ON s.university_id = u.id 
                    SET s.university = u.name_ar 
                    WHERE (s.university IS NULL OR s.university = '' OR s.university = 'غير محدد')
                    AND s.university_id IS NOT NULL
                ");
                $updates[] = "✅ تم تحديث أسماء الجامعات من university_id";
            } catch (Exception $e) {
                // If that fails, set default
                $pdo->exec("UPDATE students SET university = 'الجامعة العربية المفتوحة' WHERE university IS NULL OR university = ''");
                $updates[] = "✅ تم تعيين جامعة افتراضية للطلاب";
            }
        }
    } catch (Exception $e) {
        $errors[] = "❌ خطأ في تحديث أسماء الجامعات: " . $e->getMessage();
    }
    
    // 5. Add sample data if no students exist
    $updates[] = "<h3>5. إضافة بيانات تجريبية</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM students");
    $studentsCount = $stmt->fetchColumn();
    
    if ($studentsCount == 0) {
        $sampleStudents = [
            ['أحمد محمد علي', '0501234567', '<EMAIL>', 'الجامعة العربية المفتوحة', 3.75, 3.80, 120, 'جيد جداً', 'sample_link_1'],
            ['فاطمة أحمد سالم', '0507654321', '<EMAIL>', 'جامعة الملك سعود', 3.90, 3.85, 110, 'ممتاز', 'sample_link_2'],
            ['محمد عبدالله حسن', '0509876543', '<EMAIL>', 'جامعة الملك عبدالعزيز', 3.25, 3.30, 95, 'جيد', 'sample_link_3']
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO students (name, phone, email, university, cumulative_gpa, semester_gpa, total_hours, classification, share_link, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        foreach ($sampleStudents as $student) {
            $stmt->execute($student);
        }
        
        $updates[] = "✅ تم إضافة " . count($sampleStudents) . " طلاب تجريبيين";
    } else {
        $updates[] = "✅ يوجد $studentsCount طالب في قاعدة البيانات";
    }
    
    // 6. Final verification
    $updates[] = "<h3>6. التحقق النهائي</h3>";
    
    $stmt = $pdo->query("DESCRIBE students");
    $finalColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $updates[] = "✅ الأعمدة النهائية: " . implode(', ', $finalColumns);
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM students");
    $finalStudentsCount = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM universities");
    $finalUniversitiesCount = $stmt->fetchColumn();
    
    $updates[] = "✅ العدد النهائي للطلاب: $finalStudentsCount";
    $updates[] = "✅ العدد النهائي للجامعات: $finalUniversitiesCount";
    $updates[] = "🎉 تم الإصلاح الشامل بنجاح!";
    
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح شامل لقاعدة البيانات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h1 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-tools ml-2"></i>
                    إصلاح شامل لقاعدة البيانات
                </h1>
                
                <?php if (!empty($updates)): ?>
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-green-600">
                        <i class="fas fa-check-circle ml-2"></i>
                        التحديثات والإصلاحات
                    </h2>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 max-h-96 overflow-y-auto">
                        <?php foreach ($updates as $update): ?>
                            <div class="mb-2 text-green-800"><?php echo $update; ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-red-600">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        الأخطاء (غير حرجة)
                    </h2>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <?php foreach ($errors as $error): ?>
                            <div class="mb-2 text-red-800"><?php echo htmlspecialchars($error); ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="text-center mt-8">
                    <a href="index.php" class="inline-block bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors mr-4">
                        <i class="fas fa-home ml-2"></i>
                        تجربة الصفحة الرئيسية
                    </a>
                    
                    <a href="admin_students.php" class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors mr-4">
                        <i class="fas fa-users ml-2"></i>
                        إدارة الطلاب
                    </a>
                    
                    <a href="index.php?shared=sample_link_1" class="inline-block bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-link ml-2"></i>
                        تجربة رابط مشترك
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
