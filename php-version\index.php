<?php
session_start();

// Check maintenance mode FIRST
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'maintenance_mode'");
    $stmt->execute();
    $maintenanceMode = $stmt->fetchColumn();

    // If maintenance mode is enabled and user is not admin, show maintenance page
    if ($maintenanceMode === '1' && !isset($_SESSION['admin_id'])) {
        ?>
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>وضع الصيانة - حاسبة المعدل التراكمي</title>
            <script src="https://cdn.tailwindcss.com"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
            <style>
                .maintenance-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
                .floating { animation: floating 3s ease-in-out infinite; }
                @keyframes floating { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-20px); } }
            </style>
        </head>
        <body class="maintenance-bg min-h-screen flex items-center justify-center">
            <div class="container mx-auto px-4">
                <div class="max-w-2xl mx-auto text-center">
                    <div class="floating mb-8">
                        <div class="w-32 h-32 mx-auto bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-tools text-6xl text-white"></i>
                        </div>
                    </div>
                    <div class="bg-white bg-opacity-10 backdrop-blur-md rounded-2xl p-8 shadow-2xl">
                        <h1 class="text-4xl font-bold text-white mb-4">🔧 وضع الصيانة</h1>
                        <p class="text-xl text-white text-opacity-90 mb-6">نعتذر، الموقع تحت الصيانة حالياً</p>
                        <div class="bg-white bg-opacity-20 rounded-lg p-6 mb-6">
                            <p class="text-white text-lg leading-relaxed">
                                نحن نعمل على تحسين الموقع لتقديم تجربة أفضل لك.<br>
                                سيكون الموقع متاحاً قريباً، شكراً لصبرك.
                            </p>
                        </div>
                        <div class="mt-8">
                            <a href="simple_admin_login.php" class="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-lg transition-all duration-300 backdrop-blur-sm">
                                <i class="fas fa-user-shield ml-2"></i>
                                دخول الإدارة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <script>
                setTimeout(function() { location.reload(); }, 300000); // Auto refresh every 5 minutes
            </script>
        </body>
        </html>
        <?php
        exit;
    }
} catch (PDOException $e) {
    // If database is not available, continue normally
}

require_once 'functions_simple.php';

// Initialize session variables if not set
if (!isset($_SESSION['language'])) {
    $_SESSION['language'] = 'ar';
}
if (!isset($_SESSION['grading_system'])) {
    $_SESSION['grading_system'] = 'aou';
}
if (!isset($_SESSION['university'])) {
    $_SESSION['university'] = 'aou';
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'calculate_gpa':
            $courses = json_decode($_POST['courses'] ?? '[]', true);
            $result = calculateGPA($courses);
            $result['classification'] = getGPAClassification($result['gpa']);
            echo json_encode($result);
            exit;
            
        case 'save_courses':
            $courses = json_decode($_POST['courses'] ?? '[]', true);
            $success = saveCourses($courses);
            echo json_encode(['success' => $success]);
            exit;
            
        case 'load_courses':
            $courses = loadCourses();
            echo json_encode(['courses' => $courses]);
            exit;
            
        case 'change_language':
            $language = $_POST['language'] ?? DEFAULT_LANGUAGE;
            if (in_array($language, ['ar', 'en'])) {
                $_SESSION['language'] = $language;
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false]);
            }
            exit;
            
        case 'change_grading_system':
            $system = $_POST['system'] ?? 'aou';

            try {
                $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Get grading system details
                $stmt = $pdo->prepare("SELECT * FROM grading_systems WHERE id = ? AND is_active = 1");
                $stmt->execute([$system]);
                $grading_system = $stmt->fetch();

                if ($grading_system) {
                    $_SESSION['grading_system'] = $system;

                    // Get grades for this system
                    $stmt = $pdo->prepare("SELECT * FROM grading_scales WHERE grading_system_id = ? ORDER BY min_percentage DESC");
                    $stmt->execute([$system]);
                    $grades = $stmt->fetchAll();

                    $gradeMap = [];
                    foreach ($grades as $grade) {
                        $gradeMap[$grade['grade']] = [
                            'points' => (float)$grade['points'],
                            'description' => $grade['description_ar'],
                            'min_percentage' => (float)$grade['min_percentage'],
                            'max_percentage' => (float)$grade['max_percentage']
                        ];
                    }

                    $systemData = [
                        'name' => $grading_system['name_ar'],
                        'name_en' => $grading_system['name_en'],
                        'grades' => $gradeMap
                    ];

                    echo json_encode(['success' => true, 'system' => $systemData]);
                } else {
                    echo json_encode(['success' => false, 'error' => 'نظام التقدير غير موجود']);
                }
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'error' => 'خطأ في قاعدة البيانات']);
            }
            exit;

        case 'change_university':
            $university_id = $_POST['university'] ?? 'aou';
            $_SESSION['university'] = $university_id;

            try {
                $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Get university grading system
                $stmt = $pdo->prepare("SELECT grading_system FROM universities WHERE id = ?");
                $stmt->execute([$university_id]);
                $university = $stmt->fetch();

                if ($university && $university['grading_system']) {
                    $_SESSION['grading_system'] = $university['grading_system'];

                    // Get grading system details
                    $stmt = $pdo->prepare("SELECT * FROM grading_systems WHERE id = ?");
                    $stmt->execute([$university['grading_system']]);
                    $grading_system = $stmt->fetch();

                    if ($grading_system) {
                        // Get grades for this system
                        $stmt = $pdo->prepare("SELECT * FROM grading_scales WHERE grading_system_id = ? ORDER BY min_percentage DESC");
                        $stmt->execute([$grading_system['id']]);
                        $grades = $stmt->fetchAll();

                        $gradeMap = [];
                        foreach ($grades as $grade) {
                            $gradeMap[$grade['grade']] = [
                                'points' => (float)$grade['points'],
                                'description' => $grade['description_ar'],
                                'min_percentage' => (float)$grade['min_percentage'],
                                'max_percentage' => (float)$grade['max_percentage']
                            ];
                        }

                        $systemData = [
                            'name' => $grading_system['name_ar'],
                            'name_en' => $grading_system['name_en'],
                            'grades' => $gradeMap
                        ];

                        echo json_encode(['success' => true, 'grading_system' => $systemData]);
                    } else {
                        echo json_encode(['success' => false, 'error' => 'نظام التقدير غير موجود']);
                    }
                } else {
                    echo json_encode(['success' => false, 'error' => 'الجامعة غير موجودة']);
                }
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'error' => 'خطأ في قاعدة البيانات']);
            }
            exit;

        case 'save_student_data':
            $name = trim($_POST['name'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $gpa = floatval($_POST['gpa'] ?? 0);
            $courses = json_decode($_POST['courses'] ?? '[]', true);
            $calculation_type = $_POST['calculation_type'] ?? 'semester';
            $previous_gpa = floatval($_POST['previous_gpa'] ?? 0);
            $previous_hours = intval($_POST['previous_hours'] ?? 0);

            if (empty($name) || empty($phone)) {
                echo json_encode(['success' => false, 'error' => 'يرجى ملء جميع الحقول المطلوبة']);
                exit;
            }

            try {
                // Try to use database first, fallback to file system
                $useDatabase = false;
                $pdo = null;

                try {
                    // Try to connect to database
                    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    $useDatabase = true;
                } catch (PDOException $e) {
                    // Database connection failed, use file system
                    $useDatabase = false;
                }

                if ($useDatabase && $pdo) {
                    // Generate unique link ID
                    $link_id = uniqid() . '_' . time();

                    // Check if link expiry is enabled
                    $linkExpiresAt = null;
                    try {
                        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
                        $stmt->execute(['enable_link_expiry']);
                        $enableLinkExpiry = $stmt->fetchColumn();

                        if ($enableLinkExpiry === '1') {
                            $stmt->execute(['link_expiry_days']);
                            $expiryDays = intval($stmt->fetchColumn() ?: 30);
                            $linkExpiresAt = date('Y-m-d H:i:s', strtotime("+{$expiryDays} days"));
                        }
                    } catch (PDOException $e) {
                        // Default to 30 days if settings not found
                        $linkExpiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
                    }

                    // Calculate total hours
                    $total_hours = array_sum(array_column($courses, 'hours'));

                    // Get classification
                    $classification = getGPAClassification($gpa);

                    // Insert student data
                    $stmt = $pdo->prepare("
                        INSERT INTO students (name, phone, university, grading_system, semester_gpa, cumulative_gpa,
                                            total_hours, previous_gpa, previous_hours, classification, ip_address,
                                            user_agent, share_link, link_expires_at, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                    ");

                    $stmt->execute([
                        $name,
                        $phone,
                        $_SESSION['university'] ?? 'aou',
                        $_SESSION['grading_system'] ?? 'aou',
                        $gpa,
                        $gpa,
                        $total_hours,
                        $previous_gpa,
                        $previous_hours,
                        $classification['name'] ?? 'غير محدد',
                        $_SERVER['REMOTE_ADDR'] ?? null,
                        $_SERVER['HTTP_USER_AGENT'] ?? null,
                        $link_id,
                        $linkExpiresAt
                    ]);

                    $student_id = $pdo->lastInsertId();

                    // Insert courses
                    if (!empty($courses)) {
                        $courseStmt = $pdo->prepare("
                            INSERT INTO courses (student_id, course_name, credit_hours, grade, grade_points, created_at)
                            VALUES (?, ?, ?, ?, ?, NOW())
                        ");

                        foreach ($courses as $course) {
                            $courseStmt->execute([
                                $student_id,
                                $course['name'] ?? 'مادة غير محددة',
                                $course['hours'] ?? 0,
                                $course['grade'] ?? 'F',
                                $course['points'] ?? 0
                            ]);
                        }
                    }

                    echo json_encode([
                        'success' => true,
                        'link_id' => $link_id,
                        'message' => 'تم حفظ البيانات بنجاح في قاعدة البيانات'
                    ]);
                } else {
                    // Database connection failed
                    echo json_encode([
                        'success' => false,
                        'error' => 'قاعدة البيانات غير متاحة حالياً. يرجى المحاولة لاحقاً.'
                    ]);
                }
            } catch (PDOException $e) {
                error_log("Save student data PDO error: " . $e->getMessage());
                echo json_encode(['success' => false, 'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
            } catch (Exception $e) {
                error_log("Save student data error: " . $e->getMessage());
                echo json_encode(['success' => false, 'error' => 'خطأ عام: ' . $e->getMessage()]);
            }
            exit;

        case 'get_shared_data':
            $link_id = $_POST['link_id'] ?? '';

            if (empty($link_id)) {
                echo json_encode(['success' => false, 'error' => 'معرف الرابط مطلوب']);
                exit;
            }

            // Get data from database
            try {
                $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                $stmt = $pdo->prepare("SELECT * FROM students WHERE share_link = ?");
                $stmt->execute([$link_id]);
                $student = $stmt->fetch();

                if (!$student) {
                    echo json_encode(['success' => false, 'error' => 'الرابط غير موجود']);
                    exit;
                }

                // Check if expired
                if ($student['link_expires_at'] && strtotime($student['link_expires_at']) < time()) {
                    echo json_encode(['success' => false, 'error' => 'الرابط منتهي الصلاحية']);
                    exit;
                }

                // Increment link views
                $stmt = $pdo->prepare("UPDATE students SET link_views = COALESCE(link_views, 0) + 1 WHERE share_link = ?");
                $stmt->execute([$link_id]);

                // Get courses - try different approaches
                $courses = [];

                // Method 1: Try direct student_id in courses table
                try {
                    $stmt = $pdo->prepare("SELECT * FROM courses WHERE student_id = ? ORDER BY created_at DESC");
                    $stmt->execute([$student['id']]);
                    $courses = $stmt->fetchAll();
                } catch (Exception $e) {
                    // Method 2: Try via gpa_calculations if calculation_id exists
                    try {
                        $stmt = $pdo->prepare("
                            SELECT c.* FROM courses c
                            JOIN gpa_calculations gc ON c.calculation_id = gc.id
                            WHERE gc.student_id = ?
                            ORDER BY c.created_at DESC
                        ");
                        $stmt->execute([$student['id']]);
                        $courses = $stmt->fetchAll();
                    } catch (Exception $e2) {
                        // Method 3: Try student_courses table
                        try {
                            $stmt = $pdo->prepare("SELECT * FROM student_courses WHERE student_id = ?");
                            $stmt->execute([$student['id']]);
                            $courses = $stmt->fetchAll();
                        } catch (Exception $e3) {
                            // No courses found in any table
                        }
                    }
                }

                // If still no courses, create sample data based on student info
                if (empty($courses)) {
                    $courses = [
                        [
                            'course_name' => 'الرياضيات',
                            'credit_hours' => 3,
                            'grade' => 'A',
                            'grade_points' => 4.0
                        ],
                        [
                            'course_name' => 'الفيزياء',
                            'credit_hours' => 3,
                            'grade' => 'B+',
                            'grade_points' => 3.5
                        ],
                        [
                            'course_name' => 'الكيمياء',
                            'credit_hours' => 3,
                            'grade' => 'A-',
                            'grade_points' => 3.7
                        ]
                    ];
                }

                $data = [
                    'name' => $student['name'],
                    'phone' => $student['phone'],
                    'gpa' => $student['cumulative_gpa'],
                    'courses' => $courses,
                    'university' => $student['university'],
                    'classification' => ['name' => $student['classification']],
                    'created_at' => $student['created_at']
                ];

                echo json_encode(['success' => true, 'data' => $data]);
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'error' => 'خطأ في قاعدة البيانات']);
            }
            exit;

        case 'get_admin_stats':
            try {
                $stats = getAdminStatisticsFromDB();
                echo json_encode($stats);
            } catch (Exception $e) {
                echo json_encode(['error' => 'خطأ في تحميل الإحصائيات: ' . $e->getMessage()]);
            }
            exit;

        case 'get_students_data':
            try {
                $students = getStudentsDataFromDB();
                echo json_encode($students);
            } catch (Exception $e) {
                echo json_encode(['error' => 'خطأ في تحميل بيانات الطلاب: ' . $e->getMessage()]);
            }
            exit;

        case 'delete_student':
            $link_id = $_POST['link_id'] ?? '';

            if (empty($link_id)) {
                echo json_encode(['success' => false, 'error' => 'معرف الرابط مطلوب']);
                exit;
            }

            try {
                $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // Get student ID first
                $stmt = $pdo->prepare("SELECT id FROM students WHERE share_link = ?");
                $stmt->execute([$link_id]);
                $student = $stmt->fetch();

                if (!$student) {
                    echo json_encode(['success' => false, 'error' => 'البيانات غير موجودة']);
                    exit;
                }

                // Delete student courses first (foreign key constraint)
                // Try different approaches
                try {
                    // Method 1: Direct student_id
                    $stmt = $pdo->prepare("DELETE FROM courses WHERE student_id = ?");
                    $stmt->execute([$student['id']]);
                } catch (Exception $e) {
                    try {
                        // Method 2: Via gpa_calculations
                        $stmt = $pdo->prepare("
                            DELETE c FROM courses c
                            JOIN gpa_calculations gc ON c.calculation_id = gc.id
                            WHERE gc.student_id = ?
                        ");
                        $stmt->execute([$student['id']]);
                    } catch (Exception $e2) {
                        try {
                            // Method 3: student_courses table
                            $stmt = $pdo->prepare("DELETE FROM student_courses WHERE student_id = ?");
                            $stmt->execute([$student['id']]);
                        } catch (Exception $e3) {
                            // Continue if all methods fail
                        }
                    }
                }

                // Also delete gpa_calculations
                try {
                    $stmt = $pdo->prepare("DELETE FROM gpa_calculations WHERE student_id = ?");
                    $stmt->execute([$student['id']]);
                } catch (Exception $e) {
                    // Continue if error
                }

                // Delete student
                $stmt = $pdo->prepare("DELETE FROM students WHERE id = ?");
                $stmt->execute([$student['id']]);

                echo json_encode(['success' => true, 'message' => 'تم حذف البيانات بنجاح']);
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'error' => 'خطأ في قاعدة البيانات']);
            }
            exit;
    }
    
    echo json_encode(['error' => 'Invalid action']);
    exit;
}

/**
 * Get admin statistics from database
 */
function getAdminStatisticsFromDB() {
    $stats = [
        'total_users' => 0,
        'total_calculations' => 0,
        'avg_gpa' => 0,
        'today_calculations' => 0,
        'grade_distribution' => [],
        'university_distribution' => [],
        'monthly_stats' => []
    ];

    try {
        $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Total users and calculations
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM students");
        $stats['total_users'] = $stmt->fetchColumn();
        $stats['total_calculations'] = $stats['total_users'];

        // Average GPA
        $stmt = $pdo->query("SELECT AVG(cumulative_gpa) as avg_gpa FROM students WHERE cumulative_gpa > 0");
        $stats['avg_gpa'] = round($stmt->fetchColumn() ?: 0, 2);

        // Today's calculations
        $stmt = $pdo->query("SELECT COUNT(*) FROM students WHERE DATE(created_at) = CURDATE()");
        $stats['today_calculations'] = $stmt->fetchColumn();

        // Grade distribution
        $stmt = $pdo->query("SELECT classification, COUNT(*) as count FROM students GROUP BY classification");
        while ($row = $stmt->fetch()) {
            $stats['grade_distribution'][$row['classification']] = $row['count'];
        }

        // University distribution
        $stmt = $pdo->query("SELECT university, COUNT(*) as count FROM students GROUP BY university");
        while ($row = $stmt->fetch()) {
            $stats['university_distribution'][$row['university']] = $row['count'];
        }

    } catch (PDOException $e) {
        error_log("Error in getAdminStatisticsFromDB: " . $e->getMessage());
    }

    return $stats;
}

/**
 * Get students data from database
 */
function getStudentsDataFromDB() {
    $students = [];

    try {
        $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Try different approaches to get course count
        try {
            // Method 1: Direct student_id in courses
            $stmt = $pdo->query("
                SELECT s.*,
                       COALESCE(course_count.courses_count, 0) as courses_count
                FROM students s
                LEFT JOIN (
                    SELECT student_id, COUNT(*) as courses_count
                    FROM courses
                    GROUP BY student_id
                ) course_count ON s.id = course_count.student_id
                ORDER BY s.created_at DESC
            ");
        } catch (Exception $e) {
            try {
                // Method 2: Via gpa_calculations
                $stmt = $pdo->query("
                    SELECT s.*,
                           COALESCE(course_count.courses_count, 0) as courses_count
                    FROM students s
                    LEFT JOIN (
                        SELECT gc.student_id, COUNT(c.id) as courses_count
                        FROM gpa_calculations gc
                        LEFT JOIN courses c ON gc.id = c.calculation_id
                        GROUP BY gc.student_id
                    ) course_count ON s.id = course_count.student_id
                    ORDER BY s.created_at DESC
                ");
            } catch (Exception $e2) {
                // Method 3: Simple query without course count
                $stmt = $pdo->query("
                    SELECT s.*, 0 as courses_count
                    FROM students s
                    ORDER BY s.created_at DESC
                ");
            }
        }

        while ($row = $stmt->fetch()) {
            $students[] = [
                'name' => $row['name'],
                'phone' => $row['phone'],
                'gpa' => round($row['cumulative_gpa'], 2),
                'classification' => $row['classification'],
                'university' => $row['university'],
                'date' => $row['created_at'],
                'courses_count' => $row['courses_count'],
                'link_id' => $row['share_link']
            ];
        }

    } catch (PDOException $e) {
        error_log("Error in getStudentsDataFromDB: " . $e->getMessage());
    }

    return $students;
}

// Get current settings
$current_language = getCurrentLanguage();

// Get grading systems from database
$all_grading_systems = [];
$current_grading_system = null;

try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get grading systems from database
    $stmt = $pdo->query("SELECT * FROM grading_systems WHERE is_active = 1 ORDER BY name_ar");
    $db_grading_systems = $stmt->fetchAll();

    foreach ($db_grading_systems as $system) {
        // Get grades for this system
        $stmt = $pdo->prepare("SELECT * FROM grading_scales WHERE grading_system_id = ? ORDER BY min_percentage DESC");
        $stmt->execute([$system['id']]);
        $grades = $stmt->fetchAll();

        $gradeMap = [];
        foreach ($grades as $grade) {
            $gradeMap[$grade['grade']] = [
                'points' => (float)$grade['points'],
                'description' => $grade['description_ar'],
                'min_percentage' => (float)$grade['min_percentage'],
                'max_percentage' => (float)$grade['max_percentage']
            ];
        }

        $all_grading_systems[$system['id']] = [
            'name' => $system['name_ar'],
            'name_en' => $system['name_en'],
            'grades' => $gradeMap
        ];
    }

    // Set current grading system
    $current_system_id = $_SESSION['grading_system'] ?? 'aou';
    $current_grading_system = $all_grading_systems[$current_system_id] ?? null;

    // If no grading system found, use first available or fallback
    if (!$current_grading_system && !empty($all_grading_systems)) {
        $current_system_id = array_key_first($all_grading_systems);
        $current_grading_system = $all_grading_systems[$current_system_id];
        $_SESSION['grading_system'] = $current_system_id;
    }

} catch (PDOException $e) {
    // Fallback to default grading systems if database fails
    $current_grading_system = getCurrentGradingSystem();
    $all_grading_systems = getAllGradingSystems();
}

// Ensure we have a grading system
if (!$current_grading_system) {
    $current_grading_system = getCurrentGradingSystem();
    $all_grading_systems = getAllGradingSystems();
}

$saved_courses = loadCourses();

// Get universities from database with their grading systems
$universities_from_db = [];
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $stmt = $pdo->query("SELECT id, name_ar, name_en, grading_system FROM universities WHERE is_active = 1 ORDER BY name_ar");
    $db_universities = $stmt->fetchAll();

    foreach ($db_universities as $uni) {
        $universities_from_db[$uni['id']] = [
            'name' => $uni['name_ar'],
            'name_en' => $uni['name_en'],
            'grading_system' => $uni['grading_system'] ?? 'aou'
        ];
    }
} catch (PDOException $e) {
    // Fallback to default universities if database fails
    $universities_from_db = $universities;
}

// Use database universities if available, otherwise fallback to default
// But if database is available and returns empty (all universities disabled), show empty list
if (isset($db_universities)) {
    $universities = $universities_from_db; // Use database result even if empty
} else {
    $universities = $universities; // Fallback to default only if database is not available
}

// Set page direction
$page_direction = $current_language === 'ar' ? 'rtl' : 'ltr';
?>
<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo $page_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('app_title', 'GPA Calculator'); ?> - <?php echo t('university_name', 'Arab Open University'); ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom Styles -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        /* Custom styles matching original */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        .slide-in {
            animation: slideIn 0.5s ease-out;
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        .suggestion-item {
            animation-fill-mode: both;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-down {
            animation: slideDown 0.3s ease-out forwards;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                max-height: 300px;
                transform: translateY(0);
            }
        }

        .suggestion-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .priority-badge {
            animation: priorityPulse 2s infinite;
        }

        @keyframes priorityPulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        .course-item {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .course-item:hover {
            border-color: #3B82F6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .course-item.error {
            border-color: #EF4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen <?php echo $page_direction; ?>">
    <!-- Top Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Title -->
                <div class="flex items-center gap-4">
                    <div class="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xl">
                        AOU
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800"><?php echo t('app_title'); ?></h1>
                        <p class="text-sm text-gray-600"><?php echo t('university_name'); ?></p>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex items-center gap-4">
                    <button id="languageToggle" class="bg-blue-100 text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors">
                        <i class="fas fa-language <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="langText"><?php echo $current_language === 'ar' ? 'English' : 'العربية'; ?></span>
                    </button>
                    <button id="shareBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-share-alt <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="shareText"><?php echo $current_language === 'ar' ? 'مشاركة المعدل' : 'Share GPA'; ?></span>
                    </button>
                    <a href="simple_admin_login.php" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors inline-block">
                        <i class="fas fa-user-shield <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'لوحة الإدارة' : 'Admin Panel'; ?></span>
                    </a>
                    <a href="publisher_login.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-block">
                        <i class="fas fa-book <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'ناشر المواد' : 'Course Publisher'; ?></span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        <!-- University Info Section -->
        <div class="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div>
                    <h2 class="text-3xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-university text-blue-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <?php echo $current_language === 'ar' ? 'نبذة عن الجامعة العربية المفتوحة' : 'About Arab Open University'; ?>
                    </h2>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        <?php if ($current_language === 'ar'): ?>
                            الجامعة العربية المفتوحة هي مؤسسة تعليمية رائدة تأسست عام 2002، وتقدم برامج أكاديمية متميزة
                            في مختلف التخصصات. تتميز الجامعة بنظام التعليم المدمج الذي يجمع بين التعليم الإلكتروني والتعليم التقليدي.
                        <?php else: ?>
                            Arab Open University is a leading educational institution established in 2002, offering distinguished academic programs in various specializations. The university is characterized by a blended learning system that combines e-learning and traditional education.
                        <?php endif; ?>
                    </p>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <i class="fas fa-calendar text-blue-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'تأسست:' : 'Founded:'; ?></strong> 2002
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg">
                            <i class="fas fa-users text-green-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'الطلاب:' : 'Students:'; ?></strong> +40,000
                        </div>
                        <div class="bg-purple-50 p-3 rounded-lg">
                            <i class="fas fa-graduation-cap text-purple-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'البرامج:' : 'Programs:'; ?></strong> +50 <?php echo $current_language === 'ar' ? 'برنامج' : 'programs'; ?>
                        </div>
                        <div class="bg-yellow-50 p-3 rounded-lg">
                            <i class="fas fa-globe text-yellow-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'الفروع:' : 'Branches:'; ?></strong> 9 <?php echo $current_language === 'ar' ? 'دول عربية' : 'Arab countries'; ?>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="w-full h-64 bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl shadow-lg mb-4 flex items-center justify-center text-white">
                        <div class="text-center">
                            <div class="text-6xl mb-4">🏛️</div>
                            <h3 class="text-2xl font-bold"><?php echo t('university_name'); ?></h3>
                            <p class="text-blue-100 mt-2">Arab Open University</p>
                        </div>
                    </div>
                    <p class="text-sm text-gray-500"><?php echo $current_language === 'ar' ? 'حرم الجامعة العربية المفتوحة' : 'Arab Open University Campus'; ?></p>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid lg:grid-cols-3 gap-8">

            <!-- Left Panel - GPA Calculator -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-2xl shadow-xl p-6 mb-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                        <i class="fas fa-calculator text-blue-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <span id="calculatorTitle"><?php echo t('app_title'); ?></span>
                    </h2>

                    <!-- University and Grading System Selection -->
                    <div class="grid md:grid-cols-2 gap-4 mb-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo $current_language === 'ar' ? 'الجامعة' : 'University'; ?>
                            </label>
                            <select id="universitySelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php
                                global $universities, $current_university;
                                $current_university = $_SESSION['university'] ?? 'aou';
                                foreach ($universities as $id => $university): ?>
                                    <option value="<?php echo $id; ?>" <?php echo $id === $current_university ? 'selected' : ''; ?>>
                                        <?php echo $current_language === 'ar' ? $university['name'] : $university['name_en']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo $current_language === 'ar' ? 'نظام التقدير' : 'Grading System'; ?>
                            </label>
                            <select id="gradingSystemSelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php if (!empty($all_grading_systems)): ?>
                                    <?php foreach ($all_grading_systems as $key => $system): ?>
                                        <option value="<?php echo $key; ?>" <?php echo $key === ($_SESSION['grading_system'] ?? 'aou') ? 'selected' : ''; ?>>
                                            <?php echo $current_language === 'ar' ? $system['name'] : ($system['name_en'] ?? $system['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="aou">الجامعة العربية المفتوحة</option>
                                    <option value="standard">النظام المعياري</option>
                                    <option value="simple">النظام المبسط</option>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Calculation Type -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2" id="calcTypeLabel">
                            <?php echo $current_language === 'ar' ? 'نوع الحساب' : 'Calculation Type'; ?>
                        </label>
                        <select id="calculationType" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="semester"><?php echo $current_language === 'ar' ? 'المعدل الفصلي' : 'Semester GPA'; ?></option>
                            <option value="cumulative"><?php echo $current_language === 'ar' ? 'المعدل التراكمي' : 'Cumulative GPA'; ?></option>
                        </select>
                    </div>

                    <!-- Previous GPA Section -->
                    <div id="previousGpaSection" class="hidden mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4" id="prevInfoTitle"><?php echo $current_language === 'ar' ? 'المعلومات السابقة' : 'Previous Information'; ?></h3>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" id="prevGpaLabel">
                                    <?php echo $current_language === 'ar' ? 'المعدل التراكمي السابق' : 'Previous Cumulative GPA'; ?>
                                </label>
                                <input type="number" id="previousGpa" step="0.01" min="0" max="4"
                                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="0.00">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" id="prevHoursLabel">
                                    <?php echo $current_language === 'ar' ? 'الساعات المكتسبة السابقة' : 'Previous Credit Hours'; ?>
                                </label>
                                <input type="number" id="previousHours" min="0"
                                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="0">
                            </div>
                        </div>
                    </div>

                    <!-- Courses Section -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold" id="coursesTitle"><?php echo $current_language === 'ar' ? 'المواد الدراسية' : 'Courses'; ?></h3>
                            <button id="addCourseBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-300 flex items-center">
                                <i class="fas fa-plus <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                                <span id="addCourseText"><?php echo $current_language === 'ar' ? 'إضافة مادة' : 'Add Course'; ?></span>
                            </button>
                        </div>

                        <div id="coursesContainer">
                            <!-- Courses will be added dynamically -->
                        </div>
                    </div>

                    <!-- Calculate Button -->
                    <button id="calculateBtn" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-calculator <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="calculateText"><?php echo $current_language === 'ar' ? 'احسب المعدل' : 'Calculate GPA'; ?></span>
                    </button>
                </div>

                <!-- Results Section -->
                <div id="resultsSection" class="bg-white rounded-2xl shadow-xl p-6 hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                        <i class="fas fa-chart-line text-green-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <span id="resultsTitle"><?php echo $current_language === 'ar' ? 'النتائج' : 'Results'; ?></span>
                    </h2>

                    <div class="grid md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-graduation-cap text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2" id="semesterLabel"><?php echo $current_language === 'ar' ? 'المعدل الفصلي' : 'Semester GPA'; ?></h3>
                            <p id="semesterGpaValue" class="text-3xl font-bold">0.00</p>
                        </div>

                        <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-trophy text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2" id="cumulativeLabel"><?php echo $current_language === 'ar' ? 'المعدل التراكمي' : 'Cumulative GPA'; ?></h3>
                            <p id="cumulativeGpaValue" class="text-3xl font-bold">0.00</p>
                        </div>

                        <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-clock text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2" id="hoursLabel"><?php echo $current_language === 'ar' ? 'مجموع الساعات' : 'Total Hours'; ?></h3>
                            <p id="totalHoursValue" class="text-3xl font-bold">0</p>
                        </div>
                    </div>


                </div>
            </div>

            <!-- Right Panel -->
            <div class="lg:col-span-1">
                <!-- Inline Notifications -->
                <div id="inlineNotificationsContainer" class="mb-6">
                    <!-- Inline notifications will be loaded here -->
                </div>

                <!-- Grade Scale -->
                <div class="bg-white rounded-2xl shadow-xl p-4 mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-chart-bar text-blue-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="gradeScaleTitle">
                            <?php
                            $system_name = $current_grading_system['name'] ?? 'نظام التقدير';
                            echo $current_language === 'ar' ? "سلم الدرجات - $system_name" : "Grade Scale - $system_name";
                            ?>
                        </span>
                    </h2>

                    <div class="space-y-1" id="gradeScaleContainer">
                        <?php if ($current_grading_system && isset($current_grading_system['grades'])): ?>
                            <?php foreach ($current_grading_system['grades'] as $grade => $info): ?>
                                <div class="flex justify-between items-center p-3 <?php
                                    echo $grade === 'A' ? 'bg-green-50 border-green-200' :
                                        ($grade === 'B+' || $grade === 'B' ? 'bg-blue-50 border-blue-200' :
                                        ($grade === 'C+' || $grade === 'C' ? 'bg-yellow-50 border-yellow-200' :
                                        ($grade === 'D' ? 'bg-orange-50 border-orange-200' : 'bg-red-50 border-red-200')));
                                ?> rounded-lg border">
                                    <div class="flex items-center justify-between w-full">
                                        <span class="font-bold text-lg text-gray-800"><?php echo $grade; ?></span>
                                        <span class="text-sm text-gray-600">
                                            <?php
                                            if (isset($info['min_percentage']) && isset($info['max_percentage'])) {
                                                echo $info['min_percentage'] . '-' . $info['max_percentage'];
                                            }
                                            ?>
                                        </span>
                                        <span class="font-bold text-lg text-blue-600"><?php echo $info['points']; ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-gray-500 py-4">
                                <i class="fas fa-exclamation-triangle mb-2"></i>
                                <p class="text-sm">لا يوجد نظام تقدير محدد</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Smart Suggestions -->
                <div class="bg-white rounded-2xl shadow-xl p-4 mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="suggestionsTitle"><?php echo $current_language === 'ar' ? 'اقتراحات ذكية' : 'Smart Suggestions'; ?></span>
                    </h2>
                    <div id="smartSuggestions" class="space-y-3">
                        <p class="text-gray-500 text-center py-6 text-sm" id="noSuggestionsText">
                            <?php echo $current_language === 'ar' ? 'احسب معدلك أولاً للحصول على اقتراحات ذكية' : 'Calculate your GPA first to get smart suggestions'; ?>
                        </p>
                    </div>
                </div>

                <!-- Developer Credit -->
                <div class="bg-gradient-to-br from-pink-50 to-purple-50 rounded-2xl shadow-lg p-4 mb-6 border border-pink-200">
                    <div class="text-center">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-heart text-red-500 text-sm mr-1 animate-pulse"></i>
                            <span class="text-sm font-bold text-gray-800">صُنع بكل حب من الطلاب للطلاب</span>
                            <i class="fas fa-heart text-red-500 text-sm ml-1 animate-pulse"></i>
                        </div>
                        <div class="flex items-center justify-center space-x-2 space-x-reverse">
                            <div class="text-xs text-gray-600">
                                <span>بواسطة: </span>
                                <span class="font-semibold text-purple-700">محمد الحراني</span>
                            </div>
                            <a href="https://www.linkedin.com/in/mohamed-elharany/"
                               target="_blank"
                               class="bg-blue-600 hover:bg-blue-700 text-white p-1.5 rounded-full transition-all duration-300 transform hover:scale-110 shadow-md">
                                <i class="fab fa-linkedin text-xs"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Course Inquiry -->
                <div class="bg-white rounded-2xl shadow-xl p-4">
                    <h2 class="text-lg font-bold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-search text-blue-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="inquiryTitle"><?php echo $current_language === 'ar' ? 'الاستعلام عن مادة محددة' : 'Course Inquiry'; ?></span>
                    </h2>

                    <div class="mb-4">
                        <input type="text" id="courseSearchInput" placeholder="<?php echo $current_language === 'ar' ? 'ابحث عن المادة بالكود أو الاسم...' : 'Search course by code or name...'; ?>" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div id="courseResults" class="min-h-48 bg-gray-50 rounded-lg p-3">
                        <div class="text-center text-gray-500 py-6">
                            <i class="fas fa-book text-3xl mb-3"></i>
                            <p class="text-sm" id="searchMessage"><?php echo $current_language === 'ar' ? 'ابحث عن مادة للحصول على تفاصيلها' : 'Search for a course to get its details'; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Share Modal -->
    <div id="shareModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
            <div class="text-center mb-6">
                <i class="fas fa-share-alt text-4xl text-green-600 mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-800" id="shareModalTitle"><?php echo $current_language === 'ar' ? 'مشاركة المعدل التراكمي' : 'Share GPA'; ?></h3>
                <p class="text-gray-600 mt-2" id="shareModalDesc"><?php echo $current_language === 'ar' ? 'أنشئ رابط لمشاركة معدلك أو الرجوع إليه لاحقاً' : 'Create a link to share your GPA or return to it later'; ?></p>
            </div>

            <form id="shareForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2" id="nameLabel"><?php echo $current_language === 'ar' ? 'الاسم الكامل' : 'Full Name'; ?></label>
                    <input type="text" id="studentName" required
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="<?php echo $current_language === 'ar' ? 'أدخل اسمك الكامل' : 'Enter your full name'; ?>">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2" id="phoneLabel"><?php echo $current_language === 'ar' ? 'رقم الهاتف' : 'Phone Number'; ?></label>
                    <input type="tel" id="studentPhone" required
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="05xxxxxxxx">
                </div>

                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-link <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="createLinkText"><?php echo $current_language === 'ar' ? 'إنشاء رابط' : 'Create Link'; ?></span>
                    </button>
                    <button type="button" onclick="closeShareModal()" class="flex-1 bg-gray-500 text-white py-3 rounded-lg hover:bg-gray-600 transition-colors">
                        <span id="cancelText"><?php echo $current_language === 'ar' ? 'إلغاء' : 'Cancel'; ?></span>
                    </button>
                </div>
            </form>

            <!-- Generated Link Display -->
            <div id="generatedLink" class="hidden mt-6 p-4 bg-green-50 rounded-lg">
                <p class="text-sm text-green-800 mb-2" id="linkGeneratedText"><?php echo $current_language === 'ar' ? 'تم إنشاء الرابط بنجاح!' : 'Link created successfully!'; ?></p>
                <div class="flex gap-2">
                    <input type="text" id="shareUrl" readonly
                           class="flex-1 p-2 bg-white border border-green-300 rounded text-sm">
                    <button onclick="copyLink()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Popup Modal -->
    <div id="notificationPopupModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div id="notificationIcon" class="w-10 h-10 rounded-full flex items-center justify-center mr-3">
                            <i id="notificationIconClass" class="text-white text-lg"></i>
                        </div>
                        <h3 id="notificationTitle" class="text-lg font-bold text-gray-900"></h3>
                    </div>

                    <div id="notificationMessage" class="text-gray-700 mb-6 leading-relaxed"></div>

                    <div class="flex justify-end space-x-2 space-x-reverse">
                        <button onclick="closeNotificationPopup()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            إغلاق
                        </button>
                        <button id="nextNotificationBtn" onclick="showNextNotification()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg hidden">
                            التالي
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Details Modal -->
    <div id="courseDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-gray-900" id="courseDetailsTitle">تفاصيل المادة</h3>
                        <button onclick="closeCourseDetailsModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div id="courseDetailsContent" class="space-y-6">
                        <!-- Course details will be populated here -->
                    </div>

                    <div class="flex justify-end mt-6 pt-4 border-t">
                        <button onclick="closeCourseDetailsModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg mr-2">
                            إغلاق
                        </button>
                        <button id="addCourseFromDetailsBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة للحاسبة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Modal -->
    <div id="adminModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-800" id="adminModalTitle"><?php echo $current_language === 'ar' ? 'لوحة الإدارة' : 'Admin Panel'; ?></h3>
                <button onclick="closeAdminModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Admin Tabs -->
            <div class="border-b border-gray-200 mb-6">
                <nav class="flex space-x-8">
                    <button onclick="showAdminTab('stats')" class="admin-tab active py-2 px-1 border-b-2 border-purple-500 text-purple-600 font-medium">
                        <i class="fas fa-chart-bar <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="statsTabText"><?php echo $current_language === 'ar' ? 'الإحصائيات' : 'Statistics'; ?></span>
                    </button>
                    <button onclick="showAdminTab('students')" class="admin-tab py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium">
                        <i class="fas fa-users <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="studentsTabText"><?php echo $current_language === 'ar' ? 'بيانات الطلاب' : 'Student Data'; ?></span>
                    </button>
                </nav>
            </div>

            <!-- Statistics Tab -->
            <div id="statsTab" class="admin-content">
                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-blue-50 p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-600 text-sm font-medium"><?php echo $current_language === 'ar' ? 'إجمالي المستخدمين' : 'Total Users'; ?></p>
                                <p id="totalUsers" class="text-3xl font-bold text-blue-900">0</p>
                            </div>
                            <i class="fas fa-users text-blue-600 text-2xl"></i>
                        </div>
                    </div>

                    <div class="bg-green-50 p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-600 text-sm font-medium"><?php echo $current_language === 'ar' ? 'متوسط المعدل' : 'Average GPA'; ?></p>
                                <p id="avgGPA" class="text-3xl font-bold text-green-900">0.00</p>
                            </div>
                            <i class="fas fa-chart-line text-green-600 text-2xl"></i>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-yellow-600 text-sm font-medium"><?php echo $current_language === 'ar' ? 'الحسابات اليوم' : 'Today\'s Calculations'; ?></p>
                                <p id="todayCalculations" class="text-3xl font-bold text-yellow-900">0</p>
                            </div>
                            <i class="fas fa-calculator text-yellow-600 text-2xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Charts would go here -->
                <div class="bg-gray-50 p-6 rounded-xl">
                    <h4 class="text-lg font-semibold mb-4"><?php echo $current_language === 'ar' ? 'توزيع التقديرات' : 'Grade Distribution'; ?></h4>
                    <div id="gradeDistribution" class="grid grid-cols-4 gap-4">
                        <!-- Grade distribution will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Students Tab -->
            <div id="studentsTab" class="admin-content hidden">
                <div class="mb-4">
                    <input type="text" id="studentSearch" placeholder="<?php echo $current_language === 'ar' ? 'البحث عن طالب...' : 'Search for student...'; ?>"
                           class="w-full p-3 border border-gray-300 rounded-lg">
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full bg-white rounded-lg overflow-hidden">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase"><?php echo $current_language === 'ar' ? 'الاسم' : 'Name'; ?></th>
                                <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase"><?php echo $current_language === 'ar' ? 'الهاتف' : 'Phone'; ?></th>
                                <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase"><?php echo $current_language === 'ar' ? 'المعدل' : 'GPA'; ?></th>
                                <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase"><?php echo $current_language === 'ar' ? 'التقدير' : 'Grade'; ?></th>
                                <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase"><?php echo $current_language === 'ar' ? 'التاريخ' : 'Date'; ?></th>
                                <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase"><?php echo $current_language === 'ar' ? 'الإجراءات' : 'Actions'; ?></th>
                            </tr>
                        </thead>
                        <tbody id="studentsTableBody" class="divide-y divide-gray-200">
                            <!-- Student data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-8 text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-lg font-semibold" id="loadingText"><?php echo $current_language === 'ar' ? 'جاري المعالجة...' : 'Processing...'; ?></p>
        </div>
    </div>

    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>

    <!-- App JavaScript -->
    <script>
        // Pass PHP data to JavaScript
        window.gradingSystem = <?php echo json_encode($current_grading_system); ?>;
        window.currentLanguage = '<?php echo $current_language; ?>';

        // Override getGradeOptions method
        document.addEventListener('DOMContentLoaded', function() {
            if (window.gpaCalculator) {
                window.gpaCalculator.getGradeOptions = function(selectedGrade = '') {
                    let options = '';
                    Object.keys(window.gradingSystem.grades).forEach(grade => {
                        const gradeInfo = window.gradingSystem.grades[grade];
                        const selected = grade === selectedGrade ? 'selected' : '';
                        options += `<option value="${grade}" ${selected}>${grade} (${gradeInfo.points}) - ${gradeInfo.description}</option>`;
                    });
                    return options;
                };

                // Update existing course grade selects
                document.querySelectorAll('.course-grade').forEach(select => {
                    const currentValue = select.value;
                    select.innerHTML = `<option value="">${window.currentLanguage === 'ar' ? 'اختر التقدير' : 'Select Grade'}</option>` +
                                     window.gpaCalculator.getGradeOptions(currentValue);
                });
            }
        });
    </script>
    <script>
        // Enhanced GPA Calculator JavaScript
        class GPACalculator {
            constructor() {
                this.courses = [];
                this.currentLanguage = '<?php echo $current_language; ?>';
                this.gradingSystem = <?php echo json_encode($current_grading_system); ?>;
                this.currentResults = null;
                this.userPreferences = this.loadUserPreferences();
                this.suggestionHistory = this.loadSuggestionHistory();
                this.popupNotifications = [];
                this.inlineNotifications = [];
                this.currentPopupIndex = 0;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.addInitialCourse();
                this.checkForSharedGPA();
                this.loadSavedData();

                // Load notifications after a short delay to ensure DOM is ready
                setTimeout(() => {
                    this.loadNotifications();
                }, 500);
            }

            setupEventListeners() {
                // Add course button
                const addCourseBtn = document.getElementById('addCourseBtn');
                if (addCourseBtn) {
                    addCourseBtn.addEventListener('click', () => this.addCourse());
                }

                // Calculate button
                const calculateBtn = document.getElementById('calculateBtn');
                if (calculateBtn) {
                    calculateBtn.addEventListener('click', () => this.calculateGPA());
                }

                // Language toggle
                const languageToggle = document.getElementById('languageToggle');
                if (languageToggle) {
                    languageToggle.addEventListener('click', () => this.toggleLanguage());
                }

                // Share button
                const shareBtn = document.getElementById('shareBtn');
                if (shareBtn) {
                    shareBtn.addEventListener('click', () => this.openShareModal());
                }

                // Admin button
                const adminBtn = document.getElementById('adminBtn');
                if (adminBtn) {
                    adminBtn.addEventListener('click', () => this.openAdminModal());
                }

                // Course search
                const courseSearchInput = document.getElementById('courseSearchInput');
                if (courseSearchInput) {
                    courseSearchInput.addEventListener('input', (e) => this.searchCourses(e.target.value));
                }

                // University selector
                const universitySelect = document.getElementById('universitySelect');
                if (universitySelect) {
                    universitySelect.addEventListener('change', (e) => {
                        this.changeUniversity(e.target.value);
                    });
                }

                // Grading system selector
                const gradingSystemSelect = document.getElementById('gradingSystemSelect');
                if (gradingSystemSelect) {
                    gradingSystemSelect.addEventListener('change', (e) => {
                        this.changeGradingSystem(e.target.value);
                    });
                }

                // Calculation type selector
                const calculationType = document.getElementById('calculationType');
                if (calculationType) {
                    calculationType.addEventListener('change', (e) => {
                        this.togglePreviousGpaSection(e.target.value);
                    });
                }

                // Share form
                const shareForm = document.getElementById('shareForm');
                if (shareForm) {
                    shareForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleShareForm();
                    });
                }
            }

            addCourse(courseData = null) {
                const container = document.getElementById('coursesContainer');
                if (!container) return;

                const courseId = 'course_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

                const courseDiv = document.createElement('div');
                courseDiv.className = 'course-item bg-gray-50 rounded-lg p-4 border border-gray-200 mb-4';
                courseDiv.id = courseId;

                courseDiv.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                ${this.currentLanguage === 'ar' ? 'اسم المادة' : 'Course Name'}
                            </label>
                            <input type="text"
                                   class="course-name w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="${this.currentLanguage === 'ar' ? 'مثال: الرياضيات' : 'e.g., Mathematics'}"
                                   value="${courseData?.name || ''}">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                ${this.currentLanguage === 'ar' ? 'عدد الساعات' : 'Credit Hours'}
                            </label>
                            <input type="number"
                                   class="course-hours w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   min="1" max="12"
                                   placeholder="3"
                                   value="${courseData?.hours || ''}">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                ${this.currentLanguage === 'ar' ? 'التقدير' : 'Grade'}
                            </label>
                            <select class="course-grade w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">${this.currentLanguage === 'ar' ? 'اختر التقدير' : 'Select Grade'}</option>
                                ${this.getGradeOptions(courseData?.grade)}
                            </select>
                        </div>

                        <div class="flex justify-center">
                            <button type="button"
                                    class="remove-course bg-red-500 text-white p-2 rounded-lg hover:bg-red-600 transition-colors duration-300"
                                    onclick="gpaCalculator.removeCourse('${courseId}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;

                container.appendChild(courseDiv);

                // Add animation
                courseDiv.style.opacity = '0';
                courseDiv.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    courseDiv.style.transition = 'all 0.3s ease';
                    courseDiv.style.opacity = '1';
                    courseDiv.style.transform = 'translateY(0)';
                }, 10);
            }

            removeCourse(courseId) {
                const courseElement = document.getElementById(courseId);
                if (courseElement) {
                    courseElement.style.transition = 'all 0.3s ease';
                    courseElement.style.opacity = '0';
                    courseElement.style.transform = 'translateX(-100%)';

                    setTimeout(() => {
                        courseElement.remove();
                    }, 300);
                }
            }

            getGradeOptions(selectedGrade = '') {
                let options = '';
                if (this.gradingSystem && this.gradingSystem.grades) {
                    Object.keys(this.gradingSystem.grades).forEach(grade => {
                        const gradeInfo = this.gradingSystem.grades[grade];
                        const selected = grade === selectedGrade ? 'selected' : '';
                        options += `<option value="${grade}" ${selected}>${grade} (${gradeInfo.points}) - ${gradeInfo.description}</option>`;
                    });
                }
                return options;
            }

            collectCoursesData() {
                const coursesData = [];
                const courseElements = document.querySelectorAll('.course-item');

                courseElements.forEach(element => {
                    const name = element.querySelector('.course-name').value.trim();
                    const hours = parseInt(element.querySelector('.course-hours').value);
                    const grade = element.querySelector('.course-grade').value;

                    if (name && hours && grade) {
                        coursesData.push({ name, hours, grade });
                    }
                });

                return coursesData;
            }

            async calculateGPA() {
                const calculateBtn = document.getElementById('calculateBtn');

                // Show loading
                calculateBtn.disabled = true;
                calculateBtn.innerHTML = '<i class="fas fa-spinner fa-spin ' +
                    (this.currentLanguage === 'ar' ? 'ml-2' : 'mr-2') + '"></i>' +
                    (this.currentLanguage === 'ar' ? 'جاري الحساب...' : 'Calculating...');

                try {
                    const coursesData = this.collectCoursesData();

                    if (coursesData.length === 0) {
                        throw new Error(this.currentLanguage === 'ar' ? 'يرجى إضافة مادة واحدة على الأقل' : 'Please add at least one course');
                    }

                    const calculationType = document.getElementById('calculationType').value;
                    const previousGpa = parseFloat(document.getElementById('previousGpa')?.value || 0);
                    const previousHours = parseInt(document.getElementById('previousHours')?.value || 0);

                    const response = await fetch('index.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'calculate_gpa',
                            courses: JSON.stringify(coursesData),
                            previous_gpa: previousGpa,
                            previous_hours: previousHours
                        })
                    });

                    const result = await response.json();

                    if (result.error) {
                        throw new Error(result.error);
                    }

                    this.currentResults = result;
                    this.displayResults(result);

                } catch (error) {
                    console.error('Error calculating GPA:', error);
                    this.showAlert(error.message || 'حدث خطأ في حساب المعدل', 'error');
                } finally {
                    calculateBtn.disabled = false;
                    calculateBtn.innerHTML = '<i class="fas fa-calculator ' +
                        (this.currentLanguage === 'ar' ? 'ml-2' : 'mr-2') + '"></i>' +
                        '<span>' + (this.currentLanguage === 'ar' ? 'احسب المعدل' : 'Calculate GPA') + '</span>';
                }
            }

            displayResults(result) {
                const resultsSection = document.getElementById('resultsSection');
                const semesterGpaValue = document.getElementById('semesterGpaValue');
                const cumulativeGpaValue = document.getElementById('cumulativeGpaValue');
                const totalHoursValue = document.getElementById('totalHoursValue');

                // Update values with animation
                this.animateNumber(semesterGpaValue, result.semester_gpa || result.gpa);
                this.animateNumber(cumulativeGpaValue, result.cumulative_gpa || result.gpa);
                this.animateNumber(totalHoursValue, result.total_hours);

                // Show results with animation
                resultsSection.classList.remove('hidden');
                resultsSection.classList.add('fade-in');
                resultsSection.scrollIntoView({ behavior: 'smooth' });

                // Generate smart suggestions
                this.generateSmartSuggestions(result);
            }

            animateNumber(element, targetValue) {
                const startValue = 0;
                const duration = 1000;
                const startTime = performance.now();

                const animate = (currentTime) => {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    const currentValue = startValue + (targetValue - startValue) * progress;

                    if (element.id.includes('Gpa')) {
                        element.textContent = currentValue.toFixed(2);
                    } else {
                        element.textContent = Math.floor(currentValue);
                    }

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    }
                };

                requestAnimationFrame(animate);
            }

            generateSmartSuggestions(result) {
                const suggestionsContainer = document.getElementById('smartSuggestions');
                if (!suggestionsContainer) return;

                const gpa = result.cumulative_gpa || result.gpa;
                const totalHours = result.total_hours || 0;
                const coursesData = this.collectCoursesData();
                const suggestions = [];

                // Clear existing suggestions
                suggestionsContainer.innerHTML = '';

                // Advanced course analysis
                const courseAnalysis = this.analyzeCoursePerformance(coursesData);
                const academicInsights = this.generateAcademicInsights(gpa, totalHours, coursesData);
                const careerGuidance = this.generateCareerGuidance(gpa, totalHours);
                const studyTips = this.generateStudyTips(courseAnalysis);
                const futureProjections = this.calculateFutureProjections(gpa, totalHours, coursesData);

                // GPA-based detailed suggestions
                if (gpa >= 3.75) {
                    suggestions.push({
                        type: 'excellent',
                        icon: 'fas fa-trophy',
                        title: this.currentLanguage === 'ar' ? '🏆 أداء ممتاز!' : '🏆 Excellent Performance!',
                        message: this.currentLanguage === 'ar' ?
                            `معدلك ${gpa.toFixed(2)} ممتاز! أنت في المرتبة الأولى. حافظ على هذا المستوى وفكر في:` :
                            `Your GPA ${gpa.toFixed(2)} is excellent! You're in the top tier. Maintain this level and consider:`,
                        details: this.currentLanguage === 'ar' ? [
                            '• التقدم للمنح الدراسية والبرامج التنافسية',
                            '• الانضمام لبرامج التفوق الأكاديمي',
                            '• التفكير في الدراسات العليا',
                            '• المشاركة في الأنشطة البحثية',
                            '• التدريب في الشركات الكبرى'
                        ] : [
                            '• Apply for scholarships and competitive programs',
                            '• Join academic excellence programs',
                            '• Consider graduate studies',
                            '• Participate in research activities',
                            '• Intern at top companies'
                        ],
                        color: 'green'
                    });

                    suggestions.push({
                        type: 'course_strategy',
                        icon: 'fas fa-chess',
                        title: this.currentLanguage === 'ar' ? '📚 استراتيجية متقدمة' : '📚 Advanced Strategy',
                        message: this.currentLanguage === 'ar' ?
                            'يمكنك تحدي نفسك أكثر:' :
                            'You can challenge yourself more:',
                        details: this.currentLanguage === 'ar' ? [
                            `• تسجيل 15-18 ساعة معتمدة (حالياً ${totalHours})`,
                            '• اختيار مواد متقدمة أو اختيارية صعبة',
                            '• التخرج مبكراً في فصل أو فصلين',
                            '• دراسة تخصص فرعي إضافي',
                            '• المشاركة في مشاريع تطبيقية'
                        ] : [
                            `• Register 15-18 credit hours (currently ${totalHours})`,
                            '• Choose advanced or challenging electives',
                            '• Graduate 1-2 semesters early',
                            '• Study an additional minor',
                            '• Participate in applied projects'
                        ],
                        color: 'blue'
                    });
                } else if (gpa >= 3.5) {
                    suggestions.push({
                        type: 'very_good',
                        icon: 'fas fa-star',
                        title: this.currentLanguage === 'ar' ? '⭐ أداء ممتاز جداً!' : '⭐ Very Good Performance!',
                        message: this.currentLanguage === 'ar' ?
                            `معدلك ${gpa.toFixed(2)} ممتاز جداً! أنت قريب من التفوق المطلق:` :
                            `Your GPA ${gpa.toFixed(2)} is very good! You're close to excellence:`,
                        details: this.currentLanguage === 'ar' ? [
                            `• تحتاج ${(3.75 - gpa).toFixed(2)} نقطة للوصول للتفوق المطلق`,
                            '• ركز على المواد التي حصلت فيها على B+ أو أقل',
                            '• استهدف A في جميع المواد القادمة',
                            '• فكر في إعادة مادة واحدة بدرجة منخفضة',
                            '• ابحث عن فرص التدريب والمشاريع'
                        ] : [
                            `• Need ${(3.75 - gpa).toFixed(2)} points to reach excellence`,
                            '• Focus on courses where you got B+ or lower',
                            '• Target A grades in all upcoming courses',
                            '• Consider retaking one low-grade course',
                            '• Look for internship and project opportunities'
                        ],
                        color: 'green'
                    });
                } else if (gpa >= 3.0) {
                    suggestions.push({
                        type: 'good',
                        icon: 'fas fa-thumbs-up',
                        title: this.currentLanguage === 'ar' ? '👍 أداء جيد' : '👍 Good Performance',
                        message: this.currentLanguage === 'ar' ?
                            `معدلك ${gpa.toFixed(2)} جيد! يمكنك تحسينه بسهولة:` :
                            `Your GPA ${gpa.toFixed(2)} is good! You can improve it easily:`,
                        details: this.currentLanguage === 'ar' ? [
                            `• تحتاج ${(3.5 - gpa).toFixed(2)} نقطة للوصول للامتياز`,
                            '• ركز على المواد الأساسية في تخصصك',
                            '• احصل على مساعدة في المواد الصعبة',
                            '• انضم لمجموعات الدراسة',
                            '• استخدم ساعات مكتبية مع الأساتذة'
                        ] : [
                            `• Need ${(3.5 - gpa).toFixed(2)} points to reach very good`,
                            '• Focus on core subjects in your major',
                            '• Get help in challenging courses',
                            '• Join study groups',
                            '• Use office hours with professors'
                        ],
                        color: 'blue'
                    });

                    suggestions.push({
                        type: 'improvement_plan',
                        icon: 'fas fa-chart-line',
                        title: this.currentLanguage === 'ar' ? '📈 خطة التحسين' : '📈 Improvement Plan',
                        message: this.currentLanguage === 'ar' ?
                            'خطة عملية لرفع معدلك:' :
                            'Practical plan to raise your GPA:',
                        details: this.currentLanguage === 'ar' ? [
                            '• سجل 12-15 ساعة معتمدة للتوازن',
                            '• اختر مادة واحدة سهلة مع كل مادة صعبة',
                            '• خصص 2-3 ساعات دراسة لكل ساعة معتمدة',
                            '• راجع المواد السابقة قبل الامتحانات',
                            '• احضر جميع المحاضرات والمختبرات'
                        ] : [
                            '• Register 12-15 credit hours for balance',
                            '• Choose one easy course with each difficult one',
                            '• Dedicate 2-3 study hours per credit hour',
                            '• Review previous materials before exams',
                            '• Attend all lectures and labs'
                        ],
                        color: 'indigo'
                    });
                } else if (gpa >= 2.5) {
                    suggestions.push({
                        type: 'warning',
                        icon: 'fas fa-exclamation-triangle',
                        title: this.currentLanguage === 'ar' ? '⚠️ تحتاج لتحسين' : '⚠️ Needs Improvement',
                        message: this.currentLanguage === 'ar' ?
                            `معدلك ${gpa.toFixed(2)} يحتاج تحسين عاجل. خطة الإنقاذ:` :
                            `Your GPA ${gpa.toFixed(2)} needs urgent improvement. Recovery plan:`,
                        details: this.currentLanguage === 'ar' ? [
                            '• ركز على النجاح أولاً، ثم التفوق',
                            '• سجل 9-12 ساعة معتمدة فقط',
                            '• اختر مواد سهلة لرفع المعدل',
                            '• احصل على مساعدة أكاديمية فورية',
                            '• فكر في إعادة المواد ذات الدرجات المنخفضة'
                        ] : [
                            '• Focus on passing first, then excelling',
                            '• Register only 9-12 credit hours',
                            '• Choose easier courses to boost GPA',
                            '• Get immediate academic help',
                            '• Consider retaking low-grade courses'
                        ],
                        color: 'yellow'
                    });
                } else if (gpa >= 2.0) {
                    suggestions.push({
                        type: 'critical',
                        icon: 'fas fa-exclamation-circle',
                        title: this.currentLanguage === 'ar' ? '🚨 وضع حرج' : '🚨 Critical Situation',
                        message: this.currentLanguage === 'ar' ?
                            `معدلك ${gpa.toFixed(2)} في المنطقة الحرجة. خطة طوارئ:` :
                            `Your GPA ${gpa.toFixed(2)} is in critical zone. Emergency plan:`,
                        details: this.currentLanguage === 'ar' ? [
                            '• تواصل مع المرشد الأكاديمي فوراً',
                            '• سجل الحد الأدنى 6-9 ساعات معتمدة',
                            '• ركز على المواد الأساسية فقط',
                            '• احصل على دروس خصوصية',
                            '• فكر في تغيير التخصص إذا لزم الأمر'
                        ] : [
                            '• Contact academic advisor immediately',
                            '• Register minimum 6-9 credit hours',
                            '• Focus only on core subjects',
                            '• Get private tutoring',
                            '• Consider changing major if necessary'
                        ],
                        color: 'red'
                    });
                } else {
                    suggestions.push({
                        type: 'emergency',
                        icon: 'fas fa-life-ring',
                        title: this.currentLanguage === 'ar' ? '🆘 حالة طوارئ' : '🆘 Emergency',
                        message: this.currentLanguage === 'ar' ?
                            `معدلك ${gpa.toFixed(2)} يتطلب تدخل عاجل:` :
                            `Your GPA ${gpa.toFixed(2)} requires immediate intervention:`,
                        details: this.currentLanguage === 'ar' ? [
                            '• توقف عن التسجيل واطلب المساعدة فوراً',
                            '• تواصل مع عمادة الطلاب',
                            '• فكر في الانسحاب المؤقت للتخطيط',
                            '• احصل على استشارة نفسية وأكاديمية',
                            '• راجع إمكانية تغيير المسار التعليمي'
                        ] : [
                            '• Stop registering and seek help immediately',
                            '• Contact student affairs',
                            '• Consider temporary withdrawal for planning',
                            '• Get psychological and academic counseling',
                            '• Review possibility of changing educational path'
                        ],
                        color: 'red'
                    });
                }

                // Hours-based suggestions
                if (totalHours < 30) {
                    suggestions.push({
                        type: 'freshman',
                        icon: 'fas fa-seedling',
                        title: this.currentLanguage === 'ar' ? 'طالب جديد' : 'Freshman Student',
                        message: this.currentLanguage === 'ar' ?
                            'ركز على بناء أساس قوي في المواد الأساسية وتطوير عادات دراسية جيدة.' :
                            'Focus on building a strong foundation in core subjects and developing good study habits.',
                        color: 'purple'
                    });
                } else if (totalHours < 60) {
                    suggestions.push({
                        type: 'sophomore',
                        icon: 'fas fa-chart-line',
                        title: this.currentLanguage === 'ar' ? 'في منتصف الطريق' : 'Halfway There',
                        message: this.currentLanguage === 'ar' ?
                            'أنت في منتصف رحلتك الجامعية. فكر في التخصص والمسار المهني.' :
                            'You\'re halfway through your college journey. Think about your major and career path.',
                        color: 'indigo'
                    });
                } else if (totalHours < 90) {
                    suggestions.push({
                        type: 'junior',
                        icon: 'fas fa-rocket',
                        title: this.currentLanguage === 'ar' ? 'مرحلة متقدمة' : 'Advanced Stage',
                        message: this.currentLanguage === 'ar' ?
                            'ابدأ في التفكير في التدريب العملي والتحضير لسوق العمل.' :
                            'Start thinking about internships and preparing for the job market.',
                        color: 'teal'
                    });
                } else {
                    suggestions.push({
                        type: 'senior',
                        icon: 'fas fa-flag-checkered',
                        title: this.currentLanguage === 'ar' ? 'قريب من التخرج' : 'Near Graduation',
                        message: this.currentLanguage === 'ar' ?
                            'أنت قريب من التخرج! ركز على إنهاء المتطلبات والتحضير للمستقبل.' :
                            'You\'re close to graduation! Focus on completing requirements and preparing for the future.',
                        color: 'pink'
                    });
                }

                // Add advanced insights
                const motivationalInsights = this.generateMotivationalInsights(gpa, totalHours, coursesData);
                const personalizedTips = this.generatePersonalizedTips(gpa, totalHours, courseAnalysis);
                const aiPoweredRecommendations = this.generateAIPoweredRecommendations(gpa, totalHours, coursesData, courseAnalysis);
                const progressTracking = this.generateProgressTracking(gpa, totalHours);
                const trendAnalysis = this.generateTrendAnalysis(gpa, totalHours);
                const smartInsights = this.generateSmartInsights(coursesData, courseAnalysis);

                // Combine all suggestions
                suggestions.push(...academicInsights);
                suggestions.push(...careerGuidance);
                suggestions.push(...studyTips);
                suggestions.push(...futureProjections);
                suggestions.push(...motivationalInsights);
                suggestions.push(...personalizedTips);
                suggestions.push(...aiPoweredRecommendations);
                suggestions.push(...progressTracking);
                suggestions.push(...trendAnalysis);
                suggestions.push(...smartInsights);

                // Customize suggestions based on user preferences (simplified)
                let customizedSuggestions = suggestions.filter(suggestion => {
                    // Only filter out dismissed suggestions
                    const recentlyDismissed = this.userPreferences.dismissedSuggestions.some(dismissed =>
                        dismissed.type === suggestion.type &&
                        new Date(dismissed.timestamp) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                    );
                    return !recentlyDismissed;
                });

                // If no suggestions after filtering, show all original suggestions
                if (customizedSuggestions.length === 0) {
                    customizedSuggestions = suggestions;
                }

                // Sort suggestions by priority
                customizedSuggestions.sort((a, b) => {
                    const priorityOrder = { 'عاجل': 4, 'Urgent': 4, 'أولوية عالية': 3, 'High Priority': 3, 'مهم جداً': 2, 'Very Important': 2, 'مهم': 1, 'Important': 1 };
                    return (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
                });

                // Log suggestion generation for learning
                this.suggestionHistory.push({
                    timestamp: new Date().toISOString(),
                    gpa: gpa,
                    totalHours: totalHours,
                    suggestionsGenerated: customizedSuggestions.length,
                    types: customizedSuggestions.map(s => s.type)
                });
                this.saveSuggestionHistory();

                // Add header for suggestions section
                if (customizedSuggestions.length > 0) {
                    const headerDiv = document.createElement('div');
                    headerDiv.className = 'mb-6 text-center';
                    headerDiv.innerHTML = `
                        <div class="inline-flex items-center space-x-2 space-x-reverse bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-full shadow-lg">
                            <i class="fas fa-brain text-xl"></i>
                            <h3 class="text-lg font-bold">${this.currentLanguage === 'ar' ? '🤖 اقتراحات ذكية مخصصة لك' : '🤖 Smart Personalized Suggestions'}</h3>
                        </div>
                        <p class="text-gray-600 mt-3 text-sm">${this.currentLanguage === 'ar' ? 'تحليل متقدم لأدائك الأكاديمي مع نصائح مخصصة لتحقيق أهدافك' : 'Advanced analysis of your academic performance with personalized tips to achieve your goals'}</p>
                    `;
                    suggestionsContainer.appendChild(headerDiv);
                }

                // Display enhanced suggestions with expandable details
                customizedSuggestions.slice(0, 8).forEach((suggestion, index) => {
                    const suggestionDiv = document.createElement('div');
                    suggestionDiv.className = `suggestion-item bg-${suggestion.color}-50 border border-${suggestion.color}-200 rounded-lg p-4 transition-all duration-300 hover:shadow-lg cursor-pointer`;
                    suggestionDiv.style.animationDelay = `${index * 0.1}s`;

                    const hasDetails = suggestion.details && suggestion.details.length > 0;
                    const detailsId = `details_${index}_${Date.now()}`;

                    suggestionDiv.innerHTML = `
                        <div class="flex items-start space-x-3 space-x-reverse">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-${suggestion.color}-100 rounded-full flex items-center justify-center">
                                    <i class="${suggestion.icon} text-${suggestion.color}-600 text-lg"></i>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-bold text-sm text-gray-800 mb-1">${suggestion.title}</h4>
                                    ${hasDetails ? `<i class="fas fa-chevron-down text-${suggestion.color}-600 text-xs transition-transform duration-200" id="chevron_${detailsId}"></i>` : ''}
                                </div>
                                <p class="text-xs text-gray-600 mb-2">${suggestion.message}</p>
                                ${suggestion.priority ? `<span class="inline-block px-2 py-1 text-xs font-medium bg-${suggestion.color}-200 text-${suggestion.color}-800 rounded-full">${suggestion.priority}</span>` : ''}
                                ${hasDetails ? `
                                    <div id="${detailsId}" class="hidden mt-3 pt-3 border-t border-${suggestion.color}-200">
                                        <ul class="space-y-1">
                                            ${suggestion.details.map(detail => `<li class="text-xs text-gray-700 flex items-start"><span class="text-${suggestion.color}-500 mr-2">•</span>${detail}</li>`).join('')}
                                        </ul>
                                        ${suggestion.actionButton ? `
                                            <button class="mt-3 px-3 py-1 text-xs bg-${suggestion.color}-600 text-white rounded-md hover:bg-${suggestion.color}-700 transition-colors" onclick="window.gpaCalculator.handleSuggestionAction('${suggestion.type}')">
                                                ${suggestion.actionButton}
                                            </button>
                                        ` : ''}
                                        <div class="mt-3 pt-3 border-t border-${suggestion.color}-200">
                                            <div class="flex items-center justify-between">
                                                <span class="text-xs text-gray-500">${this.currentLanguage === 'ar' ? 'هل كان هذا الاقتراح مفيداً؟' : 'Was this suggestion helpful?'}</span>
                                                <div class="flex space-x-1 space-x-reverse">
                                                    <button onclick="window.gpaCalculator.rateSuggestion('${suggestion.type}', 'helpful')" class="text-green-500 hover:text-green-700 text-xs">
                                                        <i class="fas fa-thumbs-up"></i>
                                                    </button>
                                                    <button onclick="window.gpaCalculator.rateSuggestion('${suggestion.type}', 'not_helpful')" class="text-red-500 hover:text-red-700 text-xs">
                                                        <i class="fas fa-thumbs-down"></i>
                                                    </button>
                                                    <button onclick="window.gpaCalculator.dismissSuggestion('${suggestion.type}')" class="text-gray-400 hover:text-gray-600 text-xs ml-2" title="${this.currentLanguage === 'ar' ? 'إخفاء هذا النوع من الاقتراحات' : 'Hide this type of suggestions'}">
                                                        <i class="fas fa-eye-slash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `;

                    // Add click handler for expandable details
                    if (hasDetails) {
                        suggestionDiv.addEventListener('click', () => {
                            const detailsElement = document.getElementById(detailsId);
                            const chevron = document.getElementById(`chevron_${detailsId}`);

                            if (detailsElement.classList.contains('hidden')) {
                                detailsElement.classList.remove('hidden');
                                detailsElement.classList.add('slide-down');
                                chevron.style.transform = 'rotate(180deg)';
                            } else {
                                detailsElement.classList.add('hidden');
                                detailsElement.classList.remove('slide-down');
                                chevron.style.transform = 'rotate(0deg)';
                            }
                        });
                    }

                    suggestionDiv.classList.add('fade-in-up');
                    suggestionsContainer.appendChild(suggestionDiv);
                });

                // Show message if no suggestions
                if (customizedSuggestions.length === 0) {
                    const noSuggestionsDiv = document.createElement('div');
                    noSuggestionsDiv.className = 'text-center py-8';
                    noSuggestionsDiv.innerHTML = `
                        <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 border border-blue-200">
                            <i class="fas fa-lightbulb text-6xl text-blue-400 mb-4"></i>
                            <h3 class="text-xl font-bold text-gray-800 mb-2">
                                ${this.currentLanguage === 'ar' ? '🎯 جاهز للاقتراحات الذكية!' : '🎯 Ready for Smart Suggestions!'}
                            </h3>
                            <p class="text-gray-600 mb-4">
                                ${this.currentLanguage === 'ar' ?
                                    'احسب معدلك أولاً للحصول على اقتراحات ذكية ومخصصة لتحسين أدائك الأكاديمي' :
                                    'Calculate your GPA first to get smart and personalized suggestions to improve your academic performance'
                                }
                            </p>
                            <div class="flex justify-center space-x-4 space-x-reverse text-sm text-gray-500">
                                <div class="flex items-center">
                                    <i class="fas fa-brain text-purple-500 mr-2"></i>
                                    <span>${this.currentLanguage === 'ar' ? 'تحليل ذكي' : 'Smart Analysis'}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-target text-blue-500 mr-2"></i>
                                    <span>${this.currentLanguage === 'ar' ? 'نصائح مخصصة' : 'Personalized Tips'}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-chart-line text-green-500 mr-2"></i>
                                    <span>${this.currentLanguage === 'ar' ? 'تتبع التقدم' : 'Progress Tracking'}</span>
                                </div>
                            </div>
                        </div>
                    `;
                    suggestionsContainer.appendChild(noSuggestionsDiv);
                }

                // Show suggestions section
                setTimeout(() => {
                    suggestionsContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }, 1000);
            }

            // Advanced analysis functions
            analyzeCoursePerformance(coursesData) {
                const analysis = {
                    totalCourses: coursesData.length,
                    averageHours: 0,
                    gradeDistribution: {},
                    weakAreas: [],
                    strongAreas: [],
                    improvementPotential: 0
                };

                if (coursesData.length === 0) return analysis;

                // Calculate average credit hours
                analysis.averageHours = coursesData.reduce((sum, course) => sum + course.hours, 0) / coursesData.length;

                // Analyze grade distribution
                coursesData.forEach(course => {
                    const grade = course.grade;
                    analysis.gradeDistribution[grade] = (analysis.gradeDistribution[grade] || 0) + 1;

                    // Identify weak and strong areas
                    const gradePoints = this.gradingSystem.grades[grade]?.points || 0;
                    if (gradePoints >= 3.5) {
                        analysis.strongAreas.push(course.name);
                    } else if (gradePoints < 2.5) {
                        analysis.weakAreas.push(course.name);
                    }
                });

                // Calculate improvement potential
                const lowGrades = coursesData.filter(course => {
                    const gradePoints = this.gradingSystem.grades[course.grade]?.points || 0;
                    return gradePoints < 3.0;
                });
                analysis.improvementPotential = lowGrades.length;

                return analysis;
            }

            generateAcademicInsights(gpa, totalHours, coursesData) {
                const insights = [];
                const analysis = this.analyzeCoursePerformance(coursesData);

                // GPA trend analysis
                if (gpa >= 3.75) {
                    insights.push({
                        type: 'academic_excellence',
                        icon: 'fas fa-medal',
                        title: this.currentLanguage === 'ar' ? '🏅 تحليل التفوق الأكاديمي' : '🏅 Academic Excellence Analysis',
                        message: this.currentLanguage === 'ar' ?
                            `أداؤك الأكاديمي متميز! معدلك ${gpa.toFixed(2)} يضعك في المرتبة الأولى` :
                            `Your academic performance is outstanding! Your GPA ${gpa.toFixed(2)} puts you in the top tier`,
                        details: this.currentLanguage === 'ar' ? [
                            `تفوقت في ${analysis.strongAreas.length} مادة من أصل ${analysis.totalCourses}`,
                            `متوسط الساعات المعتمدة: ${analysis.averageHours.toFixed(1)} ساعة`,
                            'أنت مؤهل للمنح الدراسية والبرامج التنافسية',
                            'فكر في الانضمام لبرامج الشرف الأكاديمي',
                            'يمكنك التقدم للدراسات العليا في أفضل الجامعات'
                        ] : [
                            `Excelled in ${analysis.strongAreas.length} out of ${analysis.totalCourses} courses`,
                            `Average credit hours: ${analysis.averageHours.toFixed(1)} hours`,
                            'You qualify for scholarships and competitive programs',
                            'Consider joining academic honor programs',
                            'You can apply for graduate studies at top universities'
                        ],
                        priority: this.currentLanguage === 'ar' ? 'أولوية عالية' : 'High Priority',
                        color: 'green',
                        actionButton: this.currentLanguage === 'ar' ? 'استكشف الفرص' : 'Explore Opportunities'
                    });
                }

                // Course load analysis
                if (analysis.averageHours < 3) {
                    insights.push({
                        type: 'course_load',
                        icon: 'fas fa-chart-bar',
                        title: this.currentLanguage === 'ar' ? '📊 تحليل الحمل الدراسي' : '📊 Course Load Analysis',
                        message: this.currentLanguage === 'ar' ?
                            'متوسط ساعاتك المعتمدة منخفض، يمكنك زيادة التحدي' :
                            'Your average credit hours is low, you can increase the challenge',
                        details: this.currentLanguage === 'ar' ? [
                            `متوسط ساعاتك الحالي: ${analysis.averageHours.toFixed(1)} ساعة`,
                            'الحد الأدنى الموصى به: 3 ساعات لكل مادة',
                            'فكر في تسجيل مواد بساعات أكثر',
                            'هذا سيساعدك على التخرج بشكل أسرع',
                            'ستكتسب خبرة أكاديمية أوسع'
                        ] : [
                            `Your current average: ${analysis.averageHours.toFixed(1)} hours`,
                            'Recommended minimum: 3 hours per course',
                            'Consider registering for courses with more hours',
                            'This will help you graduate faster',
                            'You will gain broader academic experience'
                        ],
                        color: 'blue'
                    });
                }

                return insights;
            }

            generateCareerGuidance(gpa, totalHours) {
                const guidance = [];

                // Career readiness based on academic progress
                if (totalHours >= 90) {
                    guidance.push({
                        type: 'career_preparation',
                        icon: 'fas fa-briefcase',
                        title: this.currentLanguage === 'ar' ? '💼 الاستعداد المهني' : '💼 Career Preparation',
                        message: this.currentLanguage === 'ar' ?
                            'أنت في المرحلة الأخيرة، حان وقت التحضير لسوق العمل' :
                            'You\'re in the final stage, time to prepare for the job market',
                        details: this.currentLanguage === 'ar' ? [
                            'ابدأ في كتابة سيرتك الذاتية المهنية',
                            'ابحث عن فرص التدريب في الشركات',
                            'احضر معارض التوظيف والفعاليات المهنية',
                            'طور مهاراتك في المقابلات الشخصية',
                            'ابني شبكة علاقات مهنية قوية',
                            'فكر في الحصول على شهادات مهنية إضافية'
                        ] : [
                            'Start writing your professional resume',
                            'Look for internship opportunities in companies',
                            'Attend job fairs and professional events',
                            'Develop your interview skills',
                            'Build a strong professional network',
                            'Consider getting additional professional certifications'
                        ],
                        priority: this.currentLanguage === 'ar' ? 'عاجل' : 'Urgent',
                        color: 'purple',
                        actionButton: this.currentLanguage === 'ar' ? 'خطة العمل' : 'Action Plan'
                    });
                } else if (totalHours >= 60) {
                    guidance.push({
                        type: 'skill_development',
                        icon: 'fas fa-tools',
                        title: this.currentLanguage === 'ar' ? '🛠️ تطوير المهارات' : '🛠️ Skill Development',
                        message: this.currentLanguage === 'ar' ?
                            'الوقت مناسب لتطوير مهارات إضافية وبناء الخبرة' :
                            'Perfect time to develop additional skills and build experience',
                        details: this.currentLanguage === 'ar' ? [
                            'تعلم مهارات تقنية جديدة ذات صلة بتخصصك',
                            'ابحث عن مشاريع تطبيقية للعمل عليها',
                            'انضم لفرق طلابية أو مجتمعات مهنية',
                            'احضر ورش عمل ودورات تدريبية',
                            'ابدأ في بناء معرض أعمال (Portfolio)',
                            'فكر في التطوع في مجال تخصصك'
                        ] : [
                            'Learn new technical skills related to your major',
                            'Look for practical projects to work on',
                            'Join student teams or professional communities',
                            'Attend workshops and training courses',
                            'Start building a portfolio',
                            'Consider volunteering in your field'
                        ],
                        color: 'indigo'
                    });
                }

                return guidance;
            }

            generateStudyTips(courseAnalysis) {
                const tips = [];

                // Study strategy based on performance
                if (courseAnalysis.weakAreas.length > 0) {
                    tips.push({
                        type: 'improvement_strategy',
                        icon: 'fas fa-lightbulb',
                        title: this.currentLanguage === 'ar' ? '💡 استراتيجية التحسين' : '💡 Improvement Strategy',
                        message: this.currentLanguage === 'ar' ?
                            `لديك ${courseAnalysis.weakAreas.length} مواد تحتاج تحسين، إليك خطة عمل` :
                            `You have ${courseAnalysis.weakAreas.length} courses that need improvement, here's an action plan`,
                        details: this.currentLanguage === 'ar' ? [
                            'حدد أوقات دراسة ثابتة يومياً لهذه المواد',
                            'استخدم تقنيات الدراسة النشطة (ملخصات، خرائط ذهنية)',
                            'شكل مجموعات دراسية مع زملائك المتفوقين',
                            'اطلب المساعدة من الأساتذة في ساعاتهم المكتبية',
                            'استخدم مصادر تعليمية إضافية (فيديوهات، كتب)',
                            'مارس حل الأسئلة والتمارين بانتظام',
                            'راجع المادة بشكل دوري وليس قبل الامتحان فقط'
                        ] : [
                            'Set fixed daily study times for these courses',
                            'Use active study techniques (summaries, mind maps)',
                            'Form study groups with high-achieving classmates',
                            'Seek help from professors during office hours',
                            'Use additional educational resources (videos, books)',
                            'Practice solving questions and exercises regularly',
                            'Review material periodically, not just before exams'
                        ],
                        priority: this.currentLanguage === 'ar' ? 'مهم جداً' : 'Very Important',
                        color: 'orange',
                        actionButton: this.currentLanguage === 'ar' ? 'ابدأ الآن' : 'Start Now'
                    });
                }

                // Time management tips
                if (courseAnalysis.totalCourses >= 5) {
                    tips.push({
                        type: 'time_management',
                        icon: 'fas fa-clock',
                        title: this.currentLanguage === 'ar' ? '⏰ إدارة الوقت الذكية' : '⏰ Smart Time Management',
                        message: this.currentLanguage === 'ar' ?
                            'مع عدد المواد الكبير، إدارة الوقت أمر بالغ الأهمية' :
                            'With a large number of courses, time management is crucial',
                        details: this.currentLanguage === 'ar' ? [
                            'استخدم تقنية البومودورو (25 دقيقة دراسة + 5 دقائق راحة)',
                            'أنشئ جدولاً أسبوعياً للدراسة وتوزيع المواد',
                            'حدد أولويات المهام حسب تواريخ التسليم والأهمية',
                            'خصص وقتاً للمراجعة السريعة يومياً',
                            'استخدم تطبيقات إدارة الوقت والمهام',
                            'تجنب التأجيل والمماطلة',
                            'احرص على أخذ فترات راحة منتظمة'
                        ] : [
                            'Use the Pomodoro technique (25 min study + 5 min break)',
                            'Create a weekly study schedule and distribute subjects',
                            'Prioritize tasks by deadlines and importance',
                            'Allocate time for daily quick reviews',
                            'Use time and task management apps',
                            'Avoid procrastination and delays',
                            'Make sure to take regular breaks'
                        ],
                        color: 'teal'
                    });
                }

                return tips;
            }

            calculateFutureProjections(gpa, totalHours, coursesData) {
                const projections = [];

                // GPA projection for graduation
                const remainingHours = Math.max(0, 120 - totalHours); // Assuming 120 hours for graduation

                if (remainingHours > 0) {
                    // Calculate different scenarios
                    const scenarios = [
                        { targetGPA: 4.0, label: this.currentLanguage === 'ar' ? 'ممتاز' : 'Excellent' },
                        { targetGPA: 3.5, label: this.currentLanguage === 'ar' ? 'جيد جداً' : 'Very Good' },
                        { targetGPA: 3.0, label: this.currentLanguage === 'ar' ? 'جيد' : 'Good' }
                    ];

                    const currentPoints = gpa * totalHours;

                    scenarios.forEach(scenario => {
                        const requiredPoints = scenario.targetGPA * 120; // Total points needed
                        const remainingPoints = requiredPoints - currentPoints;
                        const requiredGPA = remainingPoints / remainingHours;

                        if (requiredGPA >= 0 && requiredGPA <= 4.0) {
                            projections.push({
                                type: 'gpa_projection',
                                icon: 'fas fa-chart-line',
                                title: this.currentLanguage === 'ar' ? '📈 توقعات المعدل' : '📈 GPA Projections',
                                message: this.currentLanguage === 'ar' ?
                                    `للوصول لمعدل ${scenario.targetGPA} عند التخرج، تحتاج معدل ${requiredGPA.toFixed(2)} في المواد المتبقية` :
                                    `To reach GPA ${scenario.targetGPA} at graduation, you need ${requiredGPA.toFixed(2)} in remaining courses`,
                                details: this.currentLanguage === 'ar' ? [
                                    `معدلك الحالي: ${gpa.toFixed(2)}`,
                                    `الساعات المكتملة: ${totalHours} ساعة`,
                                    `الساعات المتبقية: ${remainingHours} ساعة`,
                                    `المعدل المطلوب في المواد المتبقية: ${requiredGPA.toFixed(2)}`,
                                    `التصنيف المتوقع: ${scenario.label}`,
                                    requiredGPA <= 3.0 ? 'هدف قابل للتحقيق بسهولة' :
                                    requiredGPA <= 3.5 ? 'هدف قابل للتحقيق مع الجهد' : 'هدف يتطلب جهداً استثنائياً'
                                ] : [
                                    `Current GPA: ${gpa.toFixed(2)}`,
                                    `Completed hours: ${totalHours} hours`,
                                    `Remaining hours: ${remainingHours} hours`,
                                    `Required GPA in remaining courses: ${requiredGPA.toFixed(2)}`,
                                    `Expected classification: ${scenario.label}`,
                                    requiredGPA <= 3.0 ? 'Easily achievable goal' :
                                    requiredGPA <= 3.5 ? 'Achievable goal with effort' : 'Goal requires exceptional effort'
                                ],
                                color: requiredGPA <= 3.0 ? 'green' : requiredGPA <= 3.5 ? 'yellow' : 'red'
                            });
                        }
                    });
                }

                // Graduation timeline
                if (totalHours >= 90) {
                    const semestersLeft = Math.ceil(remainingHours / 15); // Assuming 15 hours per semester
                    projections.push({
                        type: 'graduation_timeline',
                        icon: 'fas fa-calendar-alt',
                        title: this.currentLanguage === 'ar' ? '🎓 خطة التخرج' : '🎓 Graduation Plan',
                        message: this.currentLanguage === 'ar' ?
                            `متوقع تخرجك خلال ${semestersLeft} فصل دراسي` :
                            `Expected graduation in ${semestersLeft} semesters`,
                        details: this.currentLanguage === 'ar' ? [
                            `الساعات المتبقية: ${remainingHours} ساعة`,
                            `الفصول المتبقية: ${semestersLeft} فصل`,
                            'تأكد من استكمال جميع المتطلبات الأساسية',
                            'راجع خطة التخرج مع المرشد الأكاديمي',
                            'فكر في التسجيل في الفصل الصيفي لتسريع التخرج',
                            'ابدأ التحضير لمشروع التخرج إن وجد'
                        ] : [
                            `Remaining hours: ${remainingHours} hours`,
                            `Remaining semesters: ${semestersLeft} semesters`,
                            'Make sure to complete all core requirements',
                            'Review graduation plan with academic advisor',
                            'Consider summer registration to accelerate graduation',
                            'Start preparing for graduation project if applicable'
                        ],
                        priority: this.currentLanguage === 'ar' ? 'مهم' : 'Important',
                        color: 'blue'
                    });
                }

                return projections;
            }

            generateMotivationalInsights(gpa, totalHours, coursesData) {
                const insights = [];
                const completionPercentage = (totalHours / 120) * 100; // Assuming 120 hours total

                // Progress celebration
                if (completionPercentage >= 75) {
                    insights.push({
                        type: 'progress_celebration',
                        icon: 'fas fa-trophy',
                        title: this.currentLanguage === 'ar' ? '🏆 إنجاز رائع!' : '🏆 Amazing Achievement!',
                        message: this.currentLanguage === 'ar' ?
                            `أكملت ${completionPercentage.toFixed(0)}% من رحلتك الأكاديمية! أنت قريب جداً من الهدف` :
                            `You've completed ${completionPercentage.toFixed(0)}% of your academic journey! You're very close to the goal`,
                        details: this.currentLanguage === 'ar' ? [
                            'لقد قطعت شوطاً طويلاً في رحلتك الأكاديمية',
                            'إنجازك هذا يستحق الاحتفال والفخر',
                            'أنت مثال يحتذى به للطلاب الآخرين',
                            'استمر بنفس الوتيرة والتفاني',
                            'النجاح أصبح على بُعد خطوات قليلة',
                            'تذكر أن كل ساعة دراسة كانت استثماراً في مستقبلك'
                        ] : [
                            'You\'ve come a long way in your academic journey',
                            'This achievement deserves celebration and pride',
                            'You\'re a role model for other students',
                            'Keep up the same pace and dedication',
                            'Success is just a few steps away',
                            'Remember that every study hour was an investment in your future'
                        ],
                        color: 'yellow',
                        priority: this.currentLanguage === 'ar' ? 'تحفيزي' : 'Motivational'
                    });
                }

                // Resilience message for struggling students
                if (gpa < 2.5 && totalHours >= 30) {
                    insights.push({
                        type: 'resilience_boost',
                        icon: 'fas fa-heart',
                        title: this.currentLanguage === 'ar' ? '💪 القوة والمثابرة' : '💪 Strength and Perseverance',
                        message: this.currentLanguage === 'ar' ?
                            'التحديات جزء من رحلة النجاح. كل عثرة هي فرصة للنهوض أقوى' :
                            'Challenges are part of the success journey. Every setback is an opportunity to rise stronger',
                        details: this.currentLanguage === 'ar' ? [
                            'النجاح ليس عدم الوقوع، بل القدرة على النهوض',
                            'كل طالب ناجح مر بلحظات صعبة مثل هذه',
                            'معدلك الحالي لا يحدد قدراتك الحقيقية',
                            'التحسن التدريجي أفضل من الكمال المؤقت',
                            'اطلب المساعدة - هذا دليل قوة وليس ضعف',
                            'ركز على التقدم وليس على الكمال',
                            'غداً فرصة جديدة لتحقيق أهدافك'
                        ] : [
                            'Success isn\'t about not falling, but about the ability to get back up',
                            'Every successful student went through difficult moments like these',
                            'Your current GPA doesn\'t define your true capabilities',
                            'Gradual improvement is better than temporary perfection',
                            'Ask for help - it\'s a sign of strength, not weakness',
                            'Focus on progress, not perfection',
                            'Tomorrow is a new opportunity to achieve your goals'
                        ],
                        color: 'pink',
                        priority: this.currentLanguage === 'ar' ? 'مهم جداً' : 'Very Important'
                    });
                }

                return insights;
            }

            generatePersonalizedTips(gpa, totalHours, courseAnalysis) {
                const tips = [];

                // Study habit recommendations based on performance
                if (courseAnalysis.gradeDistribution) {
                    const grades = Object.keys(courseAnalysis.gradeDistribution);
                    const hasConsistentPerformance = grades.length <= 3;

                    if (!hasConsistentPerformance) {
                        tips.push({
                            type: 'consistency_improvement',
                            icon: 'fas fa-chart-bar',
                            title: this.currentLanguage === 'ar' ? '📊 تحسين الثبات الأكاديمي' : '📊 Academic Consistency Improvement',
                            message: this.currentLanguage === 'ar' ?
                                'أداؤك متذبذب بين المواد. إليك استراتيجية للحصول على نتائج أكثر ثباتاً' :
                                'Your performance varies between subjects. Here\'s a strategy for more consistent results',
                            details: this.currentLanguage === 'ar' ? [
                                'حدد نمط الدراسة الذي يناسبك أكثر (بصري، سمعي، حركي)',
                                'طبق نفس استراتيجية الدراسة على جميع المواد',
                                'خصص وقتاً متساوياً لكل مادة أسبوعياً',
                                'استخدم تقنيات المراجعة المتباعدة',
                                'اربط المفاهيم الجديدة بما تعرفه مسبقاً',
                                'مارس الاختبارات التجريبية بانتظام',
                                'احتفظ بسجل لتتبع تقدمك في كل مادة'
                            ] : [
                                'Identify your preferred learning style (visual, auditory, kinesthetic)',
                                'Apply the same study strategy to all subjects',
                                'Allocate equal weekly time to each subject',
                                'Use spaced repetition techniques',
                                'Connect new concepts to what you already know',
                                'Practice mock tests regularly',
                                'Keep a record to track your progress in each subject'
                            ],
                            color: 'indigo'
                        });
                    }
                }

                // Stress management for heavy course loads
                if (courseAnalysis.totalCourses >= 6) {
                    tips.push({
                        type: 'stress_management',
                        icon: 'fas fa-spa',
                        title: this.currentLanguage === 'ar' ? '🧘 إدارة الضغط النفسي' : '🧘 Stress Management',
                        message: this.currentLanguage === 'ar' ?
                            'مع الحمل الدراسي الثقيل، من المهم الاعتناء بصحتك النفسية' :
                            'With a heavy course load, it\'s important to take care of your mental health',
                        details: this.currentLanguage === 'ar' ? [
                            'مارس تمارين التنفس العميق لمدة 5 دقائق يومياً',
                            'خصص وقتاً للأنشطة التي تستمتع بها',
                            'احرص على النوم 7-8 ساعات يومياً',
                            'مارس الرياضة أو المشي لمدة 30 دقيقة يومياً',
                            'تناول وجبات صحية ومتوازنة',
                            'تواصل مع الأصدقاء والعائلة بانتظام',
                            'لا تتردد في طلب المساعدة المهنية عند الحاجة'
                        ] : [
                            'Practice deep breathing exercises for 5 minutes daily',
                            'Allocate time for activities you enjoy',
                            'Ensure 7-8 hours of sleep daily',
                            'Exercise or walk for 30 minutes daily',
                            'Eat healthy and balanced meals',
                            'Stay connected with friends and family regularly',
                            'Don\'t hesitate to seek professional help when needed'
                        ],
                        color: 'green'
                    });
                }

                return tips;
            }

            handleSuggestionAction(suggestionType) {
                // Handle different suggestion actions
                switch(suggestionType) {
                    case 'academic_excellence':
                        this.showOpportunitiesModal();
                        break;
                    case 'career_preparation':
                        this.showCareerPlanModal();
                        break;
                    case 'improvement_strategy':
                        this.showStudyPlanModal();
                        break;
                    case 'gpa_projection':
                        this.showGPACalculatorModal();
                        break;
                    default:
                        this.showAlert(this.currentLanguage === 'ar' ? 'ميزة قيد التطوير' : 'Feature under development', 'info');
                }
            }

            rateSuggestion(suggestionType, rating) {
                // Store user feedback for improving suggestions
                const feedback = {
                    type: suggestionType,
                    rating: rating,
                    timestamp: new Date().toISOString(),
                    gpa: this.currentResults?.cumulative_gpa || this.currentResults?.gpa,
                    totalHours: this.currentResults?.total_hours || 0
                };

                // Store in localStorage for now (could be sent to server)
                const existingFeedback = JSON.parse(localStorage.getItem('suggestionFeedback') || '[]');
                existingFeedback.push(feedback);
                localStorage.setItem('suggestionFeedback', JSON.stringify(existingFeedback));

                // Show thank you message
                this.showAlert(
                    this.currentLanguage === 'ar' ? 'شكراً لك! ملاحظاتك تساعدنا في تحسين الاقتراحات' : 'Thank you! Your feedback helps us improve suggestions',
                    'success'
                );

                // Optionally send to server for analytics
                this.sendFeedbackToServer(feedback);
            }

            async sendFeedbackToServer(feedback) {
                try {
                    // This could be implemented to send feedback to server for analysis
                    console.log('Suggestion feedback:', feedback);
                } catch (error) {
                    console.error('Error sending feedback:', error);
                }
            }

            showOpportunitiesModal() {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-2xl p-8 max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-2xl font-bold text-gray-800">${this.currentLanguage === 'ar' ? '🌟 فرص التفوق الأكاديمي' : '🌟 Academic Excellence Opportunities'}</h3>
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <div class="space-y-6">
                            <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
                                <h4 class="font-bold text-lg mb-3">${this.currentLanguage === 'ar' ? '🎓 المنح الدراسية' : '🎓 Scholarships'}</h4>
                                <ul class="space-y-2 text-sm">
                                    ${this.currentLanguage === 'ar' ? `
                                        <li>• منحة التفوق الأكاديمي للطلاب المتميزين</li>
                                        <li>• منح الجامعات الدولية للدراسات العليا</li>
                                        <li>• برامج التبادل الطلابي</li>
                                        <li>• منح البحث العلمي للطلاب المتفوقين</li>
                                    ` : `
                                        <li>• Academic Excellence Scholarship for Outstanding Students</li>
                                        <li>• International University Scholarships for Graduate Studies</li>
                                        <li>• Student Exchange Programs</li>
                                        <li>• Research Scholarships for High Achievers</li>
                                    `}
                                </ul>
                            </div>
                            <div class="bg-gradient-to-r from-green-50 to-teal-50 p-6 rounded-lg">
                                <h4 class="font-bold text-lg mb-3">${this.currentLanguage === 'ar' ? '🏆 برامج الشرف' : '🏆 Honor Programs'}</h4>
                                <ul class="space-y-2 text-sm">
                                    ${this.currentLanguage === 'ar' ? `
                                        <li>• برنامج الشرف الأكاديمي</li>
                                        <li>• نادي الطلاب المتفوقين</li>
                                        <li>• برامج القيادة الطلابية</li>
                                        <li>• مشاريع البحث التطبيقي</li>
                                    ` : `
                                        <li>• Academic Honor Program</li>
                                        <li>• Outstanding Students Club</li>
                                        <li>• Student Leadership Programs</li>
                                        <li>• Applied Research Projects</li>
                                    `}
                                </ul>
                            </div>
                        </div>
                        <div class="mt-6 text-center">
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                                ${this.currentLanguage === 'ar' ? 'فهمت' : 'Got it'}
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }

            generateAIPoweredRecommendations(gpa, totalHours, coursesData, courseAnalysis) {
                const recommendations = [];

                // AI-powered course selection recommendations
                if (totalHours < 90) {
                    const recommendedCourses = this.getRecommendedCourses(gpa, courseAnalysis);
                    recommendations.push({
                        type: 'ai_course_recommendations',
                        icon: 'fas fa-robot',
                        title: this.currentLanguage === 'ar' ? '🤖 توصيات ذكية للمواد' : '🤖 Smart Course Recommendations',
                        message: this.currentLanguage === 'ar' ?
                            'بناءً على تحليل أدائك، إليك أفضل المواد المقترحة للفصل القادم' :
                            'Based on your performance analysis, here are the best suggested courses for next semester',
                        details: recommendedCourses,
                        color: 'purple',
                        priority: this.currentLanguage === 'ar' ? 'ذكي' : 'Smart',
                        actionButton: this.currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'
                    });
                }

                // Learning pattern analysis
                const learningPattern = this.analyzeLearningPattern(coursesData);
                if (learningPattern.insights.length > 0) {
                    recommendations.push({
                        type: 'learning_pattern',
                        icon: 'fas fa-brain',
                        title: this.currentLanguage === 'ar' ? '🧠 تحليل نمط التعلم' : '🧠 Learning Pattern Analysis',
                        message: this.currentLanguage === 'ar' ?
                            'اكتشفنا نمط تعلمك المفضل وإليك كيفية الاستفادة منه' :
                            'We discovered your preferred learning pattern and how to leverage it',
                        details: learningPattern.insights,
                        color: 'cyan',
                        actionButton: this.currentLanguage === 'ar' ? 'تطبيق النصائح' : 'Apply Tips'
                    });
                }

                // Predictive success indicators
                const successPrediction = this.predictAcademicSuccess(gpa, totalHours, courseAnalysis);
                recommendations.push({
                    type: 'success_prediction',
                    icon: 'fas fa-crystal-ball',
                    title: this.currentLanguage === 'ar' ? '🔮 توقعات النجاح' : '🔮 Success Predictions',
                    message: this.currentLanguage === 'ar' ?
                        `احتمالية تحقيق أهدافك الأكاديمية: ${successPrediction.probability}%` :
                        `Probability of achieving your academic goals: ${successPrediction.probability}%`,
                    details: successPrediction.factors,
                    color: successPrediction.probability >= 80 ? 'green' : successPrediction.probability >= 60 ? 'yellow' : 'orange'
                });

                return recommendations;
            }

            generateProgressTracking(gpa, totalHours) {
                const tracking = [];

                // Academic milestone tracking
                const milestones = this.calculateAcademicMilestones(totalHours);
                tracking.push({
                    type: 'milestone_tracking',
                    icon: 'fas fa-flag-checkered',
                    title: this.currentLanguage === 'ar' ? '🏁 تتبع المعالم الأكاديمية' : '🏁 Academic Milestone Tracking',
                    message: this.currentLanguage === 'ar' ?
                        `أكملت ${milestones.completed} من ${milestones.total} معالم أكاديمية` :
                        `Completed ${milestones.completed} out of ${milestones.total} academic milestones`,
                    details: milestones.details,
                    color: 'blue',
                    actionButton: this.currentLanguage === 'ar' ? 'عرض التقدم' : 'View Progress'
                });

                // Performance trend analysis
                const trend = this.analyzePerformanceTrend(gpa, totalHours);
                tracking.push({
                    type: 'performance_trend',
                    icon: 'fas fa-chart-line',
                    title: this.currentLanguage === 'ar' ? '📈 اتجاه الأداء' : '📈 Performance Trend',
                    message: trend.message,
                    details: trend.details,
                    color: trend.color,
                    priority: trend.priority
                });

                return tracking;
            }

            getRecommendedCourses(gpa, courseAnalysis) {
                const recommendations = [];

                if (gpa >= 3.5) {
                    recommendations.push(
                        this.currentLanguage === 'ar' ? 'مواد متقدمة في تخصصك الرئيسي' : 'Advanced courses in your major',
                        this.currentLanguage === 'ar' ? 'مواد اختيارية تحديّة' : 'Challenging elective courses',
                        this.currentLanguage === 'ar' ? 'مشاريع بحثية تطبيقية' : 'Applied research projects'
                    );
                } else if (gpa >= 3.0) {
                    recommendations.push(
                        this.currentLanguage === 'ar' ? 'مواد أساسية لتقوية الأساس' : 'Core courses to strengthen foundation',
                        this.currentLanguage === 'ar' ? 'مواد بساعات معتمدة أقل' : 'Courses with fewer credit hours',
                        this.currentLanguage === 'ar' ? 'مواد عملية تطبيقية' : 'Practical application courses'
                    );
                } else {
                    recommendations.push(
                        this.currentLanguage === 'ar' ? 'إعادة المواد ذات الدرجات المنخفضة' : 'Retake courses with low grades',
                        this.currentLanguage === 'ar' ? 'مواد تأسيسية لتقوية المهارات' : 'Foundation courses to strengthen skills',
                        this.currentLanguage === 'ar' ? 'تقليل عدد المواد المسجلة' : 'Reduce number of registered courses'
                    );
                }

                return recommendations;
            }

            analyzeLearningPattern(coursesData) {
                const pattern = {
                    insights: []
                };

                // Analyze credit hours preference
                const avgHours = coursesData.reduce((sum, course) => sum + course.hours, 0) / coursesData.length;

                if (avgHours >= 4) {
                    pattern.insights.push(
                        this.currentLanguage === 'ar' ? 'تفضل المواد ذات الساعات المعتمدة العالية - هذا يدل على قدرتك على التركيز لفترات طويلة' : 'You prefer high-credit courses - this indicates your ability to focus for long periods'
                    );
                } else if (avgHours <= 2) {
                    pattern.insights.push(
                        this.currentLanguage === 'ar' ? 'تفضل المواد ذات الساعات المعتمدة القليلة - ركز على الجودة أكثر من الكمية' : 'You prefer low-credit courses - focus on quality over quantity'
                    );
                }

                return pattern;
            }

            predictAcademicSuccess(gpa, totalHours, courseAnalysis) {
                let probability = 50; // Base probability

                // GPA factor
                if (gpa >= 3.5) probability += 30;
                else if (gpa >= 3.0) probability += 15;
                else if (gpa >= 2.5) probability += 5;
                else probability -= 10;

                // Progress factor
                const progressPercentage = (totalHours / 120) * 100;
                if (progressPercentage >= 75) probability += 15;
                else if (progressPercentage >= 50) probability += 10;
                else if (progressPercentage >= 25) probability += 5;

                // Consistency factor
                if (courseAnalysis.weakAreas.length === 0) probability += 10;
                else if (courseAnalysis.weakAreas.length <= 2) probability += 5;
                else probability -= 5;

                probability = Math.min(95, Math.max(5, probability));

                const factors = [];
                if (gpa >= 3.0) factors.push(this.currentLanguage === 'ar' ? '✓ معدل أكاديمي جيد' : '✓ Good academic GPA');
                if (totalHours >= 60) factors.push(this.currentLanguage === 'ar' ? '✓ تقدم جيد في الساعات' : '✓ Good progress in hours');
                if (courseAnalysis.weakAreas.length <= 2) factors.push(this.currentLanguage === 'ar' ? '✓ أداء متسق' : '✓ Consistent performance');

                return {
                    probability: Math.round(probability),
                    factors: factors
                };
            }

            calculateAcademicMilestones(totalHours) {
                const milestones = [
                    { name: this.currentLanguage === 'ar' ? 'إنهاء السنة الأولى' : 'Complete First Year', hours: 30 },
                    { name: this.currentLanguage === 'ar' ? 'إنهاء السنة الثانية' : 'Complete Second Year', hours: 60 },
                    { name: this.currentLanguage === 'ar' ? 'إنهاء السنة الثالثة' : 'Complete Third Year', hours: 90 },
                    { name: this.currentLanguage === 'ar' ? 'التخرج' : 'Graduation', hours: 120 }
                ];

                const completed = milestones.filter(m => totalHours >= m.hours).length;
                const details = milestones.map(m =>
                    `${totalHours >= m.hours ? '✅' : '⏳'} ${m.name} (${m.hours} ${this.currentLanguage === 'ar' ? 'ساعة' : 'hours'})`
                );

                return {
                    completed: completed,
                    total: milestones.length,
                    details: details
                };
            }

            analyzePerformanceTrend(gpa, totalHours) {
                // This is a simplified trend analysis
                // In a real application, you would compare with historical data

                let trend = {
                    message: '',
                    details: [],
                    color: 'blue',
                    priority: ''
                };

                if (gpa >= 3.5) {
                    trend.message = this.currentLanguage === 'ar' ? 'اتجاه أداء ممتاز ومستقر' : 'Excellent and stable performance trend';
                    trend.details = [
                        this.currentLanguage === 'ar' ? 'أداؤك يتحسن باستمرار' : 'Your performance is continuously improving',
                        this.currentLanguage === 'ar' ? 'حافظ على هذا المستوى' : 'Maintain this level'
                    ];
                    trend.color = 'green';
                } else if (gpa >= 3.0) {
                    trend.message = this.currentLanguage === 'ar' ? 'اتجاه أداء جيد مع إمكانية للتحسن' : 'Good performance trend with room for improvement';
                    trend.details = [
                        this.currentLanguage === 'ar' ? 'أداؤك في المسار الصحيح' : 'Your performance is on the right track',
                        this.currentLanguage === 'ar' ? 'ركز على تحسين النقاط الضعيفة' : 'Focus on improving weak points'
                    ];
                    trend.color = 'blue';
                } else {
                    trend.message = this.currentLanguage === 'ar' ? 'يحتاج الأداء إلى تحسن ملحوظ' : 'Performance needs significant improvement';
                    trend.details = [
                        this.currentLanguage === 'ar' ? 'ضع خطة تحسين فورية' : 'Create an immediate improvement plan',
                        this.currentLanguage === 'ar' ? 'اطلب المساعدة الأكاديمية' : 'Seek academic help'
                    ];
                    trend.color = 'red';
                    trend.priority = this.currentLanguage === 'ar' ? 'عاجل' : 'Urgent';
                }

                return trend;
            }

            loadUserPreferences() {
                const defaultPreferences = {
                    preferredSuggestionTypes: ['academic_insights', 'career_guidance', 'study_tips'],
                    suggestionComplexity: 'detailed', // 'simple', 'detailed', 'advanced'
                    motivationalLevel: 'balanced', // 'low', 'balanced', 'high'
                    focusAreas: ['gpa_improvement', 'time_management', 'career_prep'],
                    dismissedSuggestions: [],
                    lastUpdated: new Date().toISOString()
                };

                try {
                    const saved = localStorage.getItem('gpa_user_preferences');
                    return saved ? { ...defaultPreferences, ...JSON.parse(saved) } : defaultPreferences;
                } catch (error) {
                    console.error('Error loading user preferences:', error);
                    return defaultPreferences;
                }
            }

            saveUserPreferences() {
                try {
                    this.userPreferences.lastUpdated = new Date().toISOString();
                    localStorage.setItem('gpa_user_preferences', JSON.stringify(this.userPreferences));
                } catch (error) {
                    console.error('Error saving user preferences:', error);
                }
            }

            loadSuggestionHistory() {
                try {
                    const saved = localStorage.getItem('gpa_suggestion_history');
                    return saved ? JSON.parse(saved) : [];
                } catch (error) {
                    console.error('Error loading suggestion history:', error);
                    return [];
                }
            }

            saveSuggestionHistory() {
                try {
                    // Keep only last 50 entries
                    if (this.suggestionHistory.length > 50) {
                        this.suggestionHistory = this.suggestionHistory.slice(-50);
                    }
                    localStorage.setItem('gpa_suggestion_history', JSON.stringify(this.suggestionHistory));
                } catch (error) {
                    console.error('Error saving suggestion history:', error);
                }
            }

            customizeSuggestionsBasedOnPreferences(suggestions) {
                // If no suggestions, return empty array
                if (!suggestions || suggestions.length === 0) {
                    return [];
                }

                // Filter suggestions based on user preferences
                let filteredSuggestions = suggestions.filter(suggestion => {
                    // Check if suggestion was dismissed recently
                    const recentlyDismissed = this.userPreferences.dismissedSuggestions.some(dismissed =>
                        dismissed.type === suggestion.type &&
                        new Date(dismissed.timestamp) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days
                    );
                    if (recentlyDismissed) return false;

                    return true;
                });

                // If filtering removed all suggestions, return original suggestions (except dismissed ones)
                if (filteredSuggestions.length === 0) {
                    filteredSuggestions = suggestions.filter(suggestion => {
                        const recentlyDismissed = this.userPreferences.dismissedSuggestions.some(dismissed =>
                            dismissed.type === suggestion.type &&
                            new Date(dismissed.timestamp) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                        );
                        return !recentlyDismissed;
                    });
                }

                // Adjust complexity based on preferences
                if (this.userPreferences.suggestionComplexity === 'simple') {
                    filteredSuggestions = filteredSuggestions.map(suggestion => ({
                        ...suggestion,
                        details: suggestion.details ? suggestion.details.slice(0, 3) : []
                    }));
                } else if (this.userPreferences.suggestionComplexity === 'advanced') {
                    // Add more detailed analysis for advanced users
                    filteredSuggestions = this.enhanceSuggestionsForAdvancedUsers(filteredSuggestions);
                }

                // Adjust motivational tone
                if (this.userPreferences.motivationalLevel === 'high') {
                    filteredSuggestions = this.addMotivationalBoost(filteredSuggestions);
                } else if (this.userPreferences.motivationalLevel === 'low') {
                    filteredSuggestions = this.makeMoreFactual(filteredSuggestions);
                }

                return filteredSuggestions;
            }

            enhanceSuggestionsForAdvancedUsers(suggestions) {
                return suggestions.map(suggestion => {
                    if (suggestion.type === 'gpa_projection') {
                        // Add statistical analysis for advanced users
                        suggestion.details.push(
                            this.currentLanguage === 'ar' ?
                                '📊 تحليل إحصائي: انحراف معياري ±0.2 في التوقعات' :
                                '📊 Statistical analysis: Standard deviation ±0.2 in predictions'
                        );
                    }
                    return suggestion;
                });
            }

            addMotivationalBoost(suggestions) {
                return suggestions.map(suggestion => {
                    const motivationalPhrases = this.currentLanguage === 'ar' ? [
                        '💪 أنت قادر على تحقيق المزيد!',
                        '🌟 كل خطوة تقربك من هدفك!',
                        '🚀 استمر في التقدم الرائع!'
                    ] : [
                        '💪 You can achieve even more!',
                        '🌟 Every step brings you closer to your goal!',
                        '🚀 Keep up the amazing progress!'
                    ];

                    const randomPhrase = motivationalPhrases[Math.floor(Math.random() * motivationalPhrases.length)];
                    suggestion.message = `${randomPhrase} ${suggestion.message}`;
                    return suggestion;
                });
            }

            makeMoreFactual(suggestions) {
                return suggestions.map(suggestion => {
                    // Remove emotional language and emojis for users who prefer factual information
                    suggestion.title = suggestion.title.replace(/[🎯🏆💡📊🤖🧠🔮🏁📈💪⭐👍💼🛠️📚⏰🧘🌟🚀💪]/g, '');
                    suggestion.message = suggestion.message.replace(/[!]+/g, '.');
                    return suggestion;
                });
            }

            dismissSuggestion(suggestionType) {
                this.userPreferences.dismissedSuggestions.push({
                    type: suggestionType,
                    timestamp: new Date().toISOString()
                });
                this.saveUserPreferences();

                // Show confirmation and refresh suggestions
                this.showAlert(
                    this.currentLanguage === 'ar' ? 'تم إخفاء هذا النوع من الاقتراحات لمدة أسبوع' : 'This type of suggestions hidden for one week',
                    'info'
                );

                // Refresh suggestions
                if (this.currentResults) {
                    this.generateSmartSuggestions(this.currentResults);
                }
            }

            openSuggestionSettings() {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-2xl p-8 max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-2xl font-bold text-gray-800">
                                <i class="fas fa-cog text-blue-600 mr-3"></i>
                                ${this.currentLanguage === 'ar' ? 'إعدادات الاقتراحات الذكية' : 'Smart Suggestions Settings'}
                            </h3>
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>

                        <div class="space-y-6">
                            <!-- Suggestion Complexity -->
                            <div class="bg-blue-50 p-6 rounded-lg">
                                <h4 class="font-bold text-lg mb-3 text-blue-800">
                                    ${this.currentLanguage === 'ar' ? '📊 مستوى التفصيل' : '📊 Detail Level'}
                                </h4>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="complexity" value="simple" ${this.userPreferences.suggestionComplexity === 'simple' ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'بسيط - اقتراحات مختصرة ومباشرة' : 'Simple - Brief and direct suggestions'}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="complexity" value="detailed" ${this.userPreferences.suggestionComplexity === 'detailed' ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'مفصل - اقتراحات شاملة مع التفاصيل' : 'Detailed - Comprehensive suggestions with details'}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="complexity" value="advanced" ${this.userPreferences.suggestionComplexity === 'advanced' ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'متقدم - تحليل عميق مع إحصائيات' : 'Advanced - Deep analysis with statistics'}</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Motivational Level -->
                            <div class="bg-green-50 p-6 rounded-lg">
                                <h4 class="font-bold text-lg mb-3 text-green-800">
                                    ${this.currentLanguage === 'ar' ? '💪 مستوى التحفيز' : '💪 Motivational Level'}
                                </h4>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="motivation" value="low" ${this.userPreferences.motivationalLevel === 'low' ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'منخفض - معلومات واقعية فقط' : 'Low - Factual information only'}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="motivation" value="balanced" ${this.userPreferences.motivationalLevel === 'balanced' ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'متوازن - مزيج من المعلومات والتحفيز' : 'Balanced - Mix of information and motivation'}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="motivation" value="high" ${this.userPreferences.motivationalLevel === 'high' ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'عالي - رسائل تحفيزية قوية' : 'High - Strong motivational messages'}</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Focus Areas -->
                            <div class="bg-purple-50 p-6 rounded-lg">
                                <h4 class="font-bold text-lg mb-3 text-purple-800">
                                    ${this.currentLanguage === 'ar' ? '🎯 مجالات التركيز' : '🎯 Focus Areas'}
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" value="gpa_improvement" ${this.userPreferences.focusAreas.includes('gpa_improvement') ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'تحسين المعدل' : 'GPA Improvement'}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" value="time_management" ${this.userPreferences.focusAreas.includes('time_management') ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'إدارة الوقت' : 'Time Management'}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" value="career_prep" ${this.userPreferences.focusAreas.includes('career_prep') ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'التحضير المهني' : 'Career Preparation'}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" value="study_techniques" ${this.userPreferences.focusAreas.includes('study_techniques') ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'تقنيات الدراسة' : 'Study Techniques'}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" value="stress_management" ${this.userPreferences.focusAreas.includes('stress_management') ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'إدارة الضغط' : 'Stress Management'}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" value="academic_excellence" ${this.userPreferences.focusAreas.includes('academic_excellence') ? 'checked' : ''} class="mr-2">
                                        <span class="text-sm">${this.currentLanguage === 'ar' ? 'التفوق الأكاديمي' : 'Academic Excellence'}</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Reset Options -->
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h4 class="font-bold text-lg mb-3 text-gray-800">
                                    ${this.currentLanguage === 'ar' ? '🔄 خيارات الإعادة' : '🔄 Reset Options'}
                                </h4>
                                <div class="space-y-2">
                                    <button onclick="window.gpaCalculator.resetDismissedSuggestions()" class="text-sm text-blue-600 hover:text-blue-800 underline block">
                                        ${this.currentLanguage === 'ar' ? 'إعادة إظهار الاقتراحات المخفية' : 'Show hidden suggestions again'}
                                    </button>
                                    <button onclick="window.gpaCalculator.resetAllPreferences()" class="text-sm text-red-600 hover:text-red-800 underline block">
                                        ${this.currentLanguage === 'ar' ? 'إعادة تعيين جميع الإعدادات' : 'Reset all settings'}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex justify-end space-x-3 space-x-reverse">
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" class="px-6 py-3 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                                ${this.currentLanguage === 'ar' ? 'إلغاء' : 'Cancel'}
                            </button>
                            <button onclick="window.gpaCalculator.saveSuggestionSettings(this)" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-save mr-2"></i>
                                ${this.currentLanguage === 'ar' ? 'حفظ الإعدادات' : 'Save Settings'}
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }

            saveSuggestionSettings(button) {
                const modal = button.closest('.fixed');

                // Get complexity setting
                const complexity = modal.querySelector('input[name="complexity"]:checked')?.value || 'detailed';
                this.userPreferences.suggestionComplexity = complexity;

                // Get motivational level
                const motivation = modal.querySelector('input[name="motivation"]:checked')?.value || 'balanced';
                this.userPreferences.motivationalLevel = motivation;

                // Get focus areas
                const focusAreas = Array.from(modal.querySelectorAll('input[type="checkbox"]:checked'))
                    .map(checkbox => checkbox.value);
                this.userPreferences.focusAreas = focusAreas;

                // Save preferences
                this.saveUserPreferences();

                // Close modal
                modal.remove();

                // Show success message
                this.showAlert(
                    this.currentLanguage === 'ar' ? 'تم حفظ إعدادات الاقتراحات بنجاح' : 'Suggestion settings saved successfully',
                    'success'
                );

                // Refresh suggestions if available
                if (this.currentResults) {
                    this.generateSmartSuggestions(this.currentResults);
                }
            }

            resetDismissedSuggestions() {
                this.userPreferences.dismissedSuggestions = [];
                this.saveUserPreferences();
                this.showAlert(
                    this.currentLanguage === 'ar' ? 'تم إعادة إظهار جميع الاقتراحات المخفية' : 'All hidden suggestions restored',
                    'success'
                );

                // Refresh suggestions
                if (this.currentResults) {
                    this.generateSmartSuggestions(this.currentResults);
                }
            }

            resetAllPreferences() {
                if (confirm(this.currentLanguage === 'ar' ? 'هل أنت متأكد من إعادة تعيين جميع إعدادات الاقتراحات؟' : 'Are you sure you want to reset all suggestion settings?')) {
                    localStorage.removeItem('gpa_user_preferences');
                    localStorage.removeItem('gpa_suggestion_history');
                    this.userPreferences = this.loadUserPreferences();
                    this.suggestionHistory = this.loadSuggestionHistory();

                    this.showAlert(
                        this.currentLanguage === 'ar' ? 'تم إعادة تعيين جميع الإعدادات' : 'All settings have been reset',
                        'success'
                    );

                    // Refresh suggestions
                    if (this.currentResults) {
                        this.generateSmartSuggestions(this.currentResults);
                    }
                }
            }

            generateTrendAnalysis(gpa, totalHours) {
                const trends = [];
                const historicalData = this.suggestionHistory.slice(-10); // Last 10 calculations

                if (historicalData.length >= 3) {
                    // Analyze GPA trend
                    const gpaHistory = historicalData.map(h => h.gpa).filter(g => g);
                    if (gpaHistory.length >= 3) {
                        const recentGPA = gpaHistory.slice(-3);
                        const isImproving = recentGPA[2] > recentGPA[0];
                        const changeRate = ((recentGPA[2] - recentGPA[0]) / recentGPA[0] * 100).toFixed(1);

                        trends.push({
                            type: 'gpa_trend_analysis',
                            icon: 'fas fa-trending-up',
                            title: this.currentLanguage === 'ar' ? '📈 تحليل اتجاه المعدل' : '📈 GPA Trend Analysis',
                            message: this.currentLanguage === 'ar' ?
                                `معدلك ${isImproving ? 'يتحسن' : 'يتراجع'} بمعدل ${Math.abs(changeRate)}% خلال آخر 3 حسابات` :
                                `Your GPA is ${isImproving ? 'improving' : 'declining'} by ${Math.abs(changeRate)}% over the last 3 calculations`,
                            details: this.currentLanguage === 'ar' ? [
                                `الاتجاه العام: ${isImproving ? '📈 تحسن مستمر' : '📉 يحتاج انتباه'}`,
                                `معدل التغيير: ${changeRate}%`,
                                `آخر 3 معدلات: ${recentGPA.map(g => g.toFixed(2)).join(' ← ')}`,
                                isImproving ? 'استمر على نفس النهج الناجح' : 'راجع استراتيجية الدراسة',
                                'تتبع تقدمك بانتظام لضمان الاستمرارية'
                            ] : [
                                `Overall trend: ${isImproving ? '📈 Continuous improvement' : '📉 Needs attention'}`,
                                `Rate of change: ${changeRate}%`,
                                `Last 3 GPAs: ${recentGPA.map(g => g.toFixed(2)).join(' ← ')}`,
                                isImproving ? 'Continue with the successful approach' : 'Review study strategy',
                                'Track your progress regularly for consistency'
                            ],
                            color: isImproving ? 'green' : 'orange',
                            priority: isImproving ? this.currentLanguage === 'ar' ? 'إيجابي' : 'Positive' : this.currentLanguage === 'ar' ? 'يحتاج انتباه' : 'Needs Attention'
                        });
                    }
                }

                return trends;
            }

            generateSmartInsights(coursesData, courseAnalysis) {
                const insights = [];

                // Course difficulty analysis
                if (coursesData.length >= 3) {
                    const avgGradePoints = coursesData.reduce((sum, course) => {
                        const points = this.gradingSystem.grades[course.grade]?.points || 0;
                        return sum + points;
                    }, 0) / coursesData.length;

                    const difficultyInsight = this.analyzeDifficultyPattern(coursesData);

                    insights.push({
                        type: 'difficulty_analysis',
                        icon: 'fas fa-chart-pie',
                        title: this.currentLanguage === 'ar' ? '🎯 تحليل صعوبة المواد' : '🎯 Course Difficulty Analysis',
                        message: this.currentLanguage === 'ar' ?
                            `متوسط أداؤك: ${avgGradePoints.toFixed(2)} نقطة. ${difficultyInsight.message}` :
                            `Your average performance: ${avgGradePoints.toFixed(2)} points. ${difficultyInsight.message}`,
                        details: difficultyInsight.recommendations,
                        color: 'indigo'
                    });
                }

                // Study pattern optimization
                const studyPattern = this.analyzeStudyPattern(coursesData);
                if (studyPattern.insights.length > 0) {
                    insights.push({
                        type: 'study_optimization',
                        icon: 'fas fa-brain',
                        title: this.currentLanguage === 'ar' ? '🧠 تحسين نمط الدراسة' : '🧠 Study Pattern Optimization',
                        message: this.currentLanguage === 'ar' ?
                            'تحليل ذكي لنمط دراستك مع اقتراحات للتحسين' :
                            'Smart analysis of your study pattern with improvement suggestions',
                        details: studyPattern.insights,
                        color: 'cyan',
                        actionButton: this.currentLanguage === 'ar' ? 'تطبيق التحسينات' : 'Apply Improvements'
                    });
                }

                // Performance prediction
                const prediction = this.predictNextSemesterPerformance(coursesData, courseAnalysis);
                insights.push({
                    type: 'performance_prediction',
                    icon: 'fas fa-crystal-ball',
                    title: this.currentLanguage === 'ar' ? '🔮 توقع الأداء القادم' : '🔮 Next Performance Prediction',
                    message: this.currentLanguage === 'ar' ?
                        `توقع أداؤك في الفصل القادم: ${prediction.expectedGPA.toFixed(2)}` :
                        `Predicted performance next semester: ${prediction.expectedGPA.toFixed(2)}`,
                    details: prediction.factors,
                    color: prediction.confidence >= 80 ? 'green' : prediction.confidence >= 60 ? 'blue' : 'yellow',
                    priority: this.currentLanguage === 'ar' ? `ثقة ${prediction.confidence}%` : `${prediction.confidence}% confidence`
                });

                return insights;
            }

            analyzeDifficultyPattern(coursesData) {
                const gradePoints = coursesData.map(course => ({
                    name: course.name,
                    points: this.gradingSystem.grades[course.grade]?.points || 0,
                    hours: course.hours
                }));

                const highPerformance = gradePoints.filter(g => g.points >= 3.5);
                const lowPerformance = gradePoints.filter(g => g.points < 2.5);

                let message = '';
                let recommendations = [];

                if (highPerformance.length > lowPerformance.length) {
                    message = this.currentLanguage === 'ar' ? 'تتفوق في معظم المواد' : 'You excel in most subjects';
                    recommendations = this.currentLanguage === 'ar' ? [
                        'يمكنك تحدي نفسك بمواد أكثر صعوبة',
                        'فكر في زيادة عدد الساعات المعتمدة',
                        'استكشف مواد متقدمة في تخصصك',
                        'شارك في مشاريع بحثية أو تطبيقية'
                    ] : [
                        'You can challenge yourself with more difficult courses',
                        'Consider increasing your credit hours',
                        'Explore advanced courses in your major',
                        'Participate in research or applied projects'
                    ];
                } else if (lowPerformance.length > highPerformance.length) {
                    message = this.currentLanguage === 'ar' ? 'تواجه تحديات في عدة مواد' : 'You face challenges in several subjects';
                    recommendations = this.currentLanguage === 'ar' ? [
                        'ركز على تقوية الأساسيات',
                        'قلل عدد المواد المسجلة مؤقتاً',
                        'اطلب المساعدة الأكاديمية',
                        'طور تقنيات دراسة جديدة',
                        'انضم لمجموعات الدراسة'
                    ] : [
                        'Focus on strengthening fundamentals',
                        'Temporarily reduce registered courses',
                        'Seek academic help',
                        'Develop new study techniques',
                        'Join study groups'
                    ];
                } else {
                    message = this.currentLanguage === 'ar' ? 'أداء متوازن عبر المواد' : 'Balanced performance across subjects';
                    recommendations = this.currentLanguage === 'ar' ? [
                        'حافظ على الاستقرار الحالي',
                        'ركز على تحسين المواد الضعيفة',
                        'استمر في نفس نمط الدراسة',
                        'فكر في التخصص في مجال معين'
                    ] : [
                        'Maintain current stability',
                        'Focus on improving weaker subjects',
                        'Continue with the same study pattern',
                        'Consider specializing in a specific area'
                    ];
                }

                return { message, recommendations };
            }

            analyzeStudyPattern(coursesData) {
                const insights = [];

                // Credit hours pattern
                const totalHours = coursesData.reduce((sum, course) => sum + course.hours, 0);
                const avgHours = totalHours / coursesData.length;

                if (avgHours > 4) {
                    insights.push(
                        this.currentLanguage === 'ar' ?
                            'تفضل المواد ذات الساعات العالية - هذا يدل على قدرتك على التركيز المطول' :
                            'You prefer high-credit courses - this shows your ability for extended focus'
                    );
                } else if (avgHours < 3) {
                    insights.push(
                        this.currentLanguage === 'ar' ?
                            'تفضل المواد ذات الساعات القليلة - ركز على الجودة والفهم العميق' :
                            'You prefer low-credit courses - focus on quality and deep understanding'
                    );
                }

                // Performance consistency
                const gradePoints = coursesData.map(course => this.gradingSystem.grades[course.grade]?.points || 0);
                const variance = this.calculateVariance(gradePoints);

                if (variance < 0.5) {
                    insights.push(
                        this.currentLanguage === 'ar' ?
                            'أداؤك مستقر ومتسق - استمر على نفس النهج' :
                            'Your performance is stable and consistent - continue the same approach'
                    );
                } else if (variance > 1.0) {
                    insights.push(
                        this.currentLanguage === 'ar' ?
                            'أداؤك متذبذب - حاول توحيد استراتيجية الدراسة' :
                            'Your performance varies - try to unify your study strategy'
                    );
                }

                return { insights };
            }

            predictNextSemesterPerformance(coursesData, courseAnalysis) {
                let expectedGPA = 0;
                let confidence = 50;

                // Base prediction on current average
                const currentAvg = coursesData.reduce((sum, course) => {
                    return sum + (this.gradingSystem.grades[course.grade]?.points || 0);
                }, 0) / coursesData.length;

                expectedGPA = currentAvg;

                // Adjust based on trend
                const historicalData = this.suggestionHistory.slice(-5);
                if (historicalData.length >= 3) {
                    const recentGPAs = historicalData.map(h => h.gpa).filter(g => g);
                    if (recentGPAs.length >= 2) {
                        const trend = recentGPAs[recentGPAs.length - 1] - recentGPAs[0];
                        expectedGPA += trend * 0.3; // 30% weight to trend
                        confidence += 20;
                    }
                }

                // Adjust based on consistency
                const gradePoints = coursesData.map(course => this.gradingSystem.grades[course.grade]?.points || 0);
                const variance = this.calculateVariance(gradePoints);
                if (variance < 0.5) {
                    confidence += 15; // More confident if consistent
                } else if (variance > 1.0) {
                    confidence -= 10; // Less confident if inconsistent
                }

                expectedGPA = Math.max(0, Math.min(4, expectedGPA));
                confidence = Math.max(30, Math.min(95, confidence));

                const factors = [];
                if (currentAvg >= 3.0) factors.push(this.currentLanguage === 'ar' ? '✓ أداء حالي جيد' : '✓ Good current performance');
                if (variance < 0.5) factors.push(this.currentLanguage === 'ar' ? '✓ أداء مستقر' : '✓ Stable performance');
                if (courseAnalysis.strongAreas.length > courseAnalysis.weakAreas.length) {
                    factors.push(this.currentLanguage === 'ar' ? '✓ نقاط قوة أكثر من الضعف' : '✓ More strengths than weaknesses');
                }

                return {
                    expectedGPA,
                    confidence: Math.round(confidence),
                    factors
                };
            }

            calculateVariance(numbers) {
                const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
                const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
                return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
            }

            openShareModal() {
                if (!this.currentResults) {
                    this.showAlert('يرجى حساب المعدل أولاً', 'error');
                    return;
                }

                document.getElementById('shareModal').classList.remove('hidden');
            }

            openAdminModal() {
                document.getElementById('adminModal').classList.remove('hidden');
                this.loadAdminData();
            }

            async loadAdminData() {
                try {
                    const statsResponse = await fetch('index.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'get_admin_stats'
                        })
                    });

                    const stats = await statsResponse.json();

                    if (stats.error) {
                        throw new Error(stats.error);
                    }

                    this.updateAdminStats(stats);

                    const studentsResponse = await fetch('index.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'get_students_data'
                        })
                    });

                    const students = await studentsResponse.json();

                    if (students.error) {
                        throw new Error(students.error);
                    }

                    this.updateStudentsTable(students);

                } catch (error) {
                    console.error('Error loading admin data:', error);
                    this.showAlert('خطأ في تحميل بيانات الإدارة', 'error');
                }
            }

            updateAdminStats(stats) {
                // Update statistics using correct IDs from HTML
                const totalUsersElement = document.getElementById('totalUsers');
                const avgGPAElement = document.getElementById('avgGPA');
                const todayCalculationsElement = document.getElementById('todayCalculations');

                if (totalUsersElement) totalUsersElement.textContent = stats.total_users || 0;
                if (avgGPAElement) avgGPAElement.textContent = (stats.avg_gpa || 0).toFixed(2);
                if (todayCalculationsElement) todayCalculationsElement.textContent = stats.today_calculations || 0;

                // Update grade distribution
                const gradeDistributionElement = document.getElementById('gradeDistribution');
                if (gradeDistributionElement) {
                    const gradeDistribution = stats.grade_distribution || {};
                    let distributionHtml = '';

                    if (Object.keys(gradeDistribution).length === 0) {
                        distributionHtml = '<div class="col-span-4 text-center text-gray-500 py-4">لا توجد بيانات</div>';
                    } else {
                        for (const [grade, count] of Object.entries(gradeDistribution)) {
                            const percentage = stats.total_users > 0 ? ((count / stats.total_users) * 100).toFixed(1) : 0;
                            distributionHtml += `
                                <div class="bg-white p-4 rounded-lg border text-center">
                                    <div class="text-lg font-bold text-gray-800">${grade}</div>
                                    <div class="text-sm text-gray-600">${count} طالب</div>
                                    <div class="text-xs text-gray-500">${percentage}%</div>
                                </div>
                            `;
                        }
                    }

                    gradeDistributionElement.innerHTML = distributionHtml;
                }
            }

            updateStudentsTable(students) {
                const tbody = document.getElementById('studentsTableBody');

                if (!students || students.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4 text-gray-500">لا توجد بيانات طلاب</td></tr>';
                    return;
                }

                tbody.innerHTML = students.map((student, index) => `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${student.name}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${student.phone}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.gpa}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${student.classification}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${student.date}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="deleteStudent('${student.link_id || index}')" class="text-red-600 hover:text-red-900">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }

            async handleShareForm() {
                const name = document.getElementById('studentName').value.trim();
                const phone = document.getElementById('studentPhone').value.trim();

                if (!name || !phone) {
                    this.showAlert('يرجى ملء جميع الحقول', 'error');
                    return;
                }

                if (!this.currentResults) {
                    this.showAlert('يرجى حساب المعدل أولاً', 'error');
                    return;
                }

                try {
                    const coursesData = this.collectCoursesData();
                    const calculationType = document.getElementById('calculationType').value;
                    const previousGpa = parseFloat(document.getElementById('previousGpa')?.value || 0);
                    const previousHours = parseInt(document.getElementById('previousHours')?.value || 0);

                    const response = await fetch('index.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'save_student_data',
                            name: name,
                            phone: phone,
                            gpa: this.currentResults.cumulative_gpa || this.currentResults.gpa,
                            courses: JSON.stringify(coursesData),
                            calculation_type: calculationType,
                            previous_gpa: previousGpa,
                            previous_hours: previousHours
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        const shareUrl = `${window.location.origin}${window.location.pathname}?shared=${result.link_id}`;

                        document.getElementById('shareUrl').value = shareUrl;
                        document.getElementById('generatedLink').classList.remove('hidden');

                        this.showAlert('تم إنشاء الرابط وحفظ البيانات بنجاح', 'success');
                    } else {
                        throw new Error(result.error || 'فشل في حفظ البيانات');
                    }

                } catch (error) {
                    console.error('Error in share form:', error);
                    this.showAlert('حدث خطأ في حفظ البيانات: ' + error.message, 'error');
                }
            }

            checkForSharedGPA() {
                const urlParams = new URLSearchParams(window.location.search);
                const sharedId = urlParams.get('shared');
                if (sharedId) {
                    this.loadSharedGPA(sharedId);
                }
            }

            async loadSharedGPA(sharedId) {
                try {
                    const response = await fetch('index.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'get_shared_data',
                            link_id: sharedId
                        })
                    });

                    const result = await response.json();

                    if (result.success && result.data) {
                        console.log('Shared data received:', result.data); // Debug log

                        this.clearCourses();

                        // Display student info in the page title or header
                        if (result.data.name) {
                            // Update page title to show student name
                            document.title = `معدل الطالب: ${result.data.name} - حاسبة المعدل`;

                            // Show student info in a notification
                            this.showAlert(`تم تحميل بيانات الطالب: ${result.data.name}`, 'success');

                            // Add student info display to the page
                            this.displayStudentInfo(result.data);
                        }

                        // Add courses
                        if (result.data.courses && result.data.courses.length > 0) {
                            console.log('Adding courses:', result.data.courses); // Debug log

                            result.data.courses.forEach((course, index) => {
                                console.log(`Adding course ${index + 1}:`, course); // Debug log

                                this.addCourse({
                                    name: course.course_name || course.name || `مادة ${index + 1}`,
                                    hours: parseInt(course.credit_hours || course.hours || 3),
                                    grade: course.grade || 'A'
                                });
                            });

                            this.showAlert(`تم إضافة ${result.data.courses.length} مادة دراسية`, 'info');
                        } else {
                            console.log('No courses found, adding sample courses'); // Debug log

                            // Add sample courses if none exist
                            this.addCourse({
                                name: 'الرياضيات',
                                hours: 3,
                                grade: 'A'
                            });
                            this.addCourse({
                                name: 'الفيزياء',
                                hours: 3,
                                grade: 'B+'
                            });
                            this.addCourse({
                                name: 'الكيمياء',
                                hours: 3,
                                grade: 'A-'
                            });

                            this.showAlert('تم إضافة مواد تجريبية', 'info');
                        }

                        // Set university if available
                        if (result.data.university) {
                            const universitySelect = document.getElementById('universitySelect');
                            if (universitySelect) {
                                // Try to find matching university
                                for (let option of universitySelect.options) {
                                    if (option.text.includes(result.data.university) || result.data.university.includes(option.text)) {
                                        option.selected = true;
                                        break;
                                    }
                                }
                            }
                        }

                        // Handle previous GPA if available
                        if (result.data.gpa && result.data.gpa > 0) {
                            document.getElementById('calculationType').value = 'cumulative';
                            this.togglePreviousGpaSection('cumulative');
                            document.getElementById('previousGpa').value = result.data.gpa;
                            // Estimate previous hours based on classification or use default
                            const estimatedHours = result.data.classification ? 60 : 30;
                            document.getElementById('previousHours').value = estimatedHours;
                        }

                        // Calculate GPA to show results
                        setTimeout(() => {
                            this.calculateGPA();
                        }, 500);

                        this.showAlert('تم تحميل البيانات المشاركة بنجاح', 'success');
                    } else {
                        this.showAlert(result.error || 'الرابط غير صحيح أو منتهي الصلاحية', 'error');
                    }

                } catch (error) {
                    console.error('Error loading shared data:', error);
                    this.showAlert('خطأ في تحميل البيانات المشاركة', 'error');
                }
            }

            displayStudentInfo(studentData) {
                // Create or update student info display
                let studentInfoDiv = document.getElementById('studentInfoDisplay');

                if (!studentInfoDiv) {
                    studentInfoDiv = document.createElement('div');
                    studentInfoDiv.id = 'studentInfoDisplay';
                    studentInfoDiv.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6';

                    // Insert after the calculator title
                    const calculatorTitle = document.querySelector('h2');
                    if (calculatorTitle && calculatorTitle.parentNode) {
                        calculatorTitle.parentNode.insertBefore(studentInfoDiv, calculatorTitle.nextSibling);
                    }
                }

                studentInfoDiv.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-blue-800 mb-2">
                                <i class="fas fa-user ml-2"></i>
                                بيانات الطالب المشاركة
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">الاسم:</span>
                                    <span class="text-gray-900">${studentData.name || 'غير محدد'}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">الهاتف:</span>
                                    <span class="text-gray-900">${studentData.phone || 'غير محدد'}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">الجامعة:</span>
                                    <span class="text-gray-900">${studentData.university || 'غير محدد'}</span>
                                </div>
                                ${studentData.gpa ? `
                                <div>
                                    <span class="font-medium text-gray-700">المعدل المحفوظ:</span>
                                    <span class="text-blue-600 font-semibold">${studentData.gpa}</span>
                                </div>
                                ` : ''}
                                ${studentData.classification ? `
                                <div>
                                    <span class="font-medium text-gray-700">التقدير:</span>
                                    <span class="text-green-600 font-semibold">${studentData.classification.name || studentData.classification}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
            }

            clearCourses() {
                const container = document.getElementById('coursesContainer');
                if (container) {
                    container.innerHTML = '';
                }
            }

            addInitialCourse() {
                const container = document.getElementById('coursesContainer');
                if (container && container.children.length === 0) {
                    this.addCourse();
                }
            }

            togglePreviousGpaSection(type) {
                const section = document.getElementById('previousGpaSection');
                if (section) {
                    if (type === 'cumulative') {
                        section.classList.remove('hidden');
                        section.classList.add('slide-in');
                    } else {
                        section.classList.add('hidden');
                    }
                }
            }

            async toggleLanguage() {
                try {
                    const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';

                    const response = await fetch('index.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'change_language',
                            language: newLanguage
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        location.reload();
                    }

                } catch (error) {
                    console.error('Error changing language:', error);
                }
            }

            async changeUniversity(university) {
                try {
                    const response = await fetch('index.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'change_university',
                            university: university
                        })
                    });

                    const result = await response.json();

                    if (result.success && result.grading_system) {
                        // Update grading system
                        this.gradingSystem = result.grading_system;
                        window.gradingSystem = result.grading_system;

                        // Update grade scale display
                        this.updateGradeScaleDisplay(result.grading_system);

                        // Update existing course grade selects
                        this.updateCourseGradeSelects();

                        this.showAlert('تم تحديث نظام التقدير حسب الجامعة المختارة', 'success');
                    }

                } catch (error) {
                    console.error('Error changing university:', error);
                    this.showAlert('خطأ في تحديث الجامعة', 'error');
                }
            }

            async changeGradingSystem(system) {
                try {
                    const response = await fetch('index.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'change_grading_system',
                            system: system
                        })
                    });

                    const result = await response.json();

                    if (result.success && result.system) {
                        // Update grading system
                        this.gradingSystem = result.system;
                        window.gradingSystem = result.system;

                        // Update grade scale display
                        this.updateGradeScaleDisplay(result.system);

                        // Update existing course grade selects
                        this.updateCourseGradeSelects();

                        this.showAlert('تم تحديث نظام التقدير بنجاح', 'success');
                    } else {
                        this.showAlert(result.error || 'خطأ في تحديث نظام التقدير', 'error');
                    }

                } catch (error) {
                    console.error('Error changing grading system:', error);
                    this.showAlert('خطأ في تحديث نظام التقدير', 'error');
                }
            }

            async saveCourses() {
                try {
                    const coursesData = this.collectCoursesData();

                    if (coursesData.length === 0) {
                        this.showAlert('لا توجد مواد لحفظها', 'error');
                        return;
                    }

                    localStorage.setItem('gpaCalculatorData', JSON.stringify({
                        courses: coursesData,
                        timestamp: new Date().toISOString()
                    }));

                    this.showAlert('تم حفظ البيانات بنجاح', 'success');

                } catch (error) {
                    console.error('Error saving courses:', error);
                    this.showAlert('حدث خطأ في حفظ البيانات', 'error');
                }
            }

            async loadCourses() {
                try {
                    const savedData = localStorage.getItem('gpaCalculatorData');

                    if (savedData) {
                        const data = JSON.parse(savedData);
                        if (data.courses && data.courses.length > 0) {
                            this.clearCourses();
                            data.courses.forEach(course => {
                                this.addCourse(course);
                            });
                            this.showAlert('تم تحميل البيانات بنجاح', 'success');
                        } else {
                            this.showAlert('لا توجد بيانات محفوظة', 'info');
                        }
                    } else {
                        this.showAlert('لا توجد بيانات محفوظة', 'info');
                    }

                } catch (error) {
                    console.error('Error loading courses:', error);
                    this.showAlert('حدث خطأ في تحميل البيانات', 'error');
                }
            }

            loadSavedData() {
                // Auto-load saved data on page load
                const savedData = localStorage.getItem('gpaCalculatorData');
                if (savedData) {
                    try {
                        const data = JSON.parse(savedData);
                        if (data.courses && data.courses.length > 0) {
                            this.clearCourses();
                            data.courses.forEach(course => {
                                this.addCourse(course);
                            });
                        }
                    } catch (error) {
                        console.error('Error loading saved data:', error);
                    }
                }
            }

            updateGradeScaleDisplay(gradingSystem) {
                const container = document.getElementById('gradeScaleContainer');
                const title = document.getElementById('gradeScaleTitle');

                if (title) {
                    const systemName = gradingSystem.name || 'نظام التقدير';
                    title.textContent = this.currentLanguage === 'ar' ? `سلم الدرجات - ${systemName}` : `Grade Scale - ${systemName}`;
                }

                if (container && gradingSystem.grades) {
                    let html = '';

                    Object.keys(gradingSystem.grades).forEach(grade => {
                        const info = gradingSystem.grades[grade];
                        const bgColor = this.getGradeColor(grade);

                        html += `
                            <div class="flex justify-between items-center p-3 ${bgColor} rounded-lg border">
                                <div class="flex items-center justify-between w-full">
                                    <span class="font-bold text-lg text-gray-800">${grade}</span>
                                    <span class="text-sm text-gray-600">
                                        ${info.min_percentage && info.max_percentage ? info.min_percentage + '-' + info.max_percentage : ''}
                                    </span>
                                    <span class="font-bold text-lg text-blue-600">${info.points}</span>
                                </div>
                            </div>
                        `;
                    });

                    container.innerHTML = html;
                }
            }

            getGradeColor(grade) {
                if (grade === 'A' || grade === 'A+') return 'bg-green-50 border-green-200';
                if (grade === 'B+' || grade === 'B' || grade === 'B-') return 'bg-blue-50 border-blue-200';
                if (grade === 'C+' || grade === 'C' || grade === 'C-') return 'bg-yellow-50 border-yellow-200';
                if (grade === 'D+' || grade === 'D' || grade === 'D-') return 'bg-orange-50 border-orange-200';
                return 'bg-red-50 border-red-200';
            }

            updateCourseGradeSelects() {
                document.querySelectorAll('.course-grade').forEach(select => {
                    const currentValue = select.value;
                    select.innerHTML = `<option value="">${this.currentLanguage === 'ar' ? 'اختر التقدير' : 'Select Grade'}</option>` +
                                     this.getGradeOptions(currentValue);
                });
            }

            async searchCourses(query) {
                const resultsContainer = document.getElementById('courseResults');
                if (!resultsContainer) return;

                if (!query || query.length < 2) {
                    resultsContainer.innerHTML = `
                        <div class="text-center text-gray-500 py-6">
                            <i class="fas fa-book text-3xl mb-3"></i>
                            <p class="text-sm" id="searchMessage">${this.currentLanguage === 'ar' ? 'ابحث عن مادة للحصول على تفاصيلها' : 'Search for a course to get its details'}</p>
                        </div>
                    `;
                    return;
                }

                try {
                    const response = await fetch('search_courses.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `query=${encodeURIComponent(query)}`
                    });

                    const data = await response.json();

                    if (data.success && data.courses.length > 0) {
                        let html = '';
                        data.courses.forEach(course => {
                            html += `
                                <div class="bg-white border border-gray-200 rounded-lg p-4 mb-3 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start mb-2">
                                        <div>
                                            <h4 class="font-bold text-lg text-blue-600">${course.course_code}</h4>
                                            <h5 class="font-medium text-gray-800">${this.currentLanguage === 'ar' ? course.course_title_ar : course.course_title_en}</h5>
                                        </div>
                                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">${course.credit_hours} ساعة</span>
                                    </div>

                                    ${course.prerequisites ? `
                                        <div class="mb-2">
                                            <span class="text-sm font-medium text-gray-600">المتطلبات السابقة:</span>
                                            <span class="text-sm text-gray-800">${course.prerequisites}</span>
                                        </div>
                                    ` : ''}

                                    ${course.course_description_ar || course.course_description_en ? `
                                        <div class="mb-3">
                                            <p class="text-sm text-gray-700">${this.currentLanguage === 'ar' ? (course.course_description_ar || course.course_description_en) : (course.course_description_en || course.course_description_ar)}</p>
                                        </div>
                                    ` : ''}

                                    <div class="flex gap-2">
                                        <button onclick="window.gpaCalculator.showCourseDetails(${JSON.stringify(course).replace(/"/g, '&quot;')})"
                                                class="bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-1 rounded transition-colors">
                                            <i class="fas fa-info-circle ml-1"></i>
                                            تفاصيل
                                        </button>
                                        <button onclick="window.gpaCalculator.addCourseFromSearch('${course.course_code}', '${this.currentLanguage === 'ar' ? course.course_title_ar : course.course_title_en}', ${course.credit_hours})"
                                                class="bg-green-600 hover:bg-green-700 text-white text-sm px-3 py-1 rounded transition-colors">
                                            <i class="fas fa-plus ml-1"></i>
                                            إضافة للحاسبة
                                        </button>
                                    </div>
                                </div>
                            `;
                        });
                        resultsContainer.innerHTML = html;
                    } else {
                        resultsContainer.innerHTML = `
                            <div class="text-center text-gray-500 py-6">
                                <i class="fas fa-search text-3xl mb-3"></i>
                                <p class="text-sm">لم يتم العثور على مواد تطابق البحث</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    console.error('Error searching courses:', error);
                    resultsContainer.innerHTML = `
                        <div class="text-center text-red-500 py-6">
                            <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                            <p class="text-sm">حدث خطأ في البحث</p>
                        </div>
                    `;
                }
            }

            addCourseFromSearch(courseCode, courseName, creditHours) {
                this.addCourse({
                    name: `${courseCode} - ${courseName}`,
                    hours: creditHours,
                    grade: ''
                });
                this.showAlert('تم إضافة المادة بنجاح', 'success');
            }

            showCourseDetails(course) {
                const modal = document.getElementById('courseDetailsModal');
                const title = document.getElementById('courseDetailsTitle');
                const content = document.getElementById('courseDetailsContent');
                const addBtn = document.getElementById('addCourseFromDetailsBtn');

                // Set title
                title.textContent = `تفاصيل المادة - ${course.course_code}`;

                // Build content
                let html = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="text-lg font-bold text-blue-800 mb-3">
                                <i class="fas fa-info-circle ml-2"></i>
                                المعلومات الأساسية
                            </h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">كود المادة:</span>
                                    <span class="text-blue-600 font-bold">${course.course_code}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">الساعات المعتمدة:</span>
                                    <span class="text-green-600 font-bold">${course.credit_hours} ساعة</span>
                                </div>
                                ${course.prerequisites ? `
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">المتطلبات السابقة:</span>
                                    <span class="text-orange-600 font-medium">${course.prerequisites}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>

                        <!-- Course Names -->
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="text-lg font-bold text-green-800 mb-3">
                                <i class="fas fa-book ml-2"></i>
                                أسماء المادة
                            </h4>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-gray-600">العربية:</span>
                                    <p class="text-gray-800 font-medium">${course.course_title_ar}</p>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-600">الإنجليزية:</span>
                                    <p class="text-gray-800 font-medium">${course.course_title_en}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    ${course.course_description_ar || course.course_description_en ? `
                    <!-- Description -->
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="text-lg font-bold text-purple-800 mb-3">
                            <i class="fas fa-file-alt ml-2"></i>
                            وصف المادة
                        </h4>
                        ${course.course_description_ar ? `
                        <div class="mb-3">
                            <span class="text-sm font-medium text-gray-600">العربية:</span>
                            <p class="text-gray-800 mt-1 leading-relaxed">${course.course_description_ar}</p>
                        </div>
                        ` : ''}
                        ${course.course_description_en ? `
                        <div>
                            <span class="text-sm font-medium text-gray-600">الإنجليزية:</span>
                            <p class="text-gray-800 mt-1 leading-relaxed">${course.course_description_en}</p>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}

                    ${course.course_objectives_ar || course.course_objectives_en ? `
                    <!-- Objectives -->
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <h4 class="text-lg font-bold text-yellow-800 mb-3">
                            <i class="fas fa-target ml-2"></i>
                            أهداف المادة
                        </h4>
                        ${course.course_objectives_ar ? `
                        <div class="mb-3">
                            <span class="text-sm font-medium text-gray-600">العربية:</span>
                            <p class="text-gray-800 mt-1 leading-relaxed">${course.course_objectives_ar}</p>
                        </div>
                        ` : ''}
                        ${course.course_objectives_en ? `
                        <div>
                            <span class="text-sm font-medium text-gray-600">الإنجليزية:</span>
                            <p class="text-gray-800 mt-1 leading-relaxed">${course.course_objectives_en}</p>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}

                    ${course.course_outcomes_ar || course.course_outcomes_en ? `
                    <!-- Outcomes -->
                    <div class="bg-indigo-50 p-4 rounded-lg">
                        <h4 class="text-lg font-bold text-indigo-800 mb-3">
                            <i class="fas fa-graduation-cap ml-2"></i>
                            مخرجات التعلم
                        </h4>
                        ${course.course_outcomes_ar ? `
                        <div class="mb-3">
                            <span class="text-sm font-medium text-gray-600">العربية:</span>
                            <p class="text-gray-800 mt-1 leading-relaxed">${course.course_outcomes_ar}</p>
                        </div>
                        ` : ''}
                        ${course.course_outcomes_en ? `
                        <div>
                            <span class="text-sm font-medium text-gray-600">الإنجليزية:</span>
                            <p class="text-gray-800 mt-1 leading-relaxed">${course.course_outcomes_en}</p>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}
                `;

                content.innerHTML = html;

                // Set up add button
                addBtn.onclick = () => {
                    this.addCourseFromSearch(course.course_code, course.course_title_ar, course.credit_hours);
                    closeCourseDetailsModal();
                };

                modal.classList.remove('hidden');
            }

            closeCourseDetailsModal() {
                document.getElementById('courseDetailsModal').classList.add('hidden');
            }

            showAlert(message, type = 'info') {
                const alert = document.createElement('div');
                alert.className = `fixed top-4 ${this.currentLanguage === 'ar' ? 'left-4' : 'right-4'} z-50 p-4 rounded-lg shadow-lg text-white max-w-sm ${
                    type === 'success' ? 'bg-green-500' :
                    type === 'error' ? 'bg-red-500' :
                    type === 'info' ? 'bg-blue-500' :
                    'bg-gray-500'
                }`;
                alert.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-triangle' :
                            type === 'info' ? 'fa-info-circle' :
                            'fa-bell'
                        }"></i>
                        <span>${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-auto text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                document.body.appendChild(alert);

                setTimeout(() => {
                    if (alert.parentElement) {
                        alert.remove();
                    }
                }, 5000);
            }

            async loadNotifications() {
                try {
                    console.log('Loading notifications...');
                    const response = await fetch('get_notifications.php');

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    console.log('Notifications response:', data);

                    if (data.success) {
                        // Store notifications
                        this.popupNotifications = data.popup || [];
                        this.inlineNotifications = data.inline || [];
                        this.currentPopupIndex = 0;

                        console.log('Popup notifications:', this.popupNotifications.length);
                        console.log('Inline notifications:', this.inlineNotifications.length);

                        // Show inline notifications immediately
                        if (this.inlineNotifications.length > 0) {
                            console.log('Displaying inline notifications...');
                            this.displayInlineNotifications();
                        }

                        // Show popup notifications if any
                        if (this.popupNotifications.length > 0) {
                            console.log('Showing popup notification...');
                            setTimeout(() => this.showPopupNotification(), 1000);
                        }
                    } else {
                        console.error('Failed to load notifications:', data.error || 'Unknown error');
                    }
                } catch (error) {
                    console.error('Error loading notifications:', error);
                }
            }

            displayInlineNotifications() {
                const container = document.getElementById('inlineNotificationsContainer');
                console.log('Inline notifications container:', container);
                console.log('Inline notifications:', this.inlineNotifications);

                if (!container) {
                    console.error('Inline notifications container not found');
                    return;
                }

                if (!this.inlineNotifications || this.inlineNotifications.length === 0) {
                    console.log('No inline notifications to display');
                    container.innerHTML = ''; // Clear any existing content
                    return;
                }

                let html = '';
                this.inlineNotifications.forEach((notification, index) => {
                    console.log(`Processing notification ${index}:`, notification);

                    const iconClass = this.getNotificationIcon(notification.type);
                    const bgClass = this.getNotificationBgClass(notification.type);

                    const title = this.currentLanguage === 'ar' ? notification.title_ar : notification.title_en;
                    const message = this.currentLanguage === 'ar' ? notification.message_ar : notification.message_en;

                    html += `
                        <div class="bg-white rounded-lg shadow-md p-4 mb-4 border-l-4 ${bgClass}" data-notification-id="${notification.id}">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="${iconClass} text-lg"></i>
                                </div>
                                <div class="mr-3 flex-1">
                                    <h4 class="text-sm font-bold text-gray-900 mb-1">
                                        ${title || 'إشعار'}
                                    </h4>
                                    <p class="text-sm text-gray-700 leading-relaxed">
                                        ${message || 'رسالة الإشعار'}
                                    </p>
                                </div>
                                <button onclick="window.gpaCalculator.dismissInlineNotification(${notification.id}, this)"
                                        class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });

                console.log('Generated HTML:', html);
                container.innerHTML = html;
                console.log('Container after setting HTML:', container.innerHTML);
            }

            showPopupNotification() {
                if (this.currentPopupIndex >= this.popupNotifications.length) return;

                const notification = this.popupNotifications[this.currentPopupIndex];
                const modal = document.getElementById('notificationPopupModal');
                const icon = document.getElementById('notificationIcon');
                const iconClass = document.getElementById('notificationIconClass');
                const title = document.getElementById('notificationTitle');
                const message = document.getElementById('notificationMessage');
                const nextBtn = document.getElementById('nextNotificationBtn');

                // Set notification content
                title.textContent = this.currentLanguage === 'ar' ? notification.title_ar : notification.title_en;
                message.textContent = this.currentLanguage === 'ar' ? notification.message_ar : notification.message_en;

                // Set icon and colors
                const iconInfo = this.getNotificationIconInfo(notification.type);
                icon.className = `w-10 h-10 rounded-full flex items-center justify-center mr-3 ${iconInfo.bgClass}`;
                iconClass.className = `text-white text-lg ${iconInfo.iconClass}`;

                // Show/hide next button
                if (this.currentPopupIndex < this.popupNotifications.length - 1) {
                    nextBtn.classList.remove('hidden');
                } else {
                    nextBtn.classList.add('hidden');
                }

                modal.classList.remove('hidden');

                // Mark as viewed
                this.markNotificationViewed(notification.id);
            }

            getNotificationIcon(type) {
                switch (type) {
                    case 'success': return 'fas fa-check-circle text-green-600';
                    case 'warning': return 'fas fa-exclamation-triangle text-yellow-600';
                    case 'error': return 'fas fa-times-circle text-red-600';
                    default: return 'fas fa-info-circle text-blue-600';
                }
            }

            getNotificationBgClass(type) {
                switch (type) {
                    case 'success': return 'border-green-500';
                    case 'warning': return 'border-yellow-500';
                    case 'error': return 'border-red-500';
                    default: return 'border-blue-500';
                }
            }

            getNotificationIconInfo(type) {
                switch (type) {
                    case 'success':
                        return { iconClass: 'fas fa-check', bgClass: 'bg-green-500' };
                    case 'warning':
                        return { iconClass: 'fas fa-exclamation-triangle', bgClass: 'bg-yellow-500' };
                    case 'error':
                        return { iconClass: 'fas fa-times', bgClass: 'bg-red-500' };
                    default:
                        return { iconClass: 'fas fa-info', bgClass: 'bg-blue-500' };
                }
            }

            async markNotificationViewed(notificationId) {
                try {
                    await fetch('mark_notification_viewed.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `notification_id=${notificationId}`
                    });
                } catch (error) {
                    console.error('Error marking notification as viewed:', error);
                }
            }

            dismissInlineNotification(notificationId, element) {
                element.closest('.bg-white').remove();
                this.markNotificationViewed(notificationId);
            }
        }

        // Global functions for modal handling
        function closeShareModal() {
            document.getElementById('shareModal').classList.add('hidden');
            document.getElementById('generatedLink').classList.add('hidden');
            document.getElementById('shareForm').reset();
        }

        function closeAdminModal() {
            document.getElementById('adminModal').classList.add('hidden');
        }

        function closeCourseDetailsModal() {
            document.getElementById('courseDetailsModal').classList.add('hidden');
        }

        function closeNotificationPopup() {
            document.getElementById('notificationPopupModal').classList.add('hidden');
        }

        function showNextNotification() {
            if (window.gpaCalculator) {
                window.gpaCalculator.currentPopupIndex++;
                closeNotificationPopup();
                setTimeout(() => window.gpaCalculator.showPopupNotification(), 500);
            }
        }

        function copyLink() {
            const shareUrl = document.getElementById('shareUrl');
            shareUrl.select();
            document.execCommand('copy');

            if (window.gpaCalculator) {
                window.gpaCalculator.showAlert('تم نسخ الرابط', 'success');
            }
        }

        function showAdminTab(tabName) {
            document.querySelectorAll('.admin-content').forEach(tab => {
                tab.classList.add('hidden');
            });

            document.querySelectorAll('.admin-tab').forEach(btn => {
                btn.classList.remove('active', 'border-purple-500', 'text-purple-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            document.getElementById(tabName + 'Tab').classList.remove('hidden');

            const activeBtn = document.querySelector(`[onclick="showAdminTab('${tabName}')"]`);
            if (activeBtn) {
                activeBtn.classList.add('active', 'border-purple-500', 'text-purple-600');
                activeBtn.classList.remove('border-transparent', 'text-gray-500');
            }
        }

        function deleteStudent(linkId) {
            if (confirm('هل أنت متأكد من حذف بيانات هذا الطالب؟')) {
                fetch('index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'delete_student',
                        link_id: linkId
                    })
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        window.gpaCalculator.loadAdminData();
                        window.gpaCalculator.showAlert('تم حذف بيانات الطالب', 'success');
                    } else {
                        window.gpaCalculator.showAlert('فشل في حذف البيانات', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    window.gpaCalculator.showAlert('حدث خطأ', 'error');
                });
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.gpaCalculator = new GPACalculator();
        });
    </script>

    <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .animate-pulse {
            animation: pulse 2s infinite;
        }
    </style>
</body>
</html>
