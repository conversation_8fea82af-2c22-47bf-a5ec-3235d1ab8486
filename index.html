<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة المعدل التراكمي - الجامعة العربية المفتوحة</title>

    <!-- Tailwind CSS - Local fallback -->
    <script>
        // Load Tailwind faster
        const tailwindScript = document.createElement('script');
        tailwindScript.src = 'https://cdn.tailwindcss.com';
        tailwindScript.async = true;
        document.head.appendChild(tailwindScript);
    </script>

    <!-- Font Awesome - Minimal icons -->
    <style>
        /* Minimal icon styles to avoid loading delays */
        .fas::before { font-family: 'Font Awesome 6 Free'; font-weight: 900; }
        .fa-university::before { content: '🏛️'; }
        .fa-calculator::before { content: '🧮'; }
        .fa-chart-line::before { content: '📈'; }
        .fa-lightbulb::before { content: '💡'; }
        .fa-robot::before { content: '🤖'; }
        .fa-language::before { content: '🌐'; }
        .fa-share-alt::before { content: '📤'; }
        .fa-cog::before { content: '⚙️'; }
        .fa-plus::before { content: '+'; }
        .fa-trash::before { content: '🗑️'; }
        .fa-save::before { content: '💾'; }
        .fa-upload::before { content: '📁'; }
        .fa-graduation-cap::before { content: '🎓'; }
        .fa-trophy::before { content: '🏆'; }
        .fa-clock::before { content: '⏰'; }
        .fa-chart-bar::before { content: '📊'; }
        .fa-comments::before { content: '💬'; }
        .fa-paper-plane::before { content: '✈️'; }
        .fa-users::before { content: '👥'; }
        .fa-times::before { content: '✖️'; }
        .fa-copy::before { content: '📋'; }
        .fa-link::before { content: '🔗'; }
        .fa-calendar::before { content: '📅'; }
        .fa-globe::before { content: '🌍'; }
    </style>

    <style>
        /* Custom styles */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        .slide-in {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .course-item {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .course-item:hover {
            border-color: #3B82F6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .course-item.error {
            border-color: #EF4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">

    <!-- Top Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Title -->
                <div class="flex items-center gap-4">
                    <div class="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xl">
                        AOU
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800">حاسبة المعدل التراكمي</h1>
                        <p class="text-sm text-gray-600">الجامعة العربية المفتوحة</p>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex items-center gap-4">
                    <button id="languageToggle" class="bg-blue-100 text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors">
                        <i class="fas fa-language ml-2"></i>
                        <span id="langText">English</span>
                    </button>
                    <button id="shareBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-share-alt ml-2"></i>
                        <span id="shareText">مشاركة المعدل</span>
                    </button>
                    <button id="adminBtn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-cog ml-2"></i>
                        <span id="adminText">لوحة الإدارة</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8 max-w-7xl">

        <!-- University Info Section -->
        <div class="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div>
                    <h2 class="text-3xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-university text-blue-600 ml-3"></i>
                        نبذة عن الجامعة العربية المفتوحة
                    </h2>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        الجامعة العربية المفتوحة هي مؤسسة تعليمية رائدة تأسست عام 2002، وتقدم برامج أكاديمية متميزة
                        في مختلف التخصصات. تتميز الجامعة بنظام التعليم المدمج الذي يجمع بين التعليم الإلكتروني والتعليم التقليدي.
                    </p>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <i class="fas fa-calendar text-blue-600 ml-2"></i>
                            <strong>تأسست:</strong> 2002
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg">
                            <i class="fas fa-users text-green-600 ml-2"></i>
                            <strong>الطلاب:</strong> +40,000
                        </div>
                        <div class="bg-purple-50 p-3 rounded-lg">
                            <i class="fas fa-graduation-cap text-purple-600 ml-2"></i>
                            <strong>البرامج:</strong> +50 برنامج
                        </div>
                        <div class="bg-yellow-50 p-3 rounded-lg">
                            <i class="fas fa-globe text-yellow-600 ml-2"></i>
                            <strong>الفروع:</strong> 9 دول عربية
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="w-full h-64 bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl shadow-lg mb-4 flex items-center justify-center text-white">
                        <div class="text-center">
                            <div class="text-6xl mb-4">🏛️</div>
                            <h3 class="text-2xl font-bold">الجامعة العربية المفتوحة</h3>
                            <p class="text-blue-100 mt-2">Arab Open University</p>
                        </div>
                    </div>
                    <p class="text-sm text-gray-500">حرم الجامعة العربية المفتوحة</p>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid lg:grid-cols-3 gap-8">

            <!-- Left Panel - GPA Calculator -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-2xl shadow-xl p-6 mb-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                        <i class="fas fa-calculator text-blue-600 ml-3"></i>
                        <span id="calculatorTitle">حاسبة المعدل التراكمي</span>
                    </h2>

                    <!-- Calculation Type -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2" id="calcTypeLabel">
                            نوع الحساب
                        </label>
                        <select id="calculationType" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="semester">المعدل الفصلي</option>
                            <option value="cumulative">المعدل التراكمي</option>
                        </select>
                    </div>

                    <!-- Previous GPA Section -->
                    <div id="previousGpaSection" class="hidden mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4" id="prevInfoTitle">المعلومات السابقة</h3>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" id="prevGpaLabel">
                                    المعدل التراكمي السابق
                                </label>
                                <input type="number" id="previousGpa" step="0.01" min="0" max="4"
                                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="0.00">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" id="prevHoursLabel">
                                    الساعات المكتسبة السابقة
                                </label>
                                <input type="number" id="previousHours" min="0"
                                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="0">
                            </div>
                        </div>
                    </div>

                    <!-- Courses Section -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold" id="coursesTitle">المواد الدراسية</h3>
                            <button id="addCourseBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-300 flex items-center">
                                <i class="fas fa-plus ml-2"></i>
                                <span id="addCourseText">إضافة مادة</span>
                            </button>
                        </div>

                        <div id="coursesContainer">
                            <!-- Courses will be added dynamically -->
                        </div>
                    </div>

                    <!-- Calculate Button -->
                    <button id="calculateBtn" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-calculator ml-2"></i>
                        <span id="calculateText">احسب المعدل</span>
                    </button>
                </div>

                <!-- Results Section -->
                <div id="resultsSection" class="bg-white rounded-2xl shadow-xl p-6 hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                        <i class="fas fa-chart-line text-green-600 ml-3"></i>
                        <span id="resultsTitle">النتائج</span>
                    </h2>

                    <div class="grid md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-graduation-cap text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2" id="semesterLabel">المعدل الفصلي</h3>
                            <p id="semesterGpaValue" class="text-3xl font-bold">0.00</p>
                        </div>

                        <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-trophy text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2" id="cumulativeLabel">المعدل التراكمي</h3>
                            <p id="cumulativeGpaValue" class="text-3xl font-bold">0.00</p>
                        </div>

                        <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-clock text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2" id="hoursLabel">مجموع الساعات</h3>
                            <p id="totalHoursValue" class="text-3xl font-bold">0</p>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-wrap gap-4 justify-center">
                        <button id="saveDataBtn" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors duration-300 flex items-center">
                            <i class="fas fa-save ml-2"></i>
                            <span id="saveText">حفظ البيانات</span>
                        </button>

                        <button id="loadDataBtn" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors duration-300 flex items-center">
                            <i class="fas fa-upload ml-2"></i>
                            <span id="loadText">تحميل البيانات</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Right Panel -->
            <div class="lg:col-span-1">
                <!-- Grade Scale -->
                <div class="bg-white rounded-2xl shadow-xl p-6 mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-chart-bar text-blue-600 ml-3"></i>
                        <span id="gradeScaleTitle">سلم الدرجات - AOU</span>
                    </h2>

                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-2 bg-green-50 rounded-lg">
                            <span class="font-semibold text-green-800">A</span>
                            <span class="text-sm text-green-600">100-90</span>
                            <span class="text-sm font-medium text-green-700">4.0</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-blue-50 rounded-lg">
                            <span class="font-semibold text-blue-800">B+</span>
                            <span class="text-sm text-blue-600">89-82</span>
                            <span class="text-sm font-medium text-blue-700">3.5</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-blue-50 rounded-lg">
                            <span class="font-semibold text-blue-800">B</span>
                            <span class="text-sm text-blue-600">81-74</span>
                            <span class="text-sm font-medium text-blue-700">3.0</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-yellow-50 rounded-lg">
                            <span class="font-semibold text-yellow-800">C+</span>
                            <span class="text-sm text-yellow-600">73-66</span>
                            <span class="text-sm font-medium text-yellow-700">2.5</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-yellow-50 rounded-lg">
                            <span class="font-semibold text-yellow-800">C</span>
                            <span class="text-sm text-yellow-600">65-58</span>
                            <span class="text-sm font-medium text-yellow-700">2.0</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-orange-50 rounded-lg">
                            <span class="font-semibold text-orange-800">D</span>
                            <span class="text-sm text-orange-600">57-50</span>
                            <span class="text-sm font-medium text-orange-700">1.5</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-red-50 rounded-lg">
                            <span class="font-semibold text-red-800">F</span>
                            <span class="text-sm text-red-600">أقل من 50</span>
                            <span class="text-sm font-medium text-red-700">0.0</span>
                        </div>
                    </div>
                </div>

                <!-- Smart Suggestions -->
                <div class="bg-white rounded-2xl shadow-xl p-6 mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 ml-3"></i>
                        <span id="suggestionsTitle">اقتراحات ذكية</span>
                    </h2>
                    <div id="smartSuggestions" class="space-y-4">
                        <p class="text-gray-500 text-center py-8" id="noSuggestionsText">
                            احسب معدلك أولاً للحصول على اقتراحات ذكية
                        </p>
                    </div>
                </div>

                <!-- AI Assistant -->
                <div class="bg-white rounded-2xl shadow-xl p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-robot text-blue-600 ml-3"></i>
                        <span id="assistantTitle">المساعد الذكي</span>
                    </h2>

                    <div id="chatContainer" class="h-64 overflow-y-auto bg-gray-50 rounded-lg p-4 mb-4">
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-comments text-4xl mb-4"></i>
                            <p id="welcomeMessage">مرحباً! أنا مساعدك الذكي لحساب المعدل التراكمي</p>
                        </div>
                    </div>

                    <div class="flex gap-2">
                        <input type="text" id="chatInput" placeholder="اسأل المساعد الذكي..."
                               class="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <button id="sendChatBtn" class="bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-300">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Share Modal -->
    <div id="shareModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
            <div class="text-center mb-6">
                <i class="fas fa-share-alt text-4xl text-green-600 mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-800" id="shareModalTitle">مشاركة المعدل التراكمي</h3>
                <p class="text-gray-600 mt-2" id="shareModalDesc">أنشئ رابط لمشاركة معدلك أو الرجوع إليه لاحقاً</p>
            </div>

            <form id="shareForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2" id="nameLabel">الاسم الكامل</label>
                    <input type="text" id="studentName" required
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="أدخل اسمك الكامل">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2" id="phoneLabel">رقم الهاتف</label>
                    <input type="tel" id="studentPhone" required
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="05xxxxxxxx">
                </div>

                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-link ml-2"></i>
                        <span id="createLinkText">إنشاء رابط</span>
                    </button>
                    <button type="button" onclick="closeShareModal()" class="flex-1 bg-gray-500 text-white py-3 rounded-lg hover:bg-gray-600 transition-colors">
                        <span id="cancelText">إلغاء</span>
                    </button>
                </div>
            </form>

            <!-- Generated Link Display -->
            <div id="generatedLink" class="hidden mt-6 p-4 bg-green-50 rounded-lg">
                <p class="text-sm text-green-800 mb-2" id="linkGeneratedText">تم إنشاء الرابط بنجاح!</p>
                <div class="flex gap-2">
                    <input type="text" id="shareUrl" readonly
                           class="flex-1 p-2 bg-white border border-green-300 rounded text-sm">
                    <button onclick="copyLink()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Modal -->
    <div id="adminModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-800" id="adminModalTitle">لوحة الإدارة</h3>
                <button onclick="closeAdminModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Admin Tabs -->
            <div class="border-b border-gray-200 mb-6">
                <nav class="flex space-x-8">
                    <button onclick="showAdminTab('stats')" class="admin-tab active py-2 px-1 border-b-2 border-purple-500 text-purple-600 font-medium">
                        <i class="fas fa-chart-bar ml-2"></i>
                        <span id="statsTabText">الإحصائيات</span>
                    </button>
                    <button onclick="showAdminTab('students')" class="admin-tab py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium">
                        <i class="fas fa-users ml-2"></i>
                        <span id="studentsTabText">بيانات الطلاب</span>
                    </button>
                </nav>
            </div>

            <!-- Statistics Tab -->
            <div id="statsTab" class="admin-content">
                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-blue-50 p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-600 text-sm font-medium">إجمالي المستخدمين</p>
                                <p id="totalUsers" class="text-3xl font-bold text-blue-900">0</p>
                            </div>
                            <i class="fas fa-users text-blue-600 text-2xl"></i>
                        </div>
                    </div>

                    <div class="bg-green-50 p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-600 text-sm font-medium">متوسط المعدل</p>
                                <p id="avgGPA" class="text-3xl font-bold text-green-900">0.00</p>
                            </div>
                            <i class="fas fa-chart-line text-green-600 text-2xl"></i>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-yellow-600 text-sm font-medium">الحسابات اليوم</p>
                                <p id="todayCalculations" class="text-3xl font-bold text-yellow-900">0</p>
                            </div>
                            <i class="fas fa-calculator text-yellow-600 text-2xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Charts would go here -->
                <div class="bg-gray-50 p-6 rounded-xl">
                    <h4 class="text-lg font-semibold mb-4">توزيع التقديرات</h4>
                    <div id="gradeDistribution" class="grid grid-cols-4 gap-4">
                        <!-- Grade distribution will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Students Tab -->
            <div id="studentsTab" class="admin-content hidden">
                <div class="mb-4">
                    <input type="text" id="studentSearch" placeholder="البحث عن طالب..."
                           class="w-full p-3 border border-gray-300 rounded-lg">
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full bg-white rounded-lg overflow-hidden">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الهاتف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المعدل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التقدير</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="studentsTableBody" class="divide-y divide-gray-200">
                            <!-- Student data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-8 text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-lg font-semibold" id="loadingText">جاري المعالجة...</p>
        </div>
    </div>


    <script src="script.js"></script>
</body>
</html>
