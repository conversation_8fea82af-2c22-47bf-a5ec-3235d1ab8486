<?php
/**
 * Test Shared Link
 * اختبار رابط المشاركة
 */

$link_id = $_GET['link'] ?? '685756f2bdf67_1750554354';

echo "<h2>اختبار رابط المشاركة</h2>";
echo "<p>الرابط المطلوب: <strong>$link_id</strong></p>";

try {
    // Direct database connection
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>1. البحث في قاعدة البيانات:</h3>";
    
    // Search for the link
    $stmt = $pdo->prepare("SELECT * FROM students WHERE share_link = ?");
    $stmt->execute([$link_id]);
    $student = $stmt->fetch();
    
    if ($student) {
        echo "✅ تم العثور على الطالب:<br>";
        echo "- الاسم: " . $student['name'] . "<br>";
        echo "- الهاتف: " . $student['phone'] . "<br>";
        echo "- الجامعة: " . $student['university'] . "<br>";
        echo "- المعدل: " . $student['cumulative_gpa'] . "<br>";
        echo "- رابط المشاركة: " . $student['share_link'] . "<br>";
        echo "- عدد الزيارات: " . ($student['link_views'] ?? 0) . "<br>";
        
        // Check expiry
        if ($student['link_expires_at']) {
            $expiryTime = strtotime($student['link_expires_at']);
            $currentTime = time();
            if ($expiryTime < $currentTime) {
                echo "❌ الرابط منتهي الصلاحية (انتهى في: " . date('Y-m-d H:i:s', $expiryTime) . ")<br>";
            } else {
                echo "✅ الرابط صالح حتى: " . date('Y-m-d H:i:s', $expiryTime) . "<br>";
            }
        } else {
            echo "✅ الرابط دائم (لا ينتهي)<br>";
        }
        
        // Try to get courses
        echo "<h3>2. فحص هيكل الجداول:</h3>";

        // Check courses table structure
        try {
            $stmt = $pdo->query("DESCRIBE courses");
            $courseColumns = $stmt->fetchAll();
            echo "أعمدة جدول courses: ";
            foreach ($courseColumns as $col) {
                echo $col['Field'] . " ";
            }
            echo "<br>";
        } catch (Exception $e) {
            echo "❌ جدول courses غير موجود: " . $e->getMessage() . "<br>";
        }

        // Check gpa_calculations table structure
        try {
            $stmt = $pdo->query("DESCRIBE gpa_calculations");
            $calcColumns = $stmt->fetchAll();
            echo "أعمدة جدول gpa_calculations: ";
            foreach ($calcColumns as $col) {
                echo $col['Field'] . " ";
            }
            echo "<br>";
        } catch (Exception $e) {
            echo "❌ جدول gpa_calculations غير موجود: " . $e->getMessage() . "<br>";
        }

        echo "<h3>3. البحث عن المواد الدراسية:</h3>";

        // Method 1: Direct from courses table by student_id
        $courses1 = [];
        try {
            $stmt = $pdo->prepare("SELECT * FROM courses WHERE student_id = ? ORDER BY created_at DESC");
            $stmt->execute([$student['id']]);
            $courses1 = $stmt->fetchAll();
        } catch (Exception $e) {
            echo "❌ خطأ في البحث في جدول courses: " . $e->getMessage() . "<br>";
        }
        
        if (!empty($courses1)) {
            echo "✅ وجدت " . count($courses1) . " مواد في جدول courses:<br>";
            foreach ($courses1 as $course) {
                echo "- " . $course['course_name'] . " (" . $course['credit_hours'] . " ساعات، تقدير: " . $course['grade'] . ")<br>";
            }
        } else {
            echo "❌ لا توجد مواد في جدول courses<br>";

            // Method 2: From student_courses table
            $courses2 = [];
            try {
                $stmt = $pdo->prepare("SELECT * FROM student_courses WHERE student_id = ?");
                $stmt->execute([$student['id']]);
                $courses2 = $stmt->fetchAll();
            } catch (Exception $e) {
                echo "❌ جدول student_courses غير موجود: " . $e->getMessage() . "<br>";
            }

            if (!empty($courses2)) {
                echo "✅ وجدت " . count($courses2) . " مواد في جدول student_courses:<br>";
                foreach ($courses2 as $course) {
                    echo "- " . $course['course_name'] . " (" . $course['credit_hours'] . " ساعات، تقدير: " . $course['grade'] . ")<br>";
                }
            } else {
                echo "❌ لا توجد مواد في أي جدول<br>";

                // Create sample courses directly in courses table
                echo "<h3>4. إنشاء مواد تجريبية:</h3>";

                // Check if courses table exists and what columns it has
                try {
                    $stmt = $pdo->query("DESCRIBE courses");
                    $courseColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);

                    $sampleCourses = [
                        ['الرياضيات', 3, 'A', 4.0],
                        ['الفيزياء', 3, 'B+', 3.5],
                        ['الكيمياء', 3, 'A-', 3.7],
                        ['اللغة الإنجليزية', 3, 'B', 3.0],
                        ['علوم الحاسوب', 3, 'A', 4.0]
                    ];

                    // Try different insert methods based on table structure
                    if (in_array('student_id', $courseColumns)) {
                        // Direct student_id column
                        $stmt = $pdo->prepare("
                            INSERT INTO courses (student_id, course_name, credit_hours, grade, grade_points)
                            VALUES (?, ?, ?, ?, ?)
                        ");

                        foreach ($sampleCourses as $course) {
                            $stmt->execute([
                                $student['id'],
                                $course[0], // course_name
                                $course[1], // credit_hours
                                $course[2], // grade
                                $course[3]  // grade_points
                            ]);
                        }
                        echo "✅ تم إضافة " . count($sampleCourses) . " مواد مباشرة في جدول courses<br>";

                    } elseif (in_array('calculation_id', $courseColumns)) {
                        // Need to create calculation first
                        try {
                            $stmt = $pdo->prepare("
                                INSERT INTO gpa_calculations (student_id, calculation_type, semester_gpa, cumulative_gpa, total_hours, total_points, classification_ar, grading_system_id, calculation_date)
                                VALUES (?, 'semester', ?, ?, ?, ?, ?, 'aou', CURDATE())
                            ");
                            $stmt->execute([
                                $student['id'],
                                $student['semester_gpa'] ?? 3.5,
                                $student['cumulative_gpa'] ?? 3.5,
                                $student['total_hours'] ?? 15,
                                ($student['total_hours'] ?? 15) * ($student['cumulative_gpa'] ?? 3.5),
                                $student['classification'] ?? 'جيد جداً'
                            ]);

                            $calculationId = $pdo->lastInsertId();
                            echo "✅ تم إنشاء سجل حساب معدل برقم: $calculationId<br>";

                            $stmt = $pdo->prepare("
                                INSERT INTO courses (calculation_id, course_name, credit_hours, grade, grade_points, course_order)
                                VALUES (?, ?, ?, ?, ?, ?)
                            ");

                            foreach ($sampleCourses as $index => $course) {
                                $stmt->execute([
                                    $calculationId,
                                    $course[0], // course_name
                                    $course[1], // credit_hours
                                    $course[2], // grade
                                    $course[3], // grade_points
                                    $index + 1  // course_order
                                ]);
                            }
                            echo "✅ تم إضافة " . count($sampleCourses) . " مواد عبر calculation_id<br>";

                        } catch (Exception $e) {
                            echo "❌ خطأ في إنشاء الحساب: " . $e->getMessage() . "<br>";
                        }
                    } else {
                        echo "❌ هيكل جدول courses غير مدعوم<br>";
                    }

                } catch (Exception $e) {
                    echo "❌ خطأ في إنشاء المواد: " . $e->getMessage() . "<br>";
                }
            }
        }
        
        echo "<h3>4. اختبار الرابط:</h3>";
        $testUrl = "http://localhost/gpa/php-version/index.php?shared=$link_id";
        echo "<a href='$testUrl' target='_blank' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار الرابط</a><br><br>";
        
    } else {
        echo "❌ لم يتم العثور على الطالب بهذا الرابط<br>";
        
        // Create a test student with this link
        echo "<h3>إنشاء طالب تجريبي:</h3>";
        
        $stmt = $pdo->prepare("
            INSERT INTO students (name, phone, email, university, cumulative_gpa, semester_gpa, total_hours, classification, share_link, link_views, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            'طالب تجريبي',
            '0501234567',
            '<EMAIL>',
            'الجامعة العربية المفتوحة',
            3.75,
            3.80,
            15,
            'جيد جداً',
            $link_id,
            0
        ]);
        
        $studentId = $pdo->lastInsertId();
        echo "✅ تم إنشاء طالب تجريبي برقم: $studentId<br>";

        // Add courses based on table structure
        $sampleCourses = [
            ['الرياضيات', 3, 'A', 4.0],
            ['الفيزياء', 3, 'B+', 3.5],
            ['الكيمياء', 3, 'A-', 3.7],
            ['اللغة الإنجليزية', 3, 'B', 3.0],
            ['علوم الحاسوب', 3, 'A', 4.0]
        ];

        try {
            // Check courses table structure
            $stmt = $pdo->query("DESCRIBE courses");
            $courseColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (in_array('student_id', $courseColumns)) {
                // Direct student_id approach
                $stmt = $pdo->prepare("
                    INSERT INTO courses (student_id, course_name, credit_hours, grade, grade_points)
                    VALUES (?, ?, ?, ?, ?)
                ");

                foreach ($sampleCourses as $course) {
                    $stmt->execute([
                        $studentId,
                        $course[0],
                        $course[1],
                        $course[2],
                        $course[3]
                    ]);
                }
                echo "✅ تم إضافة المواد مباشرة<br>";

            } elseif (in_array('calculation_id', $courseColumns)) {
                // Create calculation first
                $stmt = $pdo->prepare("
                    INSERT INTO gpa_calculations (student_id, calculation_type, semester_gpa, cumulative_gpa, total_hours, total_points, classification_ar, grading_system_id, calculation_date)
                    VALUES (?, 'semester', 3.80, 3.75, 15, 56.25, 'جيد جداً', 'aou', CURDATE())
                ");
                $stmt->execute([$studentId]);
                $calculationId = $pdo->lastInsertId();

                $stmt = $pdo->prepare("
                    INSERT INTO courses (calculation_id, course_name, credit_hours, grade, grade_points, course_order)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");

                foreach ($sampleCourses as $index => $course) {
                    $stmt->execute([
                        $calculationId,
                        $course[0],
                        $course[1],
                        $course[2],
                        $course[3],
                        $index + 1
                    ]);
                }
                echo "✅ تم إضافة المواد عبر calculation_id<br>";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في إضافة المواد: " . $e->getMessage() . "<br>";
        }
        
        echo "✅ تم إنشاء البيانات التجريبية بنجاح<br>";
        
        $testUrl = "http://localhost/gpa/php-version/index.php?shared=$link_id";
        echo "<a href='$testUrl' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار الرابط الآن</a><br><br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
</style>
