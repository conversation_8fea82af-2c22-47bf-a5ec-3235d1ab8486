<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

// Redirect publishers to their own dashboard
if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'publisher') {
    header('Location: publisher_dashboard.php');
    exit;
}

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        switch ($_POST['action']) {
            case 'add':
                $stmt = $pdo->prepare("INSERT INTO notifications (title_ar, title_en, message_ar, message_en, type, display_type, target_audience, priority, start_date, end_date, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $_POST['title_ar'],
                    $_POST['title_en'],
                    $_POST['message_ar'],
                    $_POST['message_en'],
                    $_POST['type'],
                    $_POST['display_type'],
                    $_POST['target_audience'],
                    $_POST['priority'],
                    $_POST['start_date'] ?: null,
                    $_POST['end_date'] ?: null,
                    $_SESSION['admin_id']
                ]);
                echo json_encode(['success' => true, 'message' => 'تم إضافة الإشعار بنجاح']);
                break;
                
            case 'edit':
                $stmt = $pdo->prepare("UPDATE notifications SET title_ar = ?, title_en = ?, message_ar = ?, message_en = ?, type = ?, display_type = ?, target_audience = ?, priority = ?, start_date = ?, end_date = ? WHERE id = ?");
                $stmt->execute([
                    $_POST['title_ar'],
                    $_POST['title_en'],
                    $_POST['message_ar'],
                    $_POST['message_en'],
                    $_POST['type'],
                    $_POST['display_type'],
                    $_POST['target_audience'],
                    $_POST['priority'],
                    $_POST['start_date'] ?: null,
                    $_POST['end_date'] ?: null,
                    $_POST['id']
                ]);
                echo json_encode(['success' => true, 'message' => 'تم تحديث الإشعار بنجاح']);
                break;
                
            case 'delete':
                $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                echo json_encode(['success' => true, 'message' => 'تم حذف الإشعار بنجاح']);
                break;
                
            case 'toggle_status':
                $stmt = $pdo->prepare("UPDATE notifications SET is_active = !is_active WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                echo json_encode(['success' => true, 'message' => 'تم تغيير حالة الإشعار بنجاح']);
                break;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ: ' . $e->getMessage()]);
    }
    exit();
}

// Get notifications for display
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get notifications
    $stmt = $pdo->query("
        SELECT n.*, 
               COUNT(nv.id) as view_count
        FROM notifications n 
        LEFT JOIN notification_views nv ON n.id = nv.notification_id 
        GROUP BY n.id 
        ORDER BY n.priority ASC, n.created_at DESC
    ");
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإشعارات - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إدارة الإشعارات</h1>
                        <p class="text-gray-600">إضافة وتعديل وحذف الإشعارات للمستخدمين</p>
                    </div>
                    <button onclick="openAddModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة إشعار جديد
                    </button>
                </div>
            </header>

            <!-- Main Content -->
            <main class="p-6">
                <!-- Notifications Table -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العنوان</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طريقة العرض</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الجمهور المستهدف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأولوية</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المشاهدات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (isset($notifications) && count($notifications) > 0): ?>
                                    <?php foreach ($notifications as $notification): ?>
                                        <tr>
                                            <td class="px-6 py-4 text-sm text-gray-900">
                                                <div class="font-medium"><?php echo htmlspecialchars($notification['title_ar']); ?></div>
                                                <div class="text-gray-500 text-xs"><?php echo htmlspecialchars($notification['title_en']); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                    <?php 
                                                    echo $notification['type'] === 'success' ? 'bg-green-100 text-green-800' :
                                                        ($notification['type'] === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                                                        ($notification['type'] === 'error' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'));
                                                    ?>">
                                                    <?php 
                                                    echo $notification['type'] === 'success' ? 'نجاح' :
                                                        ($notification['type'] === 'warning' ? 'تحذير' :
                                                        ($notification['type'] === 'error' ? 'خطأ' : 'معلومات'));
                                                    ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <span class="px-2 py-1 text-xs rounded-full <?php echo $notification['display_type'] === 'popup' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'; ?>">
                                                    <?php echo $notification['display_type'] === 'popup' ? 'منبثق' : 'مدمج'; ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php 
                                                echo $notification['target_audience'] === 'all' ? 'الجميع' :
                                                    ($notification['target_audience'] === 'students' ? 'الطلاب' : 'المديرين');
                                                ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <span class="font-bold text-blue-600"><?php echo $notification['priority']; ?></span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <span class="bg-gray-100 px-2 py-1 rounded-full text-xs"><?php echo $notification['view_count']; ?></span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $notification['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                                    <?php echo $notification['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button onclick="editNotification(<?php echo htmlspecialchars(json_encode($notification)); ?>)" class="text-blue-600 hover:text-blue-900 ml-2">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button onclick="toggleNotificationStatus(<?php echo $notification['id']; ?>)" class="text-yellow-600 hover:text-yellow-900 ml-2">
                                                    <i class="fas fa-toggle-<?php echo $notification['is_active'] ? 'on' : 'off'; ?>"></i>
                                                </button>
                                                <button onclick="deleteNotification(<?php echo $notification['id']; ?>)" class="text-red-600 hover:text-red-900">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                            لا توجد إشعارات مسجلة
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add/Edit Notification Modal -->
    <div id="notificationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 id="modalTitle" class="text-lg font-medium text-gray-900">إضافة إشعار جديد</h3>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="notificationForm" class="space-y-4">
                        <input type="hidden" id="notificationId" name="id">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">العنوان (عربي)</label>
                                <input type="text" id="titleAr" name="title_ar" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">العنوان (إنجليزي)</label>
                                <input type="text" id="titleEn" name="title_en" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الرسالة (عربي)</label>
                                <textarea id="messageAr" name="message_ar" rows="4" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الرسالة (إنجليزي)</label>
                                <textarea id="messageEn" name="message_en" rows="4" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">نوع الإشعار</label>
                                <select id="type" name="type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="info">معلومات</option>
                                    <option value="success">نجاح</option>
                                    <option value="warning">تحذير</option>
                                    <option value="error">خطأ</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">طريقة العرض</label>
                                <select id="displayType" name="display_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="popup">منبثق</option>
                                    <option value="inline">مدمج</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الجمهور المستهدف</label>
                                <select id="targetAudience" name="target_audience" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="all">الجميع</option>
                                    <option value="students">الطلاب</option>
                                    <option value="admins">المديرين</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الأولوية</label>
                                <input type="number" id="priority" name="priority" min="1" max="10" value="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ البداية (اختياري)</label>
                                <input type="datetime-local" id="startDate" name="start_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ النهاية (اختياري)</label>
                                <input type="datetime-local" id="endDate" name="end_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                            <button type="button" onclick="closeModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                                إلغاء
                            </button>
                            <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                                حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isEditMode = false;

        function openAddModal() {
            isEditMode = false;
            document.getElementById('modalTitle').textContent = 'إضافة إشعار جديد';
            document.getElementById('notificationForm').reset();
            document.getElementById('notificationId').value = '';
            document.getElementById('notificationModal').classList.remove('hidden');
        }

        function editNotification(notification) {
            isEditMode = true;
            document.getElementById('modalTitle').textContent = 'تعديل الإشعار';
            
            // Fill form with notification data
            document.getElementById('notificationId').value = notification.id;
            document.getElementById('titleAr').value = notification.title_ar;
            document.getElementById('titleEn').value = notification.title_en;
            document.getElementById('messageAr').value = notification.message_ar;
            document.getElementById('messageEn').value = notification.message_en;
            document.getElementById('type').value = notification.type;
            document.getElementById('displayType').value = notification.display_type;
            document.getElementById('targetAudience').value = notification.target_audience;
            document.getElementById('priority').value = notification.priority;
            
            if (notification.start_date) {
                document.getElementById('startDate').value = notification.start_date.replace(' ', 'T');
            }
            if (notification.end_date) {
                document.getElementById('endDate').value = notification.end_date.replace(' ', 'T');
            }
            
            document.getElementById('notificationModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('notificationModal').classList.add('hidden');
        }

        function deleteNotification(id) {
            if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
                fetch('admin_notifications.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=delete&id=${id}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.message);
                    }
                });
            }
        }

        function toggleNotificationStatus(id) {
            fetch('admin_notifications.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=toggle_status&id=${id}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('خطأ: ' + data.message);
                }
            });
        }

        // Handle form submission
        document.getElementById('notificationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', isEditMode ? 'edit' : 'add');
            
            fetch('admin_notifications.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('خطأ: ' + data.message);
                }
            });
        });
    </script>
</body>
</html>
