<?php
// Fix publisher user password

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing Publisher User</h2>";
    
    // Check current publisher user
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = 'publisher'");
    $stmt->execute();
    $publisher = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($publisher) {
        echo "<p>✓ Publisher user found</p>";
        echo "<p>Current details:</p>";
        echo "<ul>";
        echo "<li>ID: {$publisher['id']}</li>";
        echo "<li>Username: {$publisher['username']}</li>";
        echo "<li>Email: {$publisher['email']}</li>";
        echo "<li>Role: {$publisher['role']}</li>";
        echo "<li>Active: " . ($publisher['is_active'] ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
        
        // Update publisher with correct password and role
        $newPassword = password_hash('publisher123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE admins SET password = ?, role = 'publisher', is_active = 1, full_name = 'ناشر المواد' WHERE username = 'publisher'");
        $stmt->execute([$newPassword]);
        
        echo "<p>✓ Publisher user updated successfully!</p>";
        echo "<p><strong>Login credentials:</strong></p>";
        echo "<p>Username: <code>publisher</code></p>";
        echo "<p>Password: <code>publisher123</code></p>";
        
        // Test password verification
        if (password_verify('publisher123', $newPassword)) {
            echo "<p>✓ Password verification test passed</p>";
        } else {
            echo "<p>❌ Password verification test failed</p>";
        }
        
    } else {
        echo "<p>❌ Publisher user not found. Creating new one...</p>";
        
        // Create new publisher user
        $stmt = $pdo->prepare("INSERT INTO admins (username, email, password, full_name, role, is_active) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            'publisher', 
            '<EMAIL>', 
            password_hash('publisher123', PASSWORD_DEFAULT), 
            'ناشر المواد',
            'publisher',
            1
        ]);
        
        echo "<p>✓ New publisher user created!</p>";
    }
    
    // Show final state
    echo "<h3>Final Publisher User State:</h3>";
    $stmt = $pdo->prepare("SELECT id, username, email, full_name, role, is_active FROM admins WHERE username = 'publisher'");
    $stmt->execute();
    $finalPublisher = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($finalPublisher) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        foreach ($finalPublisher as $key => $value) {
            echo "<tr><td>$key</td><td>$value</td></tr>";
        }
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
</style>
