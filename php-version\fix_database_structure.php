<?php
/**
 * Fix Complete Database Structure
 * إصلاح هيكل قاعدة البيانات بالكامل
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$fixes = [];
$errors = [];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $fixes[] = "✅ اتصال قاعدة البيانات ناجح";
    
    // Disable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    $fixes[] = "✅ تم تعطيل فحص المفاتيح الخارجية مؤقتاً";
    
    // 1. Fix courses table - add student_id column if missing
    $stmt = $pdo->query("DESCRIBE courses");
    $courseColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('student_id', $courseColumns)) {
        $pdo->exec("ALTER TABLE courses ADD COLUMN student_id INT NULL AFTER id");
        $fixes[] = "✅ تم إضافة عمود student_id إلى جدول courses";
    } else {
        $fixes[] = "✅ عمود student_id موجود في جدول courses";
    }
    
    // 2. Fix universities table - ensure code column exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'universities'");
    if ($stmt->rowCount() == 0) {
        // Create universities table
        $pdo->exec("
            CREATE TABLE universities (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(10) UNIQUE NOT NULL,
                name_ar VARCHAR(100) NOT NULL,
                name_en VARCHAR(100) NOT NULL,
                grading_system JSON NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_code (code),
                INDEX idx_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $fixes[] = "✅ تم إنشاء جدول universities";
        
        // Insert default universities
        $defaultUniversities = [
            ['aou', 'الجامعة العربية المفتوحة', 'Arab Open University', '{"A": 4.0, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D": 1.0, "F": 0.0}'],
            ['ksu', 'جامعة الملك سعود', 'King Saud University', '{"A+": 4.0, "A": 3.75, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D+": 1.5, "D": 1.0, "F": 0.0}'],
            ['kau', 'جامعة الملك عبدالعزيز', 'King Abdulaziz University', '{"A+": 4.0, "A": 3.75, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D+": 1.5, "D": 1.0, "F": 0.0}']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO universities (code, name_ar, name_en, grading_system) VALUES (?, ?, ?, ?)");
        foreach ($defaultUniversities as $uni) {
            $stmt->execute($uni);
        }
        $fixes[] = "✅ تم إدراج الجامعات الافتراضية";
    } else {
        // Check if code column exists
        $stmt = $pdo->query("DESCRIBE universities");
        $uniColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('code', $uniColumns)) {
            $pdo->exec("ALTER TABLE universities ADD COLUMN code VARCHAR(10) UNIQUE NOT NULL AFTER id");
            $fixes[] = "✅ تم إضافة عمود code إلى جدول universities";
        } else {
            $fixes[] = "✅ عمود code موجود في جدول universities";
        }
    }
    
    // 3. Ensure students table has correct structure
    $stmt = $pdo->query("DESCRIBE students");
    $studentColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredStudentColumns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'name' => 'VARCHAR(100) NOT NULL',
        'phone' => 'VARCHAR(20) NOT NULL',
        'email' => 'VARCHAR(100) NULL',
        'student_id' => 'VARCHAR(50) NULL',
        'university' => 'VARCHAR(50) NOT NULL DEFAULT "aou"',
        'grading_system' => 'VARCHAR(50) NOT NULL DEFAULT "aou"',
        'semester_gpa' => 'DECIMAL(3,2) NULL',
        'cumulative_gpa' => 'DECIMAL(3,2) NULL',
        'total_hours' => 'INT DEFAULT 0',
        'previous_gpa' => 'DECIMAL(3,2) NULL',
        'previous_hours' => 'INT DEFAULT 0',
        'classification' => 'VARCHAR(50) NULL',
        'ip_address' => 'VARCHAR(45) NULL',
        'user_agent' => 'TEXT NULL',
        'share_link' => 'VARCHAR(100) UNIQUE NULL',
        'link_expires_at' => 'TIMESTAMP NULL',
        'is_verified' => 'BOOLEAN DEFAULT FALSE',
        'notes' => 'TEXT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    foreach ($requiredStudentColumns as $column => $definition) {
        if (!in_array($column, $studentColumns) && $column !== 'id') {
            try {
                $pdo->exec("ALTER TABLE students ADD COLUMN $column $definition");
                $fixes[] = "✅ تم إضافة عمود $column إلى جدول students";
            } catch (PDOException $e) {
                $errors[] = "❌ فشل في إضافة عمود $column: " . $e->getMessage();
            }
        }
    }
    
    // 4. Ensure courses table has correct structure
    $stmt = $pdo->query("DESCRIBE courses");
    $courseColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredCourseColumns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'student_id' => 'INT NULL',
        'course_name' => 'VARCHAR(100) NOT NULL',
        'course_code' => 'VARCHAR(20) NULL',
        'credit_hours' => 'INT NOT NULL DEFAULT 3',
        'grade' => 'VARCHAR(5) NOT NULL',
        'grade_points' => 'DECIMAL(3,2) NOT NULL DEFAULT 0',
        'semester' => 'VARCHAR(20) NULL',
        'year' => 'YEAR NULL',
        'is_repeated' => 'BOOLEAN DEFAULT FALSE',
        'notes' => 'TEXT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    foreach ($requiredCourseColumns as $column => $definition) {
        if (!in_array($column, $courseColumns) && $column !== 'id') {
            try {
                $pdo->exec("ALTER TABLE courses ADD COLUMN $column $definition");
                $fixes[] = "✅ تم إضافة عمود $column إلى جدول courses";
            } catch (PDOException $e) {
                $errors[] = "❌ فشل في إضافة عمود $column: " . $e->getMessage();
            }
        }
    }
    
    // 5. Add indexes for better performance
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_students_university ON students(university)",
        "CREATE INDEX IF NOT EXISTS idx_students_created_at ON students(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_students_share_link ON students(share_link)",
        "CREATE INDEX IF NOT EXISTS idx_students_gpa ON students(cumulative_gpa)",
        "CREATE INDEX IF NOT EXISTS idx_courses_student_id ON courses(student_id)",
        "CREATE INDEX IF NOT EXISTS idx_courses_grade ON courses(grade)"
    ];
    
    foreach ($indexes as $indexSql) {
        try {
            $pdo->exec($indexSql);
            $fixes[] = "✅ تم إنشاء فهرس للأداء";
        } catch (PDOException $e) {
            // Index might already exist
            $fixes[] = "ℹ️ فهرس موجود مسبقاً";
        }
    }
    
    // 6. Test data insertion
    $testStudentData = [
        'طالب اختبار ' . date('H:i:s'),
        '050' . rand(1000000, 9999999),
        null, // email
        null, // student_id
        'aou',
        'aou',
        3.75,
        3.75,
        15,
        0,
        0,
        'ممتاز',
        '127.0.0.1',
        'Test Browser',
        uniqid() . '_' . time(),
        date('Y-m-d H:i:s', strtotime('+30 days')),
        0, // is_verified
        'بيانات اختبار'
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO students (name, phone, email, student_id, university, grading_system, semester_gpa, 
                            cumulative_gpa, total_hours, previous_gpa, previous_hours, classification, 
                            ip_address, user_agent, share_link, link_expires_at, is_verified, notes) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    if ($stmt->execute($testStudentData)) {
        $studentId = $pdo->lastInsertId();
        $fixes[] = "✅ تم اختبار إدراج طالب جديد بنجاح (ID: $studentId)";
        
        // Test course insertion
        $testCourseData = [
            $studentId,
            'مادة اختبار',
            'TEST101',
            3,
            'A',
            4.0,
            'الفصل الأول',
            date('Y'),
            0, // is_repeated
            'مادة اختبار'
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO courses (student_id, course_name, course_code, credit_hours, grade, 
                               grade_points, semester, year, is_repeated, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        if ($stmt->execute($testCourseData)) {
            $fixes[] = "✅ تم اختبار إدراج مادة جديدة بنجاح";
        } else {
            $errors[] = "❌ فشل في اختبار إدراج مادة جديدة";
        }
    } else {
        $errors[] = "❌ فشل في اختبار إدراج طالب جديد";
    }
    
    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    $fixes[] = "✅ تم إعادة تفعيل فحص المفاتيح الخارجية";
    
    // Log this fix
    try {
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (action, description, ip_address, created_at) 
            VALUES ('database_structure_fix', 'تم إصلاح هيكل قاعدة البيانات بالكامل', ?, NOW())
        ");
        $stmt->execute([$_SERVER['REMOTE_ADDR'] ?? '127.0.0.1']);
        $fixes[] = "✅ تم تسجيل عملية الإصلاح";
    } catch (Exception $e) {
        $errors[] = "تحذير: فشل في تسجيل عملية الإصلاح: " . $e->getMessage();
    }
    
} catch (PDOException $e) {
    $errors[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح هيكل قاعدة البيانات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .fix-item {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-database text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">إصلاح هيكل قاعدة البيانات</h1>
                <p class="text-gray-600">إصلاح شامل لجميع الجداول والأعمدة المفقودة</p>
            </div>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Fixes -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-green-700 mb-4">
                    <i class="fas fa-check-circle ml-2"></i>
                    الإصلاحات المكتملة (<?php echo count($fixes); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php foreach ($fixes as $index => $fix): ?>
                        <div class="fix-item bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg text-sm" 
                             style="animation-delay: <?php echo $index * 0.1; ?>s">
                            <?php echo htmlspecialchars($fix); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Errors -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-red-700 mb-4">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    المشاكل المتبقية (<?php echo count($errors); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($errors)): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-thumbs-up ml-2"></i>
                                <span>تم إصلاح جميع المشاكل بنجاح!</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($errors as $error): ?>
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg text-sm">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Database Structure Summary -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">ملخص هيكل قاعدة البيانات</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="border rounded-lg p-4">
                    <h3 class="font-semibold text-gray-700 mb-2">جدول الطلاب (students)</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ معلومات الطالب الأساسية</li>
                        <li>✅ بيانات المعدل والساعات</li>
                        <li>✅ معلومات المشاركة والانتهاء</li>
                        <li>✅ فهارس للأداء</li>
                    </ul>
                </div>
                
                <div class="border rounded-lg p-4">
                    <h3 class="font-semibold text-gray-700 mb-2">جدول المواد (courses)</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ ربط مع الطلاب</li>
                        <li>✅ معلومات المادة والدرجات</li>
                        <li>✅ بيانات الفصل والسنة</li>
                        <li>✅ فهارس للأداء</li>
                    </ul>
                </div>
                
                <div class="border rounded-lg p-4">
                    <h3 class="font-semibold text-gray-700 mb-2">جدول الجامعات (universities)</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ معلومات الجامعة</li>
                        <li>✅ أنظمة التقدير</li>
                        <li>✅ حالة التفعيل</li>
                        <li>✅ فهارس للأداء</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">نتائج الاختبار</h2>
            
            <?php
            try {
                // Show current table structures
                $tables = ['students', 'courses', 'universities'];
                foreach ($tables as $table) {
                    echo "<div class='mb-4'>";
                    echo "<h3 class='font-semibold text-gray-700 mb-2'>جدول $table:</h3>";
                    
                    $stmt = $pdo->query("DESCRIBE $table");
                    $columns = $stmt->fetchAll();
                    
                    echo "<div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 text-sm'>";
                    foreach ($columns as $column) {
                        echo "<div class='bg-gray-100 px-3 py-1 rounded'>";
                        echo "<span class='font-medium'>" . $column['Field'] . "</span>";
                        echo "<span class='text-gray-600 text-xs ml-2'>" . $column['Type'] . "</span>";
                        echo "</div>";
                    }
                    echo "</div>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<p class='text-red-600'>خطأ في عرض هيكل الجداول: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            ?>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
            <?php if (empty($errors)): ?>
                <a href="test_student_save.php" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center">
                    <i class="fas fa-test-tube ml-2"></i>
                    اختبار حفظ الطلاب
                </a>
                
                <a href="admin_universities.php" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center">
                    <i class="fas fa-university ml-2"></i>
                    إدارة الجامعات
                </a>
                
                <a href="index.php" class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center">
                    <i class="fas fa-home ml-2"></i>
                    الصفحة الرئيسية
                </a>
            <?php else: ?>
                <button onclick="location.reload()" class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة المحاولة
                </button>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="mt-8 text-center text-sm text-gray-500">
            <p>تم إصلاح هيكل قاعدة البيانات في: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p class="mt-1">جميع الجداول والأعمدة جاهزة للاستخدام!</p>
        </div>
    </div>

    <script>
        // Show success message
        <?php if (empty($errors)): ?>
        setTimeout(() => {
            alert('تم إصلاح هيكل قاعدة البيانات بنجاح!\nجميع الجداول والأعمدة جاهزة للاستخدام.');
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
