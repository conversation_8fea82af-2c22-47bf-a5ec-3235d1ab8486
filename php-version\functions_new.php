<?php
/**
 * GPA Calculator Functions
 * Enhanced with MySQL Database Support
 */

/**
 * Sanitize input data
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

/**
 * Calculate GPA from courses data
 */
function calculateGPA($courses, $previousGPA = 0, $previousHours = 0) {
    $totalPoints = 0;
    $totalHours = 0;
    $gradingSystem = getCurrentGradingSystem();
    
    foreach ($courses as $course) {
        $hours = intval($course['hours']);
        $grade = $course['grade'];
        
        if (isset($gradingSystem['grades'][$grade])) {
            $points = $gradingSystem['grades'][$grade]['points'];
            $totalPoints += $points * $hours;
            $totalHours += $hours;
        }
    }
    
    // Calculate semester GPA
    $semesterGPA = $totalHours > 0 ? $totalPoints / $totalHours : 0;
    
    // Calculate cumulative GPA if previous data exists
    $cumulativeGPA = $semesterGPA;
    if ($previousHours > 0) {
        $totalCumulativePoints = $totalPoints + ($previousGPA * $previousHours);
        $totalCumulativeHours = $totalHours + $previousHours;
        $cumulativeGPA = $totalCumulativeHours > 0 ? $totalCumulativePoints / $totalCumulativeHours : 0;
    }
    
    return [
        'semester_gpa' => round($semesterGPA, 2),
        'cumulative_gpa' => round($cumulativeGPA, 2),
        'total_hours' => $totalHours,
        'total_points' => round($totalPoints, 2),
        'courses_count' => count($courses)
    ];
}

/**
 * Get GPA classification
 */
function getGPAClassification($gpa) {
    if ($gpa >= 3.75) {
        return [
            'name' => 'ممتاز',
            'name_en' => 'Excellent',
            'class' => 'excellent'
        ];
    } elseif ($gpa >= 3.25) {
        return [
            'name' => 'جيد جداً مرتفع',
            'name_en' => 'Very Good High',
            'class' => 'very-good-high'
        ];
    } elseif ($gpa >= 2.75) {
        return [
            'name' => 'جيد جداً',
            'name_en' => 'Very Good',
            'class' => 'very-good'
        ];
    } elseif ($gpa >= 2.25) {
        return [
            'name' => 'جيد مرتفع',
            'name_en' => 'Good High',
            'class' => 'good-high'
        ];
    } elseif ($gpa >= 2.0) {
        return [
            'name' => 'جيد',
            'name_en' => 'Good',
            'class' => 'good'
        ];
    } elseif ($gpa >= 1.0) {
        return [
            'name' => 'مقبول',
            'name_en' => 'Pass',
            'class' => 'pass'
        ];
    } else {
        return [
            'name' => 'راسب',
            'name_en' => 'Fail',
            'class' => 'fail'
        ];
    }
}

/**
 * Get current grading system
 */
function getCurrentGradingSystem() {
    global $all_grading_systems;
    $system = $_SESSION['grading_system'] ?? DEFAULT_GRADING_SYSTEM;
    return $all_grading_systems[$system] ?? $all_grading_systems[DEFAULT_GRADING_SYSTEM];
}

/**
 * Save courses data (fallback to file system)
 */
function saveCourses($courses, $studentId = null) {
    try {
        $filename = $studentId ? "courses_{$studentId}.json" : "courses_" . session_id() . ".json";
        $filepath = DATA_DIR . $filename;
        
        $data = [
            'courses' => $courses,
            'timestamp' => time(),
            'session_id' => session_id()
        ];
        
        return file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT)) !== false;
    } catch (Exception $e) {
        error_log("Save courses error: " . $e->getMessage());
        return false;
    }
}

/**
 * Load courses data (fallback to file system)
 */
function loadCourses($studentId = null) {
    try {
        $filename = $studentId ? "courses_{$studentId}.json" : "courses_" . session_id() . ".json";
        $filepath = DATA_DIR . $filename;
        
        if (file_exists($filepath)) {
            $data = json_decode(file_get_contents($filepath), true);
            return $data['courses'] ?? [];
        }
        
        return [];
    } catch (Exception $e) {
        error_log("Load courses error: " . $e->getMessage());
        return [];
    }
}

/**
 * Handle AJAX requests
 */
if (isset($_POST['action'])) {
    global $all_grading_systems;
    
    switch ($_POST['action']) {
        case 'calculate_gpa':
            $courses = json_decode($_POST['courses'], true);
            $previousGPA = floatval($_POST['previous_gpa'] ?? 0);
            $previousHours = intval($_POST['previous_hours'] ?? 0);
            
            $result = calculateGPA($courses, $previousGPA, $previousHours);
            $result['classification'] = getGPAClassification($result['cumulative_gpa']);
            
            echo json_encode($result);
            break;
            
        case 'save_courses':
            $courses = json_decode($_POST['courses'], true);
            $success = saveCourses($courses);
            echo json_encode(['success' => $success]);
            break;
            
        case 'load_courses':
            $courses = loadCourses();
            echo json_encode(['courses' => $courses]);
            break;
            
        case 'change_language':
            $language = sanitizeInput($_POST['language']);
            if (in_array($language, ['ar', 'en'])) {
                $_SESSION['language'] = $language;
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['error' => 'لغة غير صحيحة']);
            }
            break;
            
        case 'change_grading_system':
            $system = sanitizeInput($_POST['system']);
            if (isset($all_grading_systems[$system])) {
                $_SESSION['grading_system'] = $system;
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['error' => 'نظام تقدير غير صحيح']);
            }
            break;
            
        case 'save_student_data':
            try {
                $name = sanitizeInput($_POST['name']);
                $phone = sanitizeInput($_POST['phone']);
                $gpa = floatval($_POST['gpa']);
                $courses = json_decode($_POST['courses'], true);
                $calculationType = sanitizeInput($_POST['calculation_type'] ?? 'semester');
                $previousGPA = floatval($_POST['previous_gpa'] ?? 0);
                $previousHours = intval($_POST['previous_hours'] ?? 0);
                $university = $_SESSION['selected_university'] ?? 'aou';
                $gradingSystem = $_SESSION['grading_system'] ?? 'aou';
                
                // Calculate GPA details
                $gpaResult = calculateGPA($courses, $previousGPA, $previousHours);
                $classification = getGPAClassification($gpaResult['cumulative_gpa']);
                
                // Prepare data for database
                $studentData = [
                    'name' => $name,
                    'phone' => $phone,
                    'university_id' => $university
                ];
                
                $calculationData = [
                    'type' => $calculationType,
                    'semester_gpa' => $gpaResult['semester_gpa'],
                    'cumulative_gpa' => $gpaResult['cumulative_gpa'],
                    'total_hours' => $gpaResult['total_hours'],
                    'total_points' => $gpaResult['total_points'],
                    'classification_ar' => $classification['name'],
                    'classification_en' => $classification['name_en'],
                    'previous_gpa' => $previousGPA,
                    'previous_hours' => $previousHours,
                    'grading_system_id' => $gradingSystem
                ];
                
                $coursesData = [];
                foreach ($courses as $course) {
                    $gradingSystemData = getCurrentGradingSystem();
                    $gradePoints = $gradingSystemData['grades'][$course['grade']]['points'] ?? 0;
                    
                    $coursesData[] = [
                        'name' => $course['name'],
                        'hours' => intval($course['hours']),
                        'grade' => $course['grade'],
                        'grade_points' => $gradePoints
                    ];
                }
                
                // Save to database
                $calculationId = saveStudentCalculation($studentData, $calculationData, $coursesData);
                
                // Create shared link
                $linkId = createSharedLink($calculationId);
                
                echo json_encode([
                    'success' => true,
                    'calculation_id' => $calculationId,
                    'link_id' => $linkId
                ]);
                
            } catch (Exception $e) {
                error_log("Save student data error: " . $e->getMessage());
                echo json_encode(['error' => 'فشل في حفظ البيانات: ' . $e->getMessage()]);
            }
            break;
            
        case 'get_shared_data':
            try {
                $linkId = sanitizeInput($_POST['link_id']);
                $data = getSharedCalculation($linkId);
                
                if ($data) {
                    echo json_encode(['success' => true, 'data' => $data]);
                } else {
                    echo json_encode(['error' => 'الرابط غير صحيح أو منتهي الصلاحية']);
                }
                
            } catch (Exception $e) {
                error_log("Get shared data error: " . $e->getMessage());
                echo json_encode(['error' => 'خطأ في تحميل البيانات المشاركة']);
            }
            break;
    }
    exit;
}

/**
 * Sanitize input (alias for backward compatibility)
 */
function sanitize_input($input) {
    return sanitizeInput($input);
}

/**
 * Load courses (alias for backward compatibility)
 */
function load_courses($student_id = null) {
    return loadCourses($student_id);
}

/**
 * Save courses (alias for backward compatibility)
 */
function save_courses($courses, $student_id = null) {
    return saveCourses($courses, $student_id);
}
?>
