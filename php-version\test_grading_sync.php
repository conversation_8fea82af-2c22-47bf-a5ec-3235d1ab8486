<?php
/**
 * Test Grading System Sync
 * اختبار مزامنة نظام التقدير
 */

require_once 'db_config.php';

echo "<h2>اختبار مزامنة نظام التقدير مع الجامعات</h2>";

try {
    $db = getDB();
    
    // 1. Check universities and their grading systems
    echo "<h3>1. الجامعات وأنظمة التقدير:</h3>";
    $universities = fetchAll("SELECT id, name_ar, grading_system FROM universities WHERE is_active = 1");
    
    if (empty($universities)) {
        echo "❌ لا توجد جامعات نشطة<br>";
        
        // Add sample universities
        echo "<h4>إضافة جامعات تجريبية:</h4>";
        $sampleUniversities = [
            ['aou', 'الجامعة العربية المفتوحة', 'aou'],
            ['ksu', 'جامعة الملك سعود', 'standard'],
            ['kau', 'جامعة الملك عبدالعزيز', 'standard'],
            ['simple', 'جامعة بنظام مبسط', 'simple']
        ];
        
        foreach ($sampleUniversities as $uni) {
            executeQuery("
                INSERT INTO universities (id, name_ar, name_en, grading_system, is_active) 
                VALUES (?, ?, ?, ?, 1)
                ON DUPLICATE KEY UPDATE grading_system = VALUES(grading_system)
            ", [$uni[0], $uni[1], $uni[1], $uni[2]]);
            echo "✅ تم إضافة: {$uni[1]} - نظام التقدير: {$uni[2]}<br>";
        }
        
        $universities = fetchAll("SELECT id, name_ar, grading_system FROM universities WHERE is_active = 1");
    }
    
    foreach ($universities as $uni) {
        echo "- {$uni['name_ar']} (ID: {$uni['id']}) - نظام التقدير: {$uni['grading_system']}<br>";
    }
    
    // 2. Check grading systems
    echo "<h3>2. أنظمة التقدير المتاحة:</h3>";
    $gradingSystems = fetchAll("SELECT * FROM grading_systems WHERE is_active = 1");
    
    if (empty($gradingSystems)) {
        echo "❌ لا توجد أنظمة تقدير<br>";
        
        // Add sample grading systems
        echo "<h4>إضافة أنظمة تقدير تجريبية:</h4>";
        $sampleSystems = [
            ['aou', 'نظام الجامعة العربية المفتوحة', 'AOU Grading System'],
            ['standard', 'النظام الأكاديمي المعياري', 'Standard Academic System'],
            ['simple', 'النظام المبسط', 'Simple System']
        ];
        
        foreach ($sampleSystems as $sys) {
            executeQuery("
                INSERT INTO grading_systems (id, name_ar, name_en, is_active) 
                VALUES (?, ?, ?, 1)
                ON DUPLICATE KEY UPDATE name_ar = VALUES(name_ar)
            ", $sys);
            echo "✅ تم إضافة نظام: {$sys[1]}<br>";
        }
        
        $gradingSystems = fetchAll("SELECT * FROM grading_systems WHERE is_active = 1");
    }
    
    foreach ($gradingSystems as $system) {
        echo "- {$system['name_ar']} (ID: {$system['id']})<br>";
        
        // Show grades for this system
        $grades = fetchAll("SELECT * FROM grading_scales WHERE grading_system_id = ? ORDER BY min_percentage DESC", [$system['id']]);
        if (!empty($grades)) {
            echo "  <small>الدرجات: ";
            foreach ($grades as $grade) {
                echo "{$grade['grade']} ({$grade['points']} نقاط، {$grade['min_percentage']}-{$grade['max_percentage']}%) ";
            }
            echo "</small><br>";
        } else {
            echo "  <small style='color: red;'>لا توجد درجات محددة</small><br>";
        }
    }
    
    // 3. Test university-grading system sync
    echo "<h3>3. اختبار مزامنة الجامعة مع نظام التقدير:</h3>";
    
    foreach ($universities as $uni) {
        echo "<h4>جامعة: {$uni['name_ar']}</h4>";
        
        // Get grading system for this university
        $gradingSystem = fetchOne("SELECT * FROM grading_systems WHERE id = ?", [$uni['grading_system']]);
        
        if ($gradingSystem) {
            echo "✅ نظام التقدير: {$gradingSystem['name_ar']}<br>";
            
            // Get grades
            $grades = fetchAll("SELECT * FROM grading_scales WHERE grading_system_id = ? ORDER BY min_percentage DESC", [$gradingSystem['id']]);
            
            if (!empty($grades)) {
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr><th>الدرجة</th><th>النقاط</th><th>الوصف</th><th>النسبة المئوية</th></tr>";
                foreach ($grades as $grade) {
                    echo "<tr>";
                    echo "<td style='padding: 5px; text-align: center; font-weight: bold;'>{$grade['grade']}</td>";
                    echo "<td style='padding: 5px; text-align: center;'>{$grade['points']}</td>";
                    echo "<td style='padding: 5px;'>{$grade['description_ar']}</td>";
                    echo "<td style='padding: 5px; text-align: center;'>{$grade['min_percentage']}% - {$grade['max_percentage']}%</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "❌ لا توجد درجات لهذا النظام<br>";
            }
        } else {
            echo "❌ نظام التقدير غير موجود: {$uni['grading_system']}<br>";
        }
        
        echo "<hr>";
    }
    
    // 4. Test API endpoint
    echo "<h3>4. اختبار API تغيير الجامعة:</h3>";
    
    foreach ($universities as $uni) {
        echo "<button onclick=\"testUniversityChange('{$uni['id']}')\" style='margin: 5px; padding: 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;'>
                اختبار {$uni['name_ar']}
              </button>";
    }
    
    echo "<div id='testResults' style='margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 5px;'></div>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>

<script>
async function testUniversityChange(universityId) {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<p>جاري الاختبار...</p>';
    
    try {
        const response = await fetch('index.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'change_university',
                university: universityId
            })
        });
        
        const result = await response.json();
        
        if (result.success && result.grading_system) {
            let html = `
                <h4>✅ نجح تغيير الجامعة إلى: ${universityId}</h4>
                <p><strong>نظام التقدير:</strong> ${result.grading_system.name}</p>
                <h5>الدرجات:</h5>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr>
                        <th style="padding: 8px;">الدرجة</th>
                        <th style="padding: 8px;">النقاط</th>
                        <th style="padding: 8px;">الوصف</th>
                        <th style="padding: 8px;">النسبة المئوية</th>
                    </tr>
            `;
            
            Object.keys(result.grading_system.grades).forEach(grade => {
                const info = result.grading_system.grades[grade];
                html += `
                    <tr>
                        <td style="padding: 8px; text-align: center; font-weight: bold;">${grade}</td>
                        <td style="padding: 8px; text-align: center;">${info.points}</td>
                        <td style="padding: 8px;">${info.description}</td>
                        <td style="padding: 8px; text-align: center;">
                            ${info.min_percentage !== undefined ? `${info.min_percentage}% - ${info.max_percentage}%` : 'غير محدد'}
                        </td>
                    </tr>
                `;
            });
            
            html += '</table>';
            resultsDiv.innerHTML = html;
        } else {
            resultsDiv.innerHTML = `<p style="color: red;">❌ فشل: ${result.error || 'خطأ غير معروف'}</p>`;
        }
        
    } catch (error) {
        resultsDiv.innerHTML = `<p style="color: red;">❌ خطأ في الشبكة: ${error.message}</p>`;
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
button:hover { background-color: #005a8b !important; }
</style>

<br><br>
<a href="index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
    <i class="fas fa-home"></i> العودة للصفحة الرئيسية
</a>
<a href="admin_universities.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
    <i class="fas fa-cog"></i> إدارة الجامعات
</a>
