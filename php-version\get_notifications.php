<?php
session_start();

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

header('Content-Type: application/json');

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get user session ID for tracking views
    $userSession = session_id();

    // Determine user type (for targeting)
    $userType = 'students'; // Default to students
    if (isset($_SESSION['admin_id'])) {
        $userType = 'admins';
    }

    // Get current date for filtering
    $currentDate = date('Y-m-d H:i:s');

    // Debug logging
    error_log("Notifications Debug - User Session: $userSession, User Type: $userType, Current Date: $currentDate");
    
    // Get active notifications that haven't been viewed by this user
    $sql = "
        SELECT n.* 
        FROM notifications n 
        LEFT JOIN notification_views nv ON n.id = nv.notification_id AND nv.user_session = ?
        WHERE n.is_active = 1 
        AND (n.target_audience = 'all' OR n.target_audience = ?)
        AND (n.start_date IS NULL OR n.start_date <= ?)
        AND (n.end_date IS NULL OR n.end_date >= ?)
        AND nv.id IS NULL
        ORDER BY n.priority ASC, n.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$userSession, $userType, $currentDate, $currentDate]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Debug logging
    error_log("Notifications Debug - Found " . count($notifications) . " notifications");

    // Separate popup and inline notifications
    $popupNotifications = [];
    $inlineNotifications = [];

    foreach ($notifications as $notification) {
        if ($notification['display_type'] === 'popup') {
            $popupNotifications[] = $notification;
        } else {
            $inlineNotifications[] = $notification;
        }
    }

    // Debug logging
    error_log("Notifications Debug - Popup: " . count($popupNotifications) . ", Inline: " . count($inlineNotifications));

    echo json_encode([
        'success' => true,
        'popup' => $popupNotifications,
        'inline' => $inlineNotifications
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
