<?php
require_once 'functions_simple.php';

// Get global variables
global $universities, $grading_systems, $current_language;

// Handle university selection
if (isset($_POST['action']) && $_POST['action'] === 'select_university') {
    $university_id = $_POST['university_id'] ?? '';
    if (isset($universities[$university_id])) {
        $_SESSION['selected_university'] = $university_id;
        $_SESSION['grading_system'] = $universities[$university_id]['grading_system'];
        $_SESSION['university'] = $university_id;
        echo json_encode(['success' => true]);
        exit;
    }
}

$current_university = $_SESSION['selected_university'] ?? 'aou';
$selected_university = $universities[$current_university] ?? $universities['aou'];
?>

<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo $page_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $current_language === 'ar' ? 'الجامعات المدعومة' : 'Supported Universities'; ?> - <?php echo t('app_title'); ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        .university-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .university-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }
        
        .university-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        
        .university-card:hover::before {
            left: 100%;
        }
        
        .university-card.selected {
            border-color: #3B82F6;
            background: linear-gradient(135deg, #EBF4FF 0%, #DBEAFE 100%);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen <?php echo $page_direction; ?>">

    <!-- Top Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Title -->
                <div class="flex items-center gap-4">
                    <div class="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xl">
                        AOU
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800"><?php echo $current_language === 'ar' ? 'الجامعات المدعومة' : 'Supported Universities'; ?></h1>
                        <p class="text-sm text-gray-600"><?php echo t('app_title'); ?></p>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex items-center gap-4">
                    <a href="index.php" class="bg-blue-100 text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors">
                        <i class="fas fa-calculator <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'حاسبة المعدل' : 'GPA Calculator'; ?></span>
                    </a>
                    <a href="admin.php" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-cog <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'لوحة الإدارة' : 'Admin Panel'; ?></span>
                    </a>
                    <button id="languageToggle" class="bg-green-100 text-green-600 px-4 py-2 rounded-lg hover:bg-green-200 transition-colors">
                        <i class="fas fa-language <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'English' : 'العربية'; ?></span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        
        <!-- Header Section -->
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">
                <i class="fas fa-university text-blue-600 <?php echo $current_language === 'ar' ? 'ml-4' : 'mr-4'; ?>"></i>
                <?php echo $current_language === 'ar' ? 'الجامعات المدعومة' : 'Supported Universities'; ?>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                <?php echo $current_language === 'ar' ? 
                    'اختر جامعتك لاستخدام نظام التقدير المناسب لحساب معدلك التراكمي بدقة' : 
                    'Choose your university to use the appropriate grading system for accurate GPA calculation'; ?>
            </p>
        </div>

        <!-- Current Selection -->
        <div class="bg-white rounded-2xl shadow-xl p-6 mb-8">
            <h3 class="text-2xl font-bold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-check-circle text-green-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                <?php echo $current_language === 'ar' ? 'الجامعة المختارة حالياً' : 'Currently Selected University'; ?>
            </h3>
            
            <div class="bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-xl p-6">
                <div class="flex items-center gap-4">
                    <div class="text-4xl"><?php echo $selected_university['logo']; ?></div>
                    <div class="flex-1">
                        <h4 class="text-2xl font-bold"><?php echo $current_language === 'ar' ? $selected_university['name'] : $selected_university['name_en']; ?></h4>
                        <p class="text-blue-100 mt-1"><?php echo $current_language === 'ar' ? $selected_university['description'] : $selected_university['description_en']; ?></p>
                        <div class="flex gap-4 mt-3 text-sm">
                            <span><i class="fas fa-calendar <?php echo $current_language === 'ar' ? 'ml-1' : 'mr-1'; ?>"></i> <?php echo $selected_university['established']; ?></span>
                            <span><i class="fas fa-users <?php echo $current_language === 'ar' ? 'ml-1' : 'mr-1'; ?>"></i> <?php echo $selected_university['students']; ?></span>
                            <span><i class="fas fa-globe <?php echo $current_language === 'ar' ? 'ml-1' : 'mr-1'; ?>"></i> <?php echo $current_language === 'ar' ? $selected_university['country'] : $selected_university['country_en']; ?></span>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="bg-white bg-opacity-20 rounded-lg p-3">
                            <div class="text-sm opacity-90"><?php echo $current_language === 'ar' ? 'نظام التقدير' : 'Grading System'; ?></div>
                            <div class="text-lg font-bold"><?php echo strtoupper($selected_university['grading_system']); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Universities Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($universities as $id => $university): ?>
                <div class="university-card bg-white rounded-2xl shadow-lg p-6 cursor-pointer <?php echo $id === $current_university ? 'selected' : ''; ?>" 
                     onclick="selectUniversity('<?php echo $id; ?>')">
                    
                    <!-- University Header -->
                    <div class="text-center mb-6">
                        <div class="text-5xl mb-4"><?php echo $university['logo']; ?></div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">
                            <?php echo $current_language === 'ar' ? $university['name'] : $university['name_en']; ?>
                        </h3>
                        <p class="text-gray-600 text-sm">
                            <?php echo $current_language === 'ar' ? $university['country'] : $university['country_en']; ?>
                        </p>
                    </div>

                    <!-- University Info -->
                    <div class="space-y-3 mb-6">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600"><?php echo $current_language === 'ar' ? 'تأسست:' : 'Established:'; ?></span>
                            <span class="font-semibold"><?php echo $university['established']; ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600"><?php echo $current_language === 'ar' ? 'الطلاب:' : 'Students:'; ?></span>
                            <span class="font-semibold"><?php echo $university['students']; ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600"><?php echo $current_language === 'ar' ? 'نظام التقدير:' : 'Grading System:'; ?></span>
                            <span class="font-semibold text-blue-600"><?php echo strtoupper($university['grading_system']); ?></span>
                        </div>
                    </div>

                    <!-- Grading System Preview -->
                    <div class="border-t pt-4">
                        <h4 class="text-sm font-semibold text-gray-700 mb-3"><?php echo $current_language === 'ar' ? 'سلم الدرجات:' : 'Grade Scale:'; ?></h4>
                        <div class="grid grid-cols-3 gap-2 text-xs">
                            <?php
                            $system = $grading_systems[$university['grading_system']] ?? $grading_systems['aou'];
                            $count = 0;
                            foreach ($system['grades'] as $grade => $info):
                                if ($count >= 6) break; // Show only first 6 grades
                            ?>
                                <div class="bg-gray-50 rounded p-1 text-center">
                                    <div class="font-bold text-blue-600"><?php echo $grade; ?></div>
                                    <div class="text-gray-500"><?php echo $info['points']; ?></div>
                                </div>
                            <?php
                                $count++;
                            endforeach;
                            ?>
                        </div>
                    </div>

                    <!-- Selection Indicator -->
                    <?php if ($id === $current_university): ?>
                        <div class="mt-4 text-center">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-check <?php echo $current_language === 'ar' ? 'ml-1' : 'mr-1'; ?>"></i>
                                <?php echo $current_language === 'ar' ? 'مختارة' : 'Selected'; ?>
                            </span>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Add New University Section -->
        <div class="mt-12 text-center">
            <div class="bg-white rounded-2xl shadow-lg p-8">
                <div class="text-6xl mb-4">➕</div>
                <h3 class="text-2xl font-bold text-gray-800 mb-4">
                    <?php echo $current_language === 'ar' ? 'جامعتك غير موجودة؟' : 'University Not Listed?'; ?>
                </h3>
                <p class="text-gray-600 mb-6">
                    <?php echo $current_language === 'ar' ? 
                        'يمكنك طلب إضافة جامعتك إلى قائمة الجامعات المدعومة' : 
                        'You can request to add your university to the supported universities list'; ?>
                </p>
                <button class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-plus <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                    <?php echo $current_language === 'ar' ? 'طلب إضافة جامعة' : 'Request University Addition'; ?>
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-8 text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-lg font-semibold"><?php echo $current_language === 'ar' ? 'جاري التحديث...' : 'Updating...'; ?></p>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function selectUniversity(universityId) {
            // Show loading
            document.getElementById('loadingOverlay').classList.remove('hidden');
            
            // Send request
            fetch('universities.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'select_university',
                    university_id: universityId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload page to show updated selection
                    location.reload();
                } else {
                    alert('حدث خطأ في تحديد الجامعة');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            })
            .finally(() => {
                document.getElementById('loadingOverlay').classList.add('hidden');
            });
        }

        // Language toggle
        document.getElementById('languageToggle').addEventListener('click', function() {
            const newLanguage = '<?php echo $current_language; ?>' === 'ar' ? 'en' : 'ar';
            
            fetch('index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'change_language',
                    language: newLanguage
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
    </script>
</body>
</html>
