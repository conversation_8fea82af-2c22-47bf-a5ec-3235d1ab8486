<?php
/**
 * Edit Student Page
 * صفحة تعديل الطالب
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

require_once 'db_config.php';

$student_id = $_GET['id'] ?? null;
$message = '';
$error = '';

if (!$student_id) {
    header('Location: admin_students.php');
    exit;
}

// Handle form submission
if ($_POST) {
    try {
        $name = $_POST['name'] ?? '';
        $phone = $_POST['phone'] ?? '';
        $email = $_POST['email'] ?? '';
        $university = $_POST['university'] ?? '';
        $cumulative_gpa = $_POST['cumulative_gpa'] ?? null;
        $semester_gpa = $_POST['semester_gpa'] ?? null;
        $total_hours = $_POST['total_hours'] ?? 0;
        $classification = $_POST['classification'] ?? '';
        $link_expires_days = $_POST['link_expires_days'] ?? null;
        
        // Validation
        if (empty($name) || empty($phone)) {
            throw new Exception('الاسم ورقم الهاتف مطلوبان');
        }
        
        // Calculate new expiry date if provided
        $link_expires_at = null;
        if ($link_expires_days && is_numeric($link_expires_days)) {
            $link_expires_at = date('Y-m-d H:i:s', strtotime("+$link_expires_days days"));
        }
        
        // Update student
        $updateData = [
            'name' => $name,
            'phone' => $phone,
            'email' => $email,
            'university' => $university,
            'cumulative_gpa' => $cumulative_gpa ?: null,
            'semester_gpa' => $semester_gpa ?: null,
            'total_hours' => (int)$total_hours,
            'classification' => $classification
        ];
        
        if ($link_expires_at) {
            $updateData['link_expires_at'] = $link_expires_at;
        }
        
        $setParts = [];
        foreach ($updateData as $key => $value) {
            $setParts[] = "$key = :$key";
        }
        
        $sql = "UPDATE students SET " . implode(', ', $setParts) . " WHERE id = :id";
        $updateData['id'] = $student_id;
        
        executeQuery($sql, $updateData);
        
        $message = 'تم تحديث بيانات الطالب بنجاح';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get student data
try {
    $student = fetchOne("SELECT * FROM students WHERE id = ?", [$student_id]);
    if (!$student) {
        header('Location: admin_students.php');
        exit;
    }
} catch (Exception $e) {
    $error = 'خطأ في تحميل بيانات الطالب: ' . $e->getMessage();
}

// Get universities
$universities = [];
try {
    $universities = fetchAll("SELECT id, name_ar FROM universities WHERE is_active = 1 ORDER BY name_ar");
} catch (Exception $e) {
    // Use default if database fails
    $universities = [
        ['id' => 'aou', 'name_ar' => 'الجامعة العربية المفتوحة'],
        ['id' => 'ksu', 'name_ar' => 'جامعة الملك سعود'],
        ['id' => 'kau', 'name_ar' => 'جامعة الملك عبدالعزيز']
    ];
}

// Calculate days until expiry
$days_until_expiry = null;
if ($student['link_expires_at']) {
    $expiry_time = strtotime($student['link_expires_at']);
    $current_time = time();
    $days_until_expiry = ceil(($expiry_time - $current_time) / (24 * 60 * 60));
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الطالب - <?php echo htmlspecialchars($student['name']); ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-user-edit ml-2"></i>
                            تعديل بيانات الطالب
                        </h1>
                        <p class="text-gray-600 mt-1">تعديل معلومات الطالب وإعدادات رابط المشاركة</p>
                    </div>
                    <a href="admin_students.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة
                    </a>
                </div>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-check-circle ml-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
            <?php endif; ?>

            <?php if ($error): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>

            <!-- Edit Form -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <form method="POST" class="space-y-6">
                    <!-- Basic Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-user ml-2"></i>
                            المعلومات الأساسية
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الاسم *</label>
                                <input type="text" name="name" required
                                       value="<?php echo htmlspecialchars($student['name']); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                                <input type="text" name="phone" required
                                       value="<?php echo htmlspecialchars($student['phone']); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                <input type="email" name="email"
                                       value="<?php echo htmlspecialchars($student['email'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الجامعة</label>
                                <select name="university" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="">اختر الجامعة</option>
                                    <?php foreach ($universities as $uni): ?>
                                        <option value="<?php echo htmlspecialchars($uni['name_ar']); ?>" 
                                                <?php echo $student['university'] === $uni['name_ar'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($uni['name_ar']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Academic Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-graduation-cap ml-2"></i>
                            المعلومات الأكاديمية
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">المعدل التراكمي</label>
                                <input type="number" name="cumulative_gpa" step="0.01" min="0" max="4"
                                       value="<?php echo $student['cumulative_gpa']; ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">المعدل الفصلي</label>
                                <input type="number" name="semester_gpa" step="0.01" min="0" max="4"
                                       value="<?php echo $student['semester_gpa']; ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">إجمالي الساعات</label>
                                <input type="number" name="total_hours" min="0"
                                       value="<?php echo $student['total_hours']; ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">التقدير</label>
                                <select name="classification" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="">اختر التقدير</option>
                                    <option value="ممتاز" <?php echo $student['classification'] === 'ممتاز' ? 'selected' : ''; ?>>ممتاز</option>
                                    <option value="جيد جداً" <?php echo $student['classification'] === 'جيد جداً' ? 'selected' : ''; ?>>جيد جداً</option>
                                    <option value="جيد" <?php echo $student['classification'] === 'جيد' ? 'selected' : ''; ?>>جيد</option>
                                    <option value="مقبول" <?php echo $student['classification'] === 'مقبول' ? 'selected' : ''; ?>>مقبول</option>
                                    <option value="راسب" <?php echo $student['classification'] === 'راسب' ? 'selected' : ''; ?>>راسب</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Link Settings -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-link ml-2"></i>
                            إعدادات رابط المشاركة
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">رابط المشاركة</label>
                                <div class="flex">
                                    <input type="text" readonly
                                           value="<?php echo $student['share_link'] ? "http://localhost/gpa/php-version/index.php?shared={$student['share_link']}" : 'لا يوجد رابط'; ?>"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-r-lg bg-gray-50">
                                    <?php if ($student['share_link']): ?>
                                    <button type="button" onclick="copyLink()" 
                                            class="px-3 py-2 bg-blue-600 text-white rounded-l-lg hover:bg-blue-700">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    تمديد صلاحية الرابط (بالأيام)
                                    <?php if ($days_until_expiry !== null): ?>
                                        <span class="text-sm <?php echo $days_until_expiry > 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                            (<?php echo $days_until_expiry > 0 ? "باقي $days_until_expiry أيام" : 'منتهي الصلاحية'; ?>)
                                        </span>
                                    <?php endif; ?>
                                </label>
                                <input type="number" name="link_expires_days" min="1" max="365"
                                       placeholder="اتركه فارغاً للرابط الدائم"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <p class="text-xs text-gray-500 mt-1">
                                    اتركه فارغاً لجعل الرابط دائماً، أو أدخل عدد الأيام لتمديد الصلاحية
                                </p>
                            </div>
                        </div>
                        
                        <!-- Link Statistics -->
                        <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                            <h4 class="font-medium text-gray-800 mb-2">إحصائيات الرابط</h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600">عدد الزيارات:</span>
                                    <span class="font-semibold"><?php echo $student['link_views'] ?? 0; ?></span>
                                </div>
                                <div>
                                    <span class="text-gray-600">تاريخ الإنشاء:</span>
                                    <span class="font-semibold"><?php echo date('Y-m-d', strtotime($student['created_at'])); ?></span>
                                </div>
                                <div>
                                    <span class="text-gray-600">تاريخ الانتهاء:</span>
                                    <span class="font-semibold">
                                        <?php echo $student['link_expires_at'] ? date('Y-m-d', strtotime($student['link_expires_at'])) : 'دائم'; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-4 space-x-reverse">
                        <a href="admin_students.php" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700">
                            إلغاء
                        </a>
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                            <i class="fas fa-save ml-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function copyLink() {
            const linkInput = document.querySelector('input[readonly]');
            linkInput.select();
            document.execCommand('copy');
            
            // Show success message
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.classList.add('bg-green-600');
            button.classList.remove('bg-blue-600');
            
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('bg-green-600');
                button.classList.add('bg-blue-600');
            }, 2000);
        }
    </script>
</body>
</html>
