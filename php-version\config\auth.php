<?php
/**
 * Authentication System
 * نظام المصادقة والتحقق
 */

require_once __DIR__ . '/database.php';

class Auth {
    private static $instance = null;
    private $currentUser = null;
    
    private function __construct() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $this->loadCurrentUser();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تسجيل دخول المدير
     */
    public function login($username, $password, $rememberMe = false) {
        try {
            // التحقق من محاولات تسجيل الدخول
            $admin = dbFetch(
                "SELECT * FROM admins WHERE (username = ? OR email = ?) AND is_active = 1",
                [$username, $username]
            );
            
            if (!$admin) {
                $this->logFailedAttempt($username);
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // التحقق من الحظر
            if ($admin['locked_until'] && strtotime($admin['locked_until']) > time()) {
                $remainingTime = ceil((strtotime($admin['locked_until']) - time()) / 60);
                return ['success' => false, 'message' => "الحساب محظور لمدة {$remainingTime} دقيقة"];
            }
            
            // التحقق من كلمة المرور
            if (!password_verify($password, $admin['password'])) {
                $this->incrementLoginAttempts($admin['id']);
                return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
            }
            
            // تسجيل دخول ناجح
            $this->resetLoginAttempts($admin['id']);
            $this->setCurrentUser($admin);
            
            // تحديث آخر تسجيل دخول
            dbUpdate('admins', 
                ['last_login' => date('Y-m-d H:i:s')], 
                'id = ?', 
                [$admin['id']]
            );
            
            // تسجيل النشاط
            logActivity($admin['id'], 'login', 'تسجيل دخول ناجح');
            
            // Remember Me functionality
            if ($rememberMe) {
                $this->setRememberToken($admin['id']);
            }
            
            return ['success' => true, 'message' => 'تم تسجيل الدخول بنجاح', 'user' => $admin];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ في تسجيل الدخول'];
        }
    }
    
    /**
     * تسجيل خروج المدير
     */
    public function logout() {
        if ($this->currentUser) {
            logActivity($this->currentUser['id'], 'logout', 'تسجيل خروج');
        }
        
        // Clear session
        $_SESSION = [];
        session_destroy();
        
        // Clear remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
        
        $this->currentUser = null;
        
        return ['success' => true, 'message' => 'تم تسجيل الخروج بنجاح'];
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn() {
        return $this->currentUser !== null;
    }
    
    /**
     * الحصول على المستخدم الحالي
     */
    public function getCurrentUser() {
        return $this->currentUser;
    }
    
    /**
     * التحقق من الصلاحيات
     */
    public function hasRole($role) {
        if (!$this->currentUser) {
            return false;
        }
        
        $userRole = $this->currentUser['role'];
        
        // Super admin has all permissions
        if ($userRole === 'super_admin') {
            return true;
        }
        
        // Check specific roles
        switch ($role) {
            case 'super_admin':
                return $userRole === 'super_admin';
            case 'admin':
                return in_array($userRole, ['super_admin', 'admin']);
            case 'moderator':
                return in_array($userRole, ['super_admin', 'admin', 'moderator']);
            default:
                return false;
        }
    }
    
    /**
     * التحقق من صلاحية محددة
     */
    public function can($permission) {
        if (!$this->currentUser) {
            return false;
        }
        
        $permissions = $this->getRolePermissions($this->currentUser['role']);
        return in_array($permission, $permissions);
    }
    
    /**
     * تحميل المستخدم الحالي
     */
    private function loadCurrentUser() {
        // Check session first
        if (isset($_SESSION['admin_id'])) {
            $admin = dbFetch("SELECT * FROM admins WHERE id = ? AND is_active = 1", [$_SESSION['admin_id']]);
            if ($admin) {
                $this->currentUser = $admin;
                return;
            }
        }
        
        // Check remember me token
        if (isset($_COOKIE['remember_token'])) {
            $this->loginWithRememberToken($_COOKIE['remember_token']);
        }
    }
    
    /**
     * تعيين المستخدم الحالي
     */
    private function setCurrentUser($admin) {
        $this->currentUser = $admin;
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['admin_role'] = $admin['role'];
        $_SESSION['last_activity'] = time();
    }
    
    /**
     * زيادة محاولات تسجيل الدخول الفاشلة
     */
    private function incrementLoginAttempts($adminId) {
        $maxAttempts = (int) getSetting('max_login_attempts', 5);
        $lockoutDuration = (int) getSetting('lockout_duration', 30); // minutes
        
        $admin = dbFetch("SELECT login_attempts FROM admins WHERE id = ?", [$adminId]);
        $attempts = $admin['login_attempts'] + 1;
        
        $updateData = ['login_attempts' => $attempts];
        
        if ($attempts >= $maxAttempts) {
            $updateData['locked_until'] = date('Y-m-d H:i:s', time() + ($lockoutDuration * 60));
            logActivity($adminId, 'account_locked', "تم حظر الحساب بعد {$attempts} محاولات فاشلة");
        }
        
        dbUpdate('admins', $updateData, 'id = ?', [$adminId]);
    }
    
    /**
     * إعادة تعيين محاولات تسجيل الدخول
     */
    private function resetLoginAttempts($adminId) {
        dbUpdate('admins', 
            ['login_attempts' => 0, 'locked_until' => null], 
            'id = ?', 
            [$adminId]
        );
    }
    
    /**
     * تسجيل محاولة فاشلة
     */
    private function logFailedAttempt($username) {
        logActivity(null, 'failed_login', "محاولة تسجيل دخول فاشلة لاسم المستخدم: {$username}");
    }
    
    /**
     * تعيين رمز التذكر
     */
    private function setRememberToken($adminId) {
        $token = bin2hex(random_bytes(32));
        $hashedToken = password_hash($token, PASSWORD_DEFAULT);
        
        // Store hashed token in database (you might want to create a remember_tokens table)
        dbUpdate('admins', ['remember_token' => $hashedToken], 'id = ?', [$adminId]);
        
        // Set cookie for 30 days
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
    }
    
    /**
     * تسجيل دخول برمز التذكر
     */
    private function loginWithRememberToken($token) {
        $admins = dbFetchAll("SELECT * FROM admins WHERE remember_token IS NOT NULL AND is_active = 1");
        
        foreach ($admins as $admin) {
            if (password_verify($token, $admin['remember_token'])) {
                $this->setCurrentUser($admin);
                logActivity($admin['id'], 'auto_login', 'تسجيل دخول تلقائي برمز التذكر');
                return true;
            }
        }
        
        // Invalid token, clear cookie
        setcookie('remember_token', '', time() - 3600, '/');
        return false;
    }
    
    /**
     * الحصول على صلاحيات الدور
     */
    private function getRolePermissions($role) {
        $permissions = [
            'super_admin' => [
                'view_dashboard', 'manage_students', 'manage_admins', 'manage_settings',
                'view_reports', 'export_data', 'manage_universities', 'system_maintenance',
                'view_logs', 'delete_data', 'backup_restore'
            ],
            'admin' => [
                'view_dashboard', 'manage_students', 'view_reports', 'export_data',
                'manage_universities', 'view_logs'
            ],
            'moderator' => [
                'view_dashboard', 'manage_students', 'view_reports'
            ]
        ];
        
        return $permissions[$role] ?? [];
    }
    
    /**
     * التحقق من انتهاء صلاحية الجلسة
     */
    public function checkSessionTimeout() {
        $timeout = (int) getSetting('session_timeout', 120) * 60; // Convert to seconds
        
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > $timeout) {
            $this->logout();
            return false;
        }
        
        $_SESSION['last_activity'] = time();
        return true;
    }
    
    /**
     * تغيير كلمة المرور
     */
    public function changePassword($currentPassword, $newPassword) {
        if (!$this->currentUser) {
            return ['success' => false, 'message' => 'غير مسجل دخول'];
        }
        
        // Verify current password
        if (!password_verify($currentPassword, $this->currentUser['password'])) {
            return ['success' => false, 'message' => 'كلمة المرور الحالية غير صحيحة'];
        }
        
        // Validate new password
        if (strlen($newPassword) < 8) {
            return ['success' => false, 'message' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'];
        }
        
        // Update password
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        dbUpdate('admins', ['password' => $hashedPassword], 'id = ?', [$this->currentUser['id']]);
        
        logActivity($this->currentUser['id'], 'password_changed', 'تم تغيير كلمة المرور');
        
        return ['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح'];
    }
}

/**
 * Helper functions
 */
function auth() {
    return Auth::getInstance();
}

function isLoggedIn() {
    return auth()->isLoggedIn();
}

function getCurrentUser() {
    return auth()->getCurrentUser();
}

function hasRole($role) {
    return auth()->hasRole($role);
}

function can($permission) {
    return auth()->can($permission);
}

function requireAuth() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

function requireRole($role) {
    requireAuth();
    if (!hasRole($role)) {
        http_response_code(403);
        die('غير مصرح لك بالوصول لهذه الصفحة');
    }
}

function requirePermission($permission) {
    requireAuth();
    if (!can($permission)) {
        http_response_code(403);
        die('غير مصرح لك بتنفيذ هذا الإجراء');
    }
}

// Initialize auth system
Auth::getInstance();
?>
