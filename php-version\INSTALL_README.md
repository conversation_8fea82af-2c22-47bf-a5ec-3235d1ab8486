# دليل تثبيت حاسبة المعدل التراكمي

## 🚀 التثبيت السريع

### المتطلبات الأساسية
- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث (أو MariaDB 10.2+)
- **Apache/Nginx**: خادم ويب
- **Extensions**: PDO, PDO_MySQL, mbstring, json

### خطوات التثبيت

#### 1. تحميل الملفات
```bash
# تحميل المشروع
git clone [repository-url]
cd gpa-calculator/php-version

# أو رفع الملفات يدوياً إلى خادم الويب
```

#### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE gpa_calculator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إن<PERSON><PERSON><PERSON> مستخدم (اختياري)
CREATE USER 'gpa_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON gpa_calculator.* TO 'gpa_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 3. تشغيل معالج التثبيت
1. افتح المتصفح واذهب إلى: `http://your-domain.com/install.php`
2. اتبع الخطوات الأربع في المعالج:
   - **الخطوة 1**: إعداد قاعدة البيانات
   - **الخطوة 2**: إنشاء حساب المدير
   - **الخطوة 3**: تثبيت النظام
   - **الخطوة 4**: اكتمال التثبيت

#### 4. إعدادات الأمان
```bash
# حذف ملف التثبيت (مهم!)
rm install.php

# تعيين صلاحيات الملفات
chmod 644 *.php
chmod 600 config.php
chmod 755 data/
chmod 755 uploads/
```

## 🔧 التكوين المتقدم

### إعداد Apache
```apache
# .htaccess
RewriteEngine On

# Force HTTPS (للإنتاج)
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Pretty URLs
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/?$ index.php?page=$1 [L,QSA]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

### إعداد Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/gpa-calculator/php-version;
    index index.php;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Deny access to sensitive files
    location ~ /\.(ht|git) {
        deny all;
    }
}
```

## 📊 البيانات الافتراضية

### الجامعات المدرجة
- الجامعة العربية المفتوحة (AOU)
- جامعة الملك سعود (KSU)
- جامعة الملك عبدالعزيز (KAU)
- جامعة الملك فهد للبترول والمعادن (KFUPM)
- جامعة الملك فيصل (KFU)
- الجامعة الإسلامية (IU)

### أنظمة التقدير
- **النظام السعودي**: A+ (4.0) إلى F (0.0)
- **النظام الأمريكي**: A (4.0) إلى F (0.0)
- **النظام البريطاني**: قابل للتخصيص

### تصنيفات المعدل
- **ممتاز**: 4.00 - 3.67
- **جيد جداً**: 3.66 - 3.00
- **جيد**: 2.99 - 2.33
- **مقبول**: 2.32 - 2.00
- **راسب**: أقل من 2.00

## 🔐 الأمان

### كلمات المرور الافتراضية
- **المدير**: admin / admin123 (يجب تغييرها فوراً!)

### إعدادات الأمان الموصى بها
1. **تغيير كلمة مرور المدير**
2. **تفعيل HTTPS**
3. **إعداد جدار حماية**
4. **تحديث PHP و MySQL بانتظام**
5. **عمل نسخ احتياطية دورية**

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

#### خطأ اتصال قاعدة البيانات
```
SQLSTATE[HY000] [1045] Access denied
```
**الحل**: تحقق من بيانات الاتصال في config.php

#### خطأ صلاحيات الملفات
```
Permission denied
```
**الحل**: 
```bash
chmod 755 data/
chmod 644 *.php
```

#### خطأ PHP Extensions
```
Class 'PDO' not found
```
**الحل**: تفعيل PDO و PDO_MySQL في php.ini

### سجلات الأخطاء
```bash
# عرض سجلات Apache
tail -f /var/log/apache2/error.log

# عرض سجلات PHP
tail -f /var/log/php_errors.log
```

## 📈 الأداء

### تحسين قاعدة البيانات
```sql
-- إضافة فهارس للأداء
CREATE INDEX idx_students_university ON students(university);
CREATE INDEX idx_students_created_at ON students(created_at);
CREATE INDEX idx_gpa_classifications_university ON university_gpa_classifications(university_id);
```

### تحسين PHP
```ini
; php.ini
memory_limit = 256M
max_execution_time = 60
upload_max_filesize = 10M
post_max_size = 10M
```

## 🔄 التحديث

### نسخ احتياطي قبل التحديث
```bash
# نسخ قاعدة البيانات
mysqldump -u username -p gpa_calculator > backup_$(date +%Y%m%d).sql

# نسخ الملفات
tar -czf files_backup_$(date +%Y%m%d).tar.gz /path/to/gpa-calculator/
```

### خطوات التحديث
1. عمل نسخة احتياطية
2. تحميل الإصدار الجديد
3. تشغيل سكريبت التحديث (إن وجد)
4. اختبار النظام

## 📞 الدعم

### المطور
- **الاسم**: محمد الحراني
- **LinkedIn**: https://www.linkedin.com/in/mohamed-elharany/
- **الرسالة**: صُنع بكل حب من الطلاب للطلاب

### المتطلبات التقنية
- **PHP**: 7.4+ (موصى به: 8.0+)
- **MySQL**: 5.7+ (موصى به: 8.0+)
- **Memory**: 128MB+ (موصى به: 256MB+)
- **Storage**: 50MB+ للملفات الأساسية

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.

---

**🎓 صُنع بكل حب من الطلاب للطلاب**
