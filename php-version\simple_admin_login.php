<?php
/**
 * Simple Admin Login - Direct Database Access
 * تسجيل دخول مبسط للإدارة
 */

session_start();

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$error = '';
$success = '';

// Redirect if already logged in
if (isset($_SESSION['admin_id'])) {
    if ($_SESSION['admin_role'] === 'publisher') {
        header('Location: publisher_dashboard.php');
    } else {
        header('Location: admin_dashboard.php');
    }
    exit;
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $loginUsername = trim($_POST['username'] ?? '');
    $loginPassword = $_POST['password'] ?? '';
    
    if (empty($loginUsername) || empty($loginPassword)) {
        $error = 'يرجى ملء جميع الحقول';
    } else {
        try {
            // Connect to database
            $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Get admin user
            $stmt = $pdo->prepare("SELECT * FROM admins WHERE (username = ? OR email = ?) AND is_active = 1");
            $stmt->execute([$loginUsername, $loginUsername]);
            $admin = $stmt->fetch();
            
            if ($admin && password_verify($loginPassword, $admin['password'])) {
                // Login successful
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_role'] = $admin['role'];
                $_SESSION['admin_name'] = $admin['full_name'];
                $_SESSION['last_activity'] = time();
                
                // Update last login
                $updateStmt = $pdo->prepare("UPDATE admins SET last_login = NOW() WHERE id = ?");
                $updateStmt->execute([$admin['id']]);
                
                // Log activity
                try {
                    $logStmt = $pdo->prepare("INSERT INTO activity_logs (admin_id, action, description, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())");
                    $logStmt->execute([
                        $admin['id'], 
                        'login', 
                        'تسجيل دخول ناجح', 
                        $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
                    ]);
                } catch (Exception $e) {
                    // Log error but don't fail login
                }
                
                // Redirect based on role
                if ($admin['role'] === 'publisher') {
                    header('Location: publisher_dashboard.php');
                } else {
                    header('Location: admin_dashboard.php');
                }
                exit;
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                
                // Log failed attempt
                try {
                    $logStmt = $pdo->prepare("INSERT INTO activity_logs (action, description, ip_address, created_at) VALUES (?, ?, ?, NOW())");
                    $logStmt->execute([
                        'failed_login', 
                        "محاولة تسجيل دخول فاشلة لاسم المستخدم: $loginUsername", 
                        $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
                    ]);
                } catch (Exception $e) {
                    // Ignore logging errors
                }
            }
            
        } catch (PDOException $e) {
            $error = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
        } catch (Exception $e) {
            $error = 'حدث خطأ غير متوقع: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول الإدارة - حاسبة المعدل التراكمي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <div class="login-card w-full max-w-md p-8 rounded-2xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-user-shield text-white text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">لوحة إدارة النظام</h1>
            <p class="text-gray-600">حاسبة المعدل التراكمي - الجامعة العربية المفتوحة</p>
        </div>

        <!-- Alerts -->
        <?php if ($error): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    <span><?php echo htmlspecialchars($error); ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <span><?php echo htmlspecialchars($success); ?></span>
                </div>
            </div>
        <?php endif; ?>

        <!-- Login Form -->
        <form method="POST" class="space-y-6">
            <!-- Username Field -->
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                    اسم المستخدم أو البريد الإلكتروني
                </label>
                <div class="relative">
                    <input type="text"
                           id="username"
                           name="username"
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                           class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="أدخل اسم المستخدم"
                           required>
                    <i class="fas fa-user absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>

            <!-- Password Field -->
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور
                </label>
                <div class="relative">
                    <input type="password"
                           id="password"
                           name="password"
                           value=""
                           class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="أدخل كلمة المرور"
                           required>
                    <i class="fas fa-lock absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>

            <!-- Login Button -->
            <button type="submit" 
                    class="btn-login w-full py-3 px-4 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl">
                <i class="fas fa-sign-in-alt ml-2"></i>
                تسجيل الدخول
            </button>
        </form>



        <!-- Footer -->
        <div class="mt-8 pt-6 border-t border-gray-200 text-center">
            <p class="text-sm text-gray-500">
                © 2024 الجامعة العربية المفتوحة. جميع الحقوق محفوظة.
            </p>
            <div class="mt-2">
                <a href="index.php" class="text-sm text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-right ml-1"></i>
                    العودة للصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <script>
        // Auto-focus on username field
        document.getElementById('username').focus();
        
        // Form validation and loading state
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول');
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري تسجيل الدخول...';
            submitBtn.disabled = true;
            
            // Re-enable button after 5 seconds in case of error
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 5000);
        });
        

    </script>
</body>
</html>
