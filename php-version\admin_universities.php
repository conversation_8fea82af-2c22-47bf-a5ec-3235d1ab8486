<?php
/**
 * Admin Universities Management
 * إدارة الجامعات
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

// Redirect publishers to their own dashboard
if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'publisher') {
    header('Location: publisher_dashboard.php');
    exit;
}

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$currentUser = [
    'id' => $_SESSION['admin_id'],
    'username' => $_SESSION['admin_username'] ?? 'admin',
    'role' => $_SESSION['admin_role'] ?? 'admin',
    'full_name' => $_SESSION['admin_name'] ?? 'مدير النظام'
];

$success = '';
$error = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_university':
                    $code = trim($_POST['code'] ?? '');
                    $nameAr = trim($_POST['name_ar'] ?? '');
                    $nameEn = trim($_POST['name_en'] ?? '');

                    if (empty($code) || empty($nameAr) || empty($nameEn)) {
                        $error = 'يرجى ملء جميع الحقول المطلوبة';
                    } else {
                        // Check if code exists
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM universities WHERE code = ?");
                        $stmt->execute([$code]);

                        if ($stmt->fetchColumn() > 0) {
                            $error = 'رمز الجامعة موجود مسبقاً';
                        } else {
                            $stmt = $pdo->prepare("
                                INSERT INTO universities (code, name_ar, name_en, is_active)
                                VALUES (?, ?, ?, 1)
                            ");
                            $stmt->execute([$code, $nameAr, $nameEn]);

                            $success = 'تم إضافة الجامعة بنجاح';
                        }
                    }
                    break;
                    
                case 'update_university':
                    $id = intval($_POST['university_id'] ?? 0);
                    $nameAr = trim($_POST['name_ar'] ?? '');
                    $nameEn = trim($_POST['name_en'] ?? '');
                    $isActive = isset($_POST['is_active']) ? 1 : 0;

                    if ($id <= 0 || empty($nameAr) || empty($nameEn)) {
                        $error = 'يرجى ملء جميع الحقول المطلوبة';
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE universities
                            SET name_ar = ?, name_en = ?, is_active = ?
                            WHERE id = ?
                        ");
                        $stmt->execute([$nameAr, $nameEn, $isActive, $id]);

                        $success = 'تم تحديث الجامعة بنجاح';
                    }
                    break;
                    
                case 'delete_university':
                    $id = intval($_POST['university_id'] ?? 0);
                    
                    if ($id <= 0) {
                        $error = 'معرف الجامعة غير صحيح';
                    } else {
                        // Check if university has students
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM students s JOIN universities u ON s.university = u.code WHERE u.id = ?");
                        $stmt->execute([$id]);
                        $studentCount = $stmt->fetchColumn();
                        
                        if ($studentCount > 0) {
                            $error = "لا يمكن حذف الجامعة لأنها تحتوي على $studentCount طالب";
                        } else {
                            $stmt = $pdo->prepare("DELETE FROM universities WHERE id = ?");
                            $stmt->execute([$id]);
                            
                            $success = 'تم حذف الجامعة بنجاح';
                        }
                    }
                    break;
            }
        }
    }
    
    // Get all universities
    $universities = $pdo->query("
        SELECT u.*,
               COALESCE((SELECT COUNT(*) FROM students s WHERE s.university = u.code), 0) as student_count
        FROM universities u
        ORDER BY u.name_ar
    ")->fetchAll();
    
} catch (PDOException $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

// Default grading systems for JavaScript (empty since we use database now)
$defaultGradingSystems = [];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الجامعات - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .modal {
            backdrop-filter: blur(4px);
        }

        .draggable {
            position: relative;
        }

        .dragging {
            user-select: none;
            cursor: move !important;
        }

        .modal-maximized {
            max-width: 95vw !important;
            max-height: 95vh !important;
            width: 95vw !important;
            height: 95vh !important;
        }

        .modal-maximized .overflow-y-auto {
            max-height: calc(95vh - 120px) !important;
        }

        #modalContent {
            transition: all 0.3s ease;
        }

        .grade-input-row {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إدارة الجامعات</h1>
                        <p class="text-gray-600">إدارة الجامعات وأنظمة التقدير</p>
                    </div>
                    
                    <button onclick="showAddModal()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة جامعة جديدة
                    </button>
                </div>
            </header>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="mx-6 mt-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle ml-2"></i>
                        <span><?php echo htmlspecialchars($success); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mx-6 mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        <span><?php echo htmlspecialchars($error); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Universities Table -->
            <div class="p-6">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-4 border-b bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-800">قائمة الجامعات</h3>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرمز</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم بالعربية</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم بالإنجليزية</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد الطلاب</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($universities)): ?>
                                    <?php foreach ($universities as $university): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($university['code']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo htmlspecialchars($university['name_ar']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo htmlspecialchars($university['name_en']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($university['student_count']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 py-1 text-xs rounded-full <?php echo $university['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                                    <?php echo $university['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button onclick="editUniversity(<?php echo htmlspecialchars(json_encode($university)); ?>)" 
                                                        class="text-blue-600 hover:text-blue-900 ml-3">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                
                                                <?php if ($university['student_count'] == 0): ?>
                                                    <button onclick="deleteUniversity(<?php echo $university['id']; ?>, '<?php echo htmlspecialchars($university['name_ar']); ?>')" 
                                                            class="text-red-600 hover:text-red-900">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="text-gray-400" title="لا يمكن حذف الجامعة لوجود طلاب">
                                                        <i class="fas fa-trash"></i>
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-8 text-gray-500">لا توجد جامعات</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit University Modal -->
    <div id="universityModal" class="modal fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden relative">
                <!-- Draggable Header -->
                <div id="modalHeader" class="p-6 border-b bg-gray-50 cursor-move select-none">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-grip-vertical text-gray-400 ml-2"></i>
                            <h3 id="modalTitle" class="text-lg font-semibold text-gray-800">إضافة جامعة جديدة</h3>
                        </div>
                        <div class="flex items-center gap-2">
                            <button onclick="toggleModalSize()" class="text-gray-400 hover:text-gray-600" title="تكبير/تصغير">
                                <i id="resizeIcon" class="fas fa-expand text-lg"></i>
                            </button>
                            <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600" title="إغلاق">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Modal Body with Scroll -->
                <div class="overflow-y-auto max-h-[calc(90vh-120px)]">
                
                <form id="universityForm" method="POST" class="p-6">
                    <input type="hidden" name="action" id="formAction" value="add_university">
                    <input type="hidden" name="university_id" id="universityId">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رمز الجامعة</label>
                            <input type="text" name="code" id="universityCode" required maxlength="10"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الاسم بالعربية</label>
                            <input type="text" name="name_ar" id="universityNameAr" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">الاسم بالإنجليزية</label>
                            <input type="text" name="name_en" id="universityNameEn" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div class="md:col-span-2">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-info-circle text-blue-600 ml-2"></i>
                                    <div>
                                        <h4 class="font-semibold text-blue-800">إدارة سلم الدرجات</h4>
                                        <p class="text-blue-700 text-sm mt-1">
                                            يتم إدارة سلم الدرجات لكل جامعة من خلال صفحة منفصلة.
                                            بعد إضافة الجامعة، يمكنك إضافة وتعديل سلم الدرجات الخاص بها.
                                        </p>
                                        <a href="admin_grading_scales.php" class="inline-flex items-center mt-2 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-graduation-cap ml-1"></i>
                                            إدارة سلم الدرجات
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="is_active" id="isActive" checked
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200">
                                <span class="mr-2 text-sm text-gray-700">جامعة نشطة</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3 space-x-reverse">
                        <button type="button" onclick="closeModal()"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-save ml-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
                </div> <!-- End scrollable content -->
            </div>
        </div>
    </div>

    <script>
        const defaultGradingSystems = <?php echo json_encode($defaultGradingSystems); ?>;

        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة جامعة جديدة';
            document.getElementById('formAction').value = 'add_university';
            document.getElementById('universityForm').reset();
            document.getElementById('universityCode').disabled = false;
            document.getElementById('universityModal').classList.remove('hidden');
            resetModalPosition();
        }

        function editUniversity(university) {
            document.getElementById('modalTitle').textContent = 'تعديل الجامعة';
            document.getElementById('formAction').value = 'update_university';
            document.getElementById('universityId').value = university.id;
            document.getElementById('universityCode').value = university.code;
            document.getElementById('universityCode').disabled = true;
            document.getElementById('universityNameAr').value = university.name_ar;
            document.getElementById('universityNameEn').value = university.name_en;
            document.getElementById('isActive').checked = university.is_active == 1;

            document.getElementById('universityModal').classList.remove('hidden');
            resetModalPosition();
        }

        function closeModal() {
            document.getElementById('universityModal').classList.add('hidden');
            resetModalPosition();
        }

        function updateGradingSystem() {
            const select = document.getElementById('gradingSystemSelect');
            const textarea = document.getElementById('gradingSystem');
            const customBuilder = document.getElementById('customGradingBuilder');

            if (select.value === 'custom') {
                customBuilder.classList.remove('hidden');
                textarea.value = '{}';
                initializeCustomGrading();
            } else {
                customBuilder.classList.add('hidden');
                if (select.value && defaultGradingSystems[select.value]) {
                    textarea.value = defaultGradingSystems[select.value];
                }
            }
        }

        function initializeCustomGrading() {
            const container = document.getElementById('gradeInputs');
            container.innerHTML = '';

            // Add default grades
            const defaultGrades = [
                {grade: 'A', points: '4.0'},
                {grade: 'B', points: '3.0'},
                {grade: 'C', points: '2.0'},
                {grade: 'D', points: '1.0'},
                {grade: 'F', points: '0.0'}
            ];

            defaultGrades.forEach(item => {
                addGradeInput(item.grade, item.points);
            });

            updateCustomGradingJSON();
        }

        function addGradeInput(grade = '', points = '') {
            const container = document.getElementById('gradeInputs');
            const index = container.children.length;

            const div = document.createElement('div');
            div.className = 'flex gap-2 items-center grade-input-row';
            div.innerHTML = `
                <div class="flex-1">
                    <input type="text" placeholder="التقدير (مثل: A+)" value="${grade}"
                           onchange="updateCustomGradingJSON()"
                           class="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="w-24">
                    <input type="number" placeholder="النقاط" value="${points}" step="0.1" min="0" max="4"
                           onchange="updateCustomGradingJSON()"
                           class="w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex gap-1">
                    <button type="button" onclick="moveGradeUp(this)" title="تحريك لأعلى"
                            class="px-2 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        <i class="fas fa-arrow-up text-sm"></i>
                    </button>
                    <button type="button" onclick="moveGradeDown(this)" title="تحريك لأسفل"
                            class="px-2 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        <i class="fas fa-arrow-down text-sm"></i>
                    </button>
                    <button type="button" onclick="removeGradeInput(this)" title="حذف"
                            class="px-2 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                        <i class="fas fa-trash text-sm"></i>
                    </button>
                </div>
            `;

            container.appendChild(div);
            updateCustomGradingJSON();

            // Focus on the grade input
            div.querySelector('input[type="text"]').focus();
        }

        function removeGradeInput(button) {
            button.parentElement.remove();
            updateCustomGradingJSON();
        }

        function moveGradeUp(button) {
            const row = button.closest('.grade-input-row');
            const prevRow = row.previousElementSibling;
            if (prevRow) {
                row.parentNode.insertBefore(row, prevRow);
                updateCustomGradingJSON();
            }
        }

        function moveGradeDown(button) {
            const row = button.closest('.grade-input-row');
            const nextRow = row.nextElementSibling;
            if (nextRow) {
                row.parentNode.insertBefore(nextRow, row);
                updateCustomGradingJSON();
            }
        }

        function sortGrades() {
            const container = document.getElementById('gradeInputs');
            const rows = Array.from(container.children);

            rows.sort((a, b) => {
                const pointsA = parseFloat(a.querySelector('input[type="number"]').value) || 0;
                const pointsB = parseFloat(b.querySelector('input[type="number"]').value) || 0;
                return pointsB - pointsA; // Sort descending
            });

            container.innerHTML = '';
            rows.forEach(row => container.appendChild(row));
            updateCustomGradingJSON();
        }

        function clearAllGrades() {
            if (confirm('هل أنت متأكد من حذف جميع التقديرات؟')) {
                document.getElementById('gradeInputs').innerHTML = '';
                updateCustomGradingJSON();
            }
        }

        function updateCustomGradingJSON() {
            const container = document.getElementById('gradeInputs');
            const textarea = document.getElementById('gradingSystem');
            const gradingSystem = {};

            Array.from(container.children).forEach(div => {
                const gradeInput = div.querySelector('input[type="text"]');
                const pointsInput = div.querySelector('input[type="number"]');

                if (gradeInput.value && pointsInput.value) {
                    gradingSystem[gradeInput.value] = parseFloat(pointsInput.value);
                }
            });

            textarea.value = JSON.stringify(gradingSystem, null, 2);
        }

        function deleteUniversity(id, name) {
            if (confirm(`هل أنت متأكد من حذف الجامعة "${name}"؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_university">
                    <input type="hidden" name="university_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Modal dragging functionality
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;
        let isMaximized = false;

        function initializeDragging() {
            const modal = document.getElementById('modalContent');
            const header = document.getElementById('modalHeader');

            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            // Touch events for mobile
            header.addEventListener('touchstart', dragStart);
            document.addEventListener('touchmove', drag);
            document.addEventListener('touchend', dragEnd);
        }

        function dragStart(e) {
            if (isMaximized) return; // Don't allow dragging when maximized

            if (e.type === "touchstart") {
                initialX = e.touches[0].clientX - xOffset;
                initialY = e.touches[0].clientY - yOffset;
            } else {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
            }

            if (e.target === document.getElementById('modalHeader') ||
                e.target.closest('#modalHeader')) {
                isDragging = true;
                document.body.classList.add('dragging');
            }
        }

        function drag(e) {
            if (isDragging) {
                e.preventDefault();

                if (e.type === "touchmove") {
                    currentX = e.touches[0].clientX - initialX;
                    currentY = e.touches[0].clientY - initialY;
                } else {
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;
                }

                xOffset = currentX;
                yOffset = currentY;

                const modal = document.getElementById('modalContent');
                modal.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        }

        function dragEnd(e) {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            document.body.classList.remove('dragging');
        }

        function toggleModalSize() {
            const modal = document.getElementById('modalContent');
            const icon = document.getElementById('resizeIcon');

            if (isMaximized) {
                // Restore to normal size
                modal.classList.remove('modal-maximized');
                modal.style.transform = `translate(${xOffset}px, ${yOffset}px)`;
                icon.className = 'fas fa-expand text-lg';
                isMaximized = false;
            } else {
                // Maximize
                modal.classList.add('modal-maximized');
                modal.style.transform = 'translate(0, 0)';
                icon.className = 'fas fa-compress text-lg';
                isMaximized = true;
            }
        }

        function resetModalPosition() {
            const modal = document.getElementById('modalContent');
            modal.style.transform = 'translate(0, 0)';
            modal.classList.remove('modal-maximized');
            document.getElementById('resizeIcon').className = 'fas fa-expand text-lg';
            xOffset = 0;
            yOffset = 0;
            isMaximized = false;
        }

        // Close modal when clicking outside
        document.getElementById('universityModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Initialize dragging when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDragging();
        });
    </script>
</body>
</html>
