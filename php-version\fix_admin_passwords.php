<?php
/**
 * Fix Admin Passwords
 * إصلاح كلمات مرور المديرين
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$success = [];
$errors = [];

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Generate correct password hash for 'admin123'
    $correctPasswordHash = password_hash('admin123', PASSWORD_DEFAULT);
    
    // Update admin passwords
    $admins = [
        ['admin', '<EMAIL>', 'مدير النظام الرئيسي', 'super_admin'],
        ['moderator', '<EMAIL>', 'مشرف النظام', 'moderator'],
        ['viewer', '<EMAIL>', 'مراقب النظام', 'admin']
    ];
    
    // First, delete existing admins to avoid conflicts
    $pdo->exec("DELETE FROM admins");
    $success[] = "تم حذف المديرين الموجودين";
    
    // Insert admins with correct password
    $stmt = $pdo->prepare("INSERT INTO admins (username, email, password, full_name, role, is_active) VALUES (?, ?, ?, ?, ?, 1)");
    
    foreach ($admins as $admin) {
        $stmt->execute([
            $admin[0], // username
            $admin[1], // email
            $correctPasswordHash, // password
            $admin[2], // full_name
            $admin[3]  // role
        ]);
        $success[] = "تم إنشاء المدير: " . $admin[0];
    }
    
    // Test password verification
    $testUser = $pdo->prepare("SELECT * FROM admins WHERE username = ?");
    $testUser->execute(['admin']);
    $user = $testUser->fetch();
    
    if ($user && password_verify('admin123', $user['password'])) {
        $success[] = "✅ تم التحقق من كلمة المرور بنجاح!";
    } else {
        $errors[] = "❌ فشل في التحقق من كلمة المرور";
    }
    
    // Show password hash for debugging
    $success[] = "Hash المستخدم: " . substr($correctPasswordHash, 0, 50) . "...";
    
} catch (PDOException $e) {
    $errors[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "خطأ عام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح كلمات مرور المديرين</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .fix-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <div class="fix-card w-full max-w-4xl p-8 rounded-2xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-key text-white text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">إصلاح كلمات مرور المديرين</h1>
            <p class="text-gray-600">تحديث كلمات المرور لحل مشكلة تسجيل الدخول</p>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Success Messages -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-green-700 flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    العمليات الناجحة
                </h2>
                
                <?php if (empty($success)): ?>
                    <div class="bg-gray-100 border border-gray-300 text-gray-600 px-4 py-3 rounded-lg">
                        لا توجد عمليات ناجحة
                    </div>
                <?php else: ?>
                    <?php foreach ($success as $message): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-600 ml-2"></i>
                                <span><?php echo htmlspecialchars($message); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- Error Messages -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-red-700 flex items-center">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    الأخطاء
                </h2>
                
                <?php if (empty($errors)): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-thumbs-up ml-2"></i>
                            <span>لا توجد أخطاء! تم الإصلاح بنجاح</span>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($errors as $error): ?>
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-times text-red-600 ml-2"></i>
                                <span><?php echo htmlspecialchars($error); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Login Credentials -->
        <div class="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 class="text-lg font-semibold text-blue-800 mb-4">بيانات تسجيل الدخول المحدثة</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white p-4 rounded-lg border">
                    <div class="font-bold text-blue-700 mb-2">المدير الرئيسي</div>
                    <div class="text-sm">
                        <div><strong>المستخدم:</strong> admin</div>
                        <div><strong>كلمة المرور:</strong> admin123</div>
                        <div><strong>الدور:</strong> Super Admin</div>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg border">
                    <div class="font-bold text-blue-700 mb-2">المشرف</div>
                    <div class="text-sm">
                        <div><strong>المستخدم:</strong> moderator</div>
                        <div><strong>كلمة المرور:</strong> admin123</div>
                        <div><strong>الدور:</strong> Moderator</div>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg border">
                    <div class="font-bold text-blue-700 mb-2">المراقب</div>
                    <div class="text-sm">
                        <div><strong>المستخدم:</strong> viewer</div>
                        <div><strong>كلمة المرور:</strong> admin123</div>
                        <div><strong>الدور:</strong> Admin</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Login -->
        <div class="mt-6 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 class="text-lg font-semibold text-yellow-800 mb-4">اختبار تسجيل الدخول</h3>
            <div class="space-y-3">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-yellow-600 ml-2"></i>
                    <span>يمكنك الآن تسجيل الدخول باستخدام البيانات أعلاه</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-yellow-600 ml-2"></i>
                    <span>تأكد من تغيير كلمات المرور بعد تسجيل الدخول</span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <?php if (empty($errors)): ?>
                <a href="admin_login.php" 
                   class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300 text-center">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل دخول الإدارة
                </a>
                
                <a href="admin_dashboard.php" 
                   class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-300 text-center">
                    <i class="fas fa-tachometer-alt ml-2"></i>
                    لوحة الإدارة
                </a>
            <?php else: ?>
                <button onclick="location.reload()" 
                        class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-300">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة المحاولة
                </button>
            <?php endif; ?>
            
            <a href="index.php" 
               class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-300 text-center">
                <i class="fas fa-home ml-2"></i>
                الصفحة الرئيسية
            </a>
        </div>

        <!-- Debug Information -->
        <?php if (!empty($success)): ?>
        <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <h4 class="text-sm font-semibold text-gray-700 mb-2">معلومات تقنية:</h4>
            <div class="text-xs text-gray-600 space-y-1">
                <div>قاعدة البيانات: <?php echo htmlspecialchars($database); ?></div>
                <div>خادم قاعدة البيانات: <?php echo htmlspecialchars($host); ?></div>
                <div>طريقة التشفير: PASSWORD_DEFAULT (bcrypt)</div>
                <div>تاريخ الإصلاح: <?php echo date('Y-m-d H:i:s'); ?></div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // Show success message if fix completed successfully
        <?php if (empty($errors) && !empty($success)): ?>
        setTimeout(() => {
            alert('تم إصلاح كلمات المرور بنجاح! يمكنك الآن تسجيل الدخول.');
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
