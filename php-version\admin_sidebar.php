<?php
/**
 * Admin Sidebar Component
 * مكون القائمة الجانبية للإدارة
 */

// Get current page to highlight active menu item
$currentPage = basename($_SERVER['PHP_SELF']);

// Get user role
$userRole = $_SESSION['admin_role'] ?? 'admin';

// Define pages based on role
if ($userRole === 'publisher') {
    // Publisher can only access courses management
    $adminPages = [
        'admin_courses.php' => ['icon' => 'fas fa-book', 'title' => 'نشر المواد']
    ];
} else {
    // Admin can access all pages
    $adminPages = [
        'admin_dashboard.php' => ['icon' => 'fas fa-tachometer-alt', 'title' => 'الرئيسية'],
        'admin_students.php' => ['icon' => 'fas fa-users', 'title' => 'إدارة الطلاب'],
        'admin_universities.php' => ['icon' => 'fas fa-university', 'title' => 'إدارة الجامعات'],
        'admin_courses.php' => ['icon' => 'fas fa-book', 'title' => 'إدارة المواد'],
        'admin_users.php' => ['icon' => 'fas fa-user-shield', 'title' => 'إدارة المستخدمين'],
        'admin_notifications.php' => ['icon' => 'fas fa-bell', 'title' => 'إدارة الإشعارات'],
        'admin_grading_scales.php' => ['icon' => 'fas fa-graduation-cap', 'title' => 'سلم الدرجات'],
        'admin_gpa_scales.php' => ['icon' => 'fas fa-chart-line', 'title' => 'مقاييس المعدل'],
        'admin_suggestions.php' => ['icon' => 'fas fa-lightbulb', 'title' => 'إعدادات الاقتراحات'],
        'admin_analytics.php' => ['icon' => 'fas fa-chart-bar', 'title' => 'إحصائيات الاستخدام'],
        'admin_settings.php' => ['icon' => 'fas fa-cog', 'title' => 'الإعدادات']
    ];
}

// Only include pages that actually exist and user has permission to access
$menuItems = [];
foreach ($adminPages as $page => $info) {
    $filePath = __DIR__ . '/' . $page;
    if (file_exists($filePath)) {
        $menuItems[] = [
            'url' => $page,
            'icon' => $info['icon'],
            'title' => $info['title'],
            'active' => $currentPage === basename($page)
        ];
    }
}

// Debug: uncomment to see what pages are available
echo "<!-- DEBUG: User Role: $userRole, Menu Items Count: " . count($menuItems) . " -->";

// Get current user info
$currentUser = [
    'id' => $_SESSION['admin_id'] ?? 1,
    'username' => $_SESSION['admin_username'] ?? 'admin',
    'role' => $_SESSION['admin_role'] ?? 'admin',
    'full_name' => $_SESSION['admin_name'] ?? 'مدير النظام'
];
?>

<!-- Sidebar for <?php echo $userRole; ?> -->
<div class="w-64 bg-white shadow-lg" data-role="<?php echo $userRole; ?>"><?php /* Force refresh */ ?>
    <div class="p-6 border-b">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <i class="fas fa-graduation-cap text-white"></i>
            </div>
            <div class="mr-3">
                <h2 class="text-lg font-semibold text-gray-800">لوحة الإدارة</h2>
                <p class="text-sm text-gray-600">حاسبة المعدل</p>
            </div>
        </div>
    </div>
    
    <nav class="mt-6">
        <!-- Main Menu Items -->
        <?php foreach ($menuItems as $item): ?>
            <a href="<?php echo $item['url']; ?>"
               class="flex items-center px-6 py-3 <?php echo $item['active'] ? 'text-blue-600 bg-blue-50 border-l-4 border-blue-600' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'; ?> transition-colors duration-300">
                <i class="<?php echo $item['icon']; ?> ml-3"></i>
                <?php echo $item['title']; ?>
            </a>
        <?php endforeach; ?>

        <?php if ($userRole === 'admin'): ?>
        <!-- Divider -->
        <div class="my-4 border-t border-gray-200"></div>

        <!-- Quick Actions -->
        <div class="px-6 py-2">
            <p class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">إجراءات سريعة</p>

            <a href="index.php" target="_blank" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-300 mb-1">
                <i class="fas fa-external-link-alt ml-2 text-xs"></i>
                عرض الموقع
            </a>

            <?php if (file_exists(__DIR__ . '/create_grading_scales.php')): ?>
            <a href="create_grading_scales.php" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-300 mb-1">
                <i class="fas fa-plus-circle ml-2 text-xs"></i>
                إنشاء سلم الدرجات
            </a>
            <?php endif; ?>

            <?php if (file_exists(__DIR__ . '/test_link_expiry.php')): ?>
            <a href="test_link_expiry.php" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-300 mb-1">
                <i class="fas fa-clock ml-2 text-xs"></i>
                اختبار انتهاء الروابط
            </a>
            <?php endif; ?>

            <?php if (file_exists(__DIR__ . '/maintenance.php')): ?>
            <a href="maintenance.php" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-300 mb-1">
                <i class="fas fa-tools ml-2 text-xs"></i>
                صفحة الصيانة
            </a>
            <?php endif; ?>

            <?php if (file_exists(__DIR__ . '/test_sync.php')): ?>
            <a href="test_sync.php" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-300 mb-1">
                <i class="fas fa-sync-alt ml-2 text-xs"></i>
                اختبار المزامنة
            </a>
            <?php endif; ?>

            <?php if (file_exists(__DIR__ . '/smart_fix.php')): ?>
            <a href="smart_fix.php" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors duration-300 mb-1">
                <i class="fas fa-magic ml-2 text-xs"></i>
                الإصلاح الذكي
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </nav>
    
    <div class="absolute bottom-0 w-64 p-6 border-t">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <i class="fas fa-user text-gray-600"></i>
            </div>
            <div class="mr-3 flex-1">
                <p class="text-sm font-medium text-gray-800"><?php echo htmlspecialchars($currentUser['full_name']); ?></p>
                <p class="text-xs text-gray-600">
                    <?php
                    $roleText = $currentUser['role'] === 'publisher' ? 'ناشر المواد' : 'مدير النظام';
                    echo $roleText;
                    ?>
                </p>
            </div>
            <a href="admin_logout.php" class="text-red-600 hover:text-red-800" title="تسجيل خروج">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>
