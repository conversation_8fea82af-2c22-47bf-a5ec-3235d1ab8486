<?php
/**
 * Test Student Data Save
 * اختبار حفظ بيانات الطلاب
 */

session_start();

// Initialize session variables
if (!isset($_SESSION['language'])) {
    $_SESSION['language'] = 'ar';
}
if (!isset($_SESSION['grading_system'])) {
    $_SESSION['grading_system'] = 'aou';
}
if (!isset($_SESSION['university'])) {
    $_SESSION['university'] = 'aou';
}

require_once 'functions_simple.php';

$results = [];
$errors = [];

// Test data
$testStudentData = [
    'name' => 'طالب تجريبي',
    'phone' => '0501234567',
    'gpa' => 3.75,
    'courses' => [
        ['name' => 'الرياضيات', 'hours' => 3, 'grade' => 'A', 'points' => 4.0],
        ['name' => 'الفيزياء', 'hours' => 4, 'grade' => 'B+', 'points' => 3.5],
        ['name' => 'الكيمياء', 'hours' => 3, 'grade' => 'A-', 'points' => 3.7]
    ],
    'calculation_type' => 'semester',
    'previous_gpa' => 3.5,
    'previous_hours' => 30
];

// Test database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $results[] = "✅ اتصال قاعدة البيانات ناجح";
    
    // Check if tables exist
    $tables = ['students', 'courses'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $results[] = "✅ جدول '$table' موجود";
        } else {
            $errors[] = "❌ جدول '$table' غير موجود";
        }
    }
    
} catch (PDOException $e) {
    $errors[] = "❌ خطأ في اتصال قاعدة البيانات: " . $e->getMessage();
}

// Test manual save
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_save'])) {
    $name = trim($_POST['name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $gpa = floatval($_POST['gpa'] ?? 0);
    $courses = [
        ['name' => $_POST['course1_name'] ?? '', 'hours' => intval($_POST['course1_hours'] ?? 0), 'grade' => $_POST['course1_grade'] ?? '', 'points' => floatval($_POST['course1_points'] ?? 0)],
        ['name' => $_POST['course2_name'] ?? '', 'hours' => intval($_POST['course2_hours'] ?? 0), 'grade' => $_POST['course2_grade'] ?? '', 'points' => floatval($_POST['course2_points'] ?? 0)]
    ];
    
    if (empty($name) || empty($phone)) {
        $errors[] = "❌ يرجى ملء جميع الحقول المطلوبة";
    } else {
        try {
            // Generate unique link ID
            $link_id = uniqid() . '_' . time();
            
            // Calculate total hours
            $total_hours = array_sum(array_column($courses, 'hours'));
            
            // Get classification
            $classification = getGPAClassification($gpa);
            
            // Insert student data
            $stmt = $pdo->prepare("
                INSERT INTO students (name, phone, university, grading_system, semester_gpa, cumulative_gpa, 
                                    total_hours, previous_gpa, previous_hours, classification, ip_address, 
                                    user_agent, share_link, link_expires_at, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $name,
                $phone,
                $_SESSION['university'] ?? 'aou',
                $_SESSION['grading_system'] ?? 'aou',
                $gpa,
                $gpa,
                $total_hours,
                0, // previous_gpa
                0, // previous_hours
                $classification['name'] ?? 'غير محدد',
                $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                $_SERVER['HTTP_USER_AGENT'] ?? 'Test Browser',
                $link_id,
                date('Y-m-d H:i:s', strtotime('+30 days'))
            ]);
            
            $student_id = $pdo->lastInsertId();
            $results[] = "✅ تم حفظ بيانات الطالب بنجاح (ID: $student_id)";
            
            // Insert courses
            if (!empty($courses)) {
                $courseStmt = $pdo->prepare("
                    INSERT INTO courses (student_id, course_name, credit_hours, grade, grade_points, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                
                $savedCourses = 0;
                foreach ($courses as $course) {
                    if (!empty($course['name']) && $course['hours'] > 0) {
                        $courseStmt->execute([
                            $student_id,
                            $course['name'],
                            $course['hours'],
                            $course['grade'],
                            $course['points']
                        ]);
                        $savedCourses++;
                    }
                }
                $results[] = "✅ تم حفظ $savedCourses مادة دراسية";
            }
            
            $results[] = "✅ رابط المشاركة: $link_id";
            
        } catch (PDOException $e) {
            $errors[] = "❌ خطأ في حفظ البيانات: " . $e->getMessage();
        } catch (Exception $e) {
            $errors[] = "❌ خطأ عام: " . $e->getMessage();
        }
    }
}

// Test AJAX save simulation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_ajax'])) {
    // Simulate the AJAX request
    $_POST['action'] = 'save_student_data';
    $_POST['name'] = 'طالب AJAX تجريبي';
    $_POST['phone'] = '0509876543';
    $_POST['gpa'] = 3.25;
    $_POST['courses'] = json_encode($testStudentData['courses']);
    $_POST['calculation_type'] = 'semester';
    $_POST['previous_gpa'] = 0;
    $_POST['previous_hours'] = 0;
    
    // Capture output
    ob_start();
    include 'index.php';
    $output = ob_get_clean();
    
    $results[] = "✅ تم اختبار AJAX: " . $output;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حفظ بيانات الطلاب</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">
                <i class="fas fa-user-graduate text-blue-600 ml-2"></i>
                اختبار حفظ بيانات الطلاب
            </h1>
            <p class="text-gray-600">اختبار وظيفة حفظ بيانات الطلاب في قاعدة البيانات</p>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Success Results -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-green-700 mb-4">
                    <i class="fas fa-check-circle ml-2"></i>
                    النتائج الناجحة (<?php echo count($results); ?>)
                </h2>
                
                <div class="space-y-2 max-h-64 overflow-y-auto">
                    <?php if (empty($results)): ?>
                        <div class="text-gray-500 text-center py-4">لا توجد نتائج</div>
                    <?php else: ?>
                        <?php foreach ($results as $result): ?>
                            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded-lg text-sm">
                                <?php echo htmlspecialchars($result); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Errors -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-red-700 mb-4">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    الأخطاء (<?php echo count($errors); ?>)
                </h2>
                
                <div class="space-y-2 max-h-64 overflow-y-auto">
                    <?php if (empty($errors)): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                            <i class="fas fa-thumbs-up ml-2"></i>
                            لا توجد أخطاء!
                        </div>
                    <?php else: ?>
                        <?php foreach ($errors as $error): ?>
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded-lg text-sm">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Manual Test Form -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">اختبار حفظ يدوي</h2>
            
            <form method="POST" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم الطالب</label>
                        <input type="text" name="name" value="طالب تجريبي <?php echo date('H:i:s'); ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                        <input type="text" name="phone" value="050<?php echo rand(1000000, 9999999); ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المعدل التراكمي</label>
                        <input type="number" name="gpa" value="3.75" step="0.01" min="0" max="4" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <!-- Courses -->
                <div class="border-t pt-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">المواد الدراسية</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Course 1 -->
                        <div class="border rounded-lg p-4">
                            <h4 class="font-medium text-gray-700 mb-2">المادة الأولى</h4>
                            <div class="space-y-2">
                                <input type="text" name="course1_name" value="الرياضيات المتقدمة" placeholder="اسم المادة"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <div class="grid grid-cols-3 gap-2">
                                    <input type="number" name="course1_hours" value="3" placeholder="الساعات" min="1" max="6"
                                           class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <input type="text" name="course1_grade" value="A" placeholder="التقدير"
                                           class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <input type="number" name="course1_points" value="4.0" placeholder="النقاط" step="0.1" min="0" max="4"
                                           class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Course 2 -->
                        <div class="border rounded-lg p-4">
                            <h4 class="font-medium text-gray-700 mb-2">المادة الثانية</h4>
                            <div class="space-y-2">
                                <input type="text" name="course2_name" value="الفيزياء العامة" placeholder="اسم المادة"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <div class="grid grid-cols-3 gap-2">
                                    <input type="number" name="course2_hours" value="4" placeholder="الساعات" min="1" max="6"
                                           class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <input type="text" name="course2_grade" value="B+" placeholder="التقدير"
                                           class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <input type="number" name="course2_points" value="3.5" placeholder="النقاط" step="0.1" min="0" max="4"
                                           class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex gap-4">
                    <button type="submit" name="test_save" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-save ml-2"></i>
                        اختبار الحفظ
                    </button>
                    
                    <button type="submit" name="test_ajax" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-code ml-2"></i>
                        اختبار AJAX
                    </button>
                </div>
            </form>
        </div>

        <!-- Current Students -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">الطلاب المحفوظون</h2>
            
            <?php
            try {
                $stmt = $pdo->query("SELECT id, name, phone, cumulative_gpa, total_hours, classification, created_at FROM students ORDER BY created_at DESC LIMIT 10");
                $students = $stmt->fetchAll();
                
                if ($students) {
                    echo '<div class="overflow-x-auto">';
                    echo '<table class="w-full border border-gray-200">';
                    echo '<thead class="bg-gray-50">';
                    echo '<tr>';
                    echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">ID</th>';
                    echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الاسم</th>';
                    echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الهاتف</th>';
                    echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">المعدل</th>';
                    echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الساعات</th>';
                    echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">التقدير</th>';
                    echo '<th class="px-4 py-2 text-right text-sm font-medium text-gray-700">التاريخ</th>';
                    echo '</tr>';
                    echo '</thead>';
                    echo '<tbody>';
                    
                    foreach ($students as $student) {
                        echo '<tr class="border-t">';
                        echo '<td class="px-4 py-2 text-sm">' . $student['id'] . '</td>';
                        echo '<td class="px-4 py-2 text-sm font-medium">' . htmlspecialchars($student['name']) . '</td>';
                        echo '<td class="px-4 py-2 text-sm">' . htmlspecialchars($student['phone']) . '</td>';
                        echo '<td class="px-4 py-2 text-sm">' . $student['cumulative_gpa'] . '</td>';
                        echo '<td class="px-4 py-2 text-sm">' . $student['total_hours'] . '</td>';
                        echo '<td class="px-4 py-2 text-sm">' . htmlspecialchars($student['classification']) . '</td>';
                        echo '<td class="px-4 py-2 text-sm text-gray-600">' . date('Y-m-d H:i', strtotime($student['created_at'])) . '</td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody>';
                    echo '</table>';
                    echo '</div>';
                } else {
                    echo '<p class="text-gray-500 text-center py-8">لا توجد بيانات طلاب محفوظة</p>';
                }
                
            } catch (Exception $e) {
                echo '<p class="text-red-600">خطأ في تحميل بيانات الطلاب: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
            ?>
        </div>

        <!-- Navigation -->
        <div class="mt-6 flex gap-4 justify-center">
            <a href="index.php" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-home ml-2"></i>
                الصفحة الرئيسية
            </a>
            
            <a href="admin_students.php" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-users ml-2"></i>
                إدارة الطلاب
            </a>
            
            <button onclick="location.reload()" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-redo ml-2"></i>
                إعادة الاختبار
            </button>
        </div>
    </div>
</body>
</html>
