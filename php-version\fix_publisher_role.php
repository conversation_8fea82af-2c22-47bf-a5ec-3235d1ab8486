<?php
// Fix publisher role

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fixing Publisher Role</h2>";
    
    // Update publisher user
    $stmt = $pdo->prepare("UPDATE admins SET role = 'publisher', is_active = 1, password = ? WHERE username = 'publisher'");
    $newPassword = password_hash('publisher123', PASSWORD_DEFAULT);
    $stmt->execute([$newPassword]);
    
    echo "<p>✓ Publisher role updated</p>";
    
    // Verify the update
    $stmt = $pdo->prepare("SELECT id, username, email, full_name, role, is_active FROM admins WHERE username = 'publisher'");
    $stmt->execute();
    $publisher = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($publisher) {
        echo "<h3>Updated Publisher Details:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        foreach ($publisher as $key => $value) {
            echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
        }
        echo "</table>";
        
        // Test login query
        echo "<h3>Testing Login Query:</h3>";
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE (username = ? OR email = ?) AND is_active = 1 AND role = 'publisher'");
        $stmt->execute(['publisher', 'publisher']);
        $testUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($testUser) {
            echo "<p>✓ Login query successful</p>";
            
            // Test password verification
            if (password_verify('publisher123', $testUser['password'])) {
                echo "<p>✓ Password verification successful</p>";
                echo "<p><strong>Login credentials are working!</strong></p>";
                echo "<p>Username: <code>publisher</code></p>";
                echo "<p>Password: <code>publisher123</code></p>";
            } else {
                echo "<p>❌ Password verification failed</p>";
            }
        } else {
            echo "<p>❌ Login query failed</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
</style>
