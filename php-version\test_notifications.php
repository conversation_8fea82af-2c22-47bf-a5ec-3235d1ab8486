<?php
session_start();

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Testing Notifications System</h2>";
    
    // Check if notifications table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'notifications'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color: green;'>✓ Table 'notifications' exists</p>";
        
        // Check notifications count
        $stmt = $pdo->query("SELECT COUNT(*) FROM notifications");
        $count = $stmt->fetchColumn();
        echo "<p>Total notifications: <strong>$count</strong></p>";
        
        if ($count > 0) {
            // Show all notifications
            $stmt = $pdo->query("SELECT * FROM notifications ORDER BY priority ASC, created_at DESC");
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>All Notifications:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Title (AR)</th><th>Type</th><th>Display Type</th><th>Target</th><th>Active</th><th>Priority</th></tr>";
            foreach ($notifications as $notification) {
                echo "<tr>";
                echo "<td>{$notification['id']}</td>";
                echo "<td>{$notification['title_ar']}</td>";
                echo "<td>{$notification['type']}</td>";
                echo "<td>{$notification['display_type']}</td>";
                echo "<td>{$notification['target_audience']}</td>";
                echo "<td>" . ($notification['is_active'] ? 'Yes' : 'No') . "</td>";
                echo "<td>{$notification['priority']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Test the get_notifications.php logic
            echo "<h3>Testing get_notifications.php Logic:</h3>";
            
            $userSession = session_id();
            $userType = 'students'; // Default to students
            $currentDate = date('Y-m-d H:i:s');
            
            echo "<p>User Session: $userSession</p>";
            echo "<p>User Type: $userType</p>";
            echo "<p>Current Date: $currentDate</p>";
            
            // Get active notifications that haven't been viewed by this user
            $sql = "
                SELECT n.* 
                FROM notifications n 
                LEFT JOIN notification_views nv ON n.id = nv.notification_id AND nv.user_session = ?
                WHERE n.is_active = 1 
                AND (n.target_audience = 'all' OR n.target_audience = ?)
                AND (n.start_date IS NULL OR n.start_date <= ?)
                AND (n.end_date IS NULL OR n.end_date >= ?)
                AND nv.id IS NULL
                ORDER BY n.priority ASC, n.created_at DESC
            ";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$userSession, $userType, $currentDate, $currentDate]);
            $activeNotifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p>Active notifications for this user: <strong>" . count($activeNotifications) . "</strong></p>";
            
            if (count($activeNotifications) > 0) {
                echo "<h4>Active Notifications:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>Display Type</th></tr>";
                foreach ($activeNotifications as $notification) {
                    echo "<tr>";
                    echo "<td>{$notification['id']}</td>";
                    echo "<td>{$notification['title_ar']}</td>";
                    echo "<td>{$notification['type']}</td>";
                    echo "<td>{$notification['display_type']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Separate popup and inline
                $popupNotifications = [];
                $inlineNotifications = [];
                
                foreach ($activeNotifications as $notification) {
                    if ($notification['display_type'] === 'popup') {
                        $popupNotifications[] = $notification;
                    } else {
                        $inlineNotifications[] = $notification;
                    }
                }
                
                echo "<p>Popup notifications: <strong>" . count($popupNotifications) . "</strong></p>";
                echo "<p>Inline notifications: <strong>" . count($inlineNotifications) . "</strong></p>";
            }
            
        } else {
            echo "<p style='color: orange;'>⚠ No notifications found. Adding sample data...</p>";
            
            // Insert sample notifications
            $notifications = [
                [
                    'title_ar' => 'مرحباً بكم في حاسبة المعدل',
                    'title_en' => 'Welcome to GPA Calculator',
                    'message_ar' => 'نرحب بكم في نظام حاسبة المعدل الجديد. يمكنكم الآن حساب معدلكم التراكمي بسهولة ودقة.',
                    'message_en' => 'Welcome to the new GPA Calculator system. You can now calculate your cumulative GPA easily and accurately.',
                    'type' => 'success',
                    'display_type' => 'popup',
                    'priority' => 1
                ],
                [
                    'title_ar' => 'تحديث النظام',
                    'title_en' => 'System Update',
                    'message_ar' => 'تم تحديث النظام بميزات جديدة تشمل البحث عن المواد وعرض تفاصيلها.',
                    'message_en' => 'The system has been updated with new features including course search and details display.',
                    'type' => 'info',
                    'display_type' => 'inline',
                    'priority' => 2
                ]
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO notifications (
                    title_ar, title_en, message_ar, message_en, type, display_type, priority
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($notifications as $notification) {
                $stmt->execute([
                    $notification['title_ar'],
                    $notification['title_en'],
                    $notification['message_ar'],
                    $notification['message_en'],
                    $notification['type'],
                    $notification['display_type'],
                    $notification['priority']
                ]);
            }
            
            echo "<p style='color: green;'>✓ Sample notifications added!</p>";
            echo "<p><a href='test_notifications.php'>Refresh to see notifications</a></p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Table 'notifications' does not exist</p>";
        echo "<p><a href='create_notifications_table.php'>Create notifications table</a></p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
</style>
