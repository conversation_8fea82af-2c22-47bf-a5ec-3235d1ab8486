<?php
/**
 * GPA Calculator Functions
 * Arab Open University System
 */

require_once 'config.php';

/**
 * Get current language
 */
function getCurrentLanguage() {
    return $_SESSION['language'] ?? DEFAULT_LANGUAGE;
}

/**
 * Get current grading system
 */
function getCurrentGradingSystem() {
    global $all_grading_systems;
    $system = $_SESSION['grading_system'] ?? DEFAULT_GRADING_SYSTEM;
    return $all_grading_systems[$system] ?? $all_grading_systems[DEFAULT_GRADING_SYSTEM];
}

/**
 * Get all grading systems
 */
function getAllGradingSystems() {
    global $all_grading_systems;
    return $all_grading_systems;
}

/**
 * Calculate GPA from courses
 */
function calculateGPA($courses) {
    if (empty($courses)) {
        return [
            'gpa' => 0.0,
            'total_points' => 0.0,
            'total_hours' => 0,
            'courses_count' => 0
        ];
    }
    
    $total_points = 0.0;
    $total_hours = 0;
    $valid_courses = 0;
    
    $grading_system = getCurrentGradingSystem();
    
    foreach ($courses as $course) {
        if (isset($course['grade']) && isset($course['hours']) && 
            $course['hours'] > 0 && isset($grading_system['grades'][$course['grade']])) {
            
            $grade_points = $grading_system['grades'][$course['grade']]['points'];
            $hours = (int)$course['hours'];
            
            $total_points += $grade_points * $hours;
            $total_hours += $hours;
            $valid_courses++;
        }
    }
    
    $gpa = $total_hours > 0 ? round($total_points / $total_hours, 2) : 0.0;
    
    return [
        'gpa' => $gpa,
        'total_points' => round($total_points, 2),
        'total_hours' => $total_hours,
        'courses_count' => $valid_courses
    ];
}

/**
 * Get GPA classification
 */
function getGPAClassification($gpa, $universityId = null) {
    $lang = getCurrentLanguage();

    // Get current university if not provided
    if ($universityId === null) {
        $universityId = $_SESSION['university'] ?? 'aou';
    }

    try {
        $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Get classification from database
        $stmt = $pdo->prepare("
            SELECT classification_ar, classification_en, color_code, min_gpa, max_gpa
            FROM university_gpa_classifications
            WHERE university_id = ? AND min_gpa <= ? AND max_gpa >= ?
            ORDER BY display_order ASC
            LIMIT 1
        ");
        $stmt->execute([$universityId, $gpa, $gpa]);
        $classification = $stmt->fetch();

        if ($classification) {
            return [
                'name' => $lang === 'ar' ? $classification['classification_ar'] : $classification['classification_en'],
                'name_ar' => $classification['classification_ar'],
                'name_en' => $classification['classification_en'],
                'color' => $classification['color_code'],
                'min' => (float)$classification['min_gpa'],
                'max' => (float)$classification['max_gpa'],
                'class' => strtolower(str_replace(' ', '-', $classification['classification_en']))
            ];
        }

    } catch (PDOException $e) {
        error_log("Error getting GPA classification: " . $e->getMessage());
    }

    // Fallback to default classifications if database lookup fails
    $defaultClassifications = [
        'ar' => [
            ['min' => 3.67, 'max' => 4.0, 'name' => 'ممتاز', 'class' => 'excellent', 'color' => '#10B981'],
            ['min' => 3.00, 'max' => 3.66, 'name' => 'جيد جداً', 'class' => 'very-good', 'color' => '#3B82F6'],
            ['min' => 2.33, 'max' => 2.99, 'name' => 'جيد', 'class' => 'good', 'color' => '#F59E0B'],
            ['min' => 2.00, 'max' => 2.32, 'name' => 'مقبول', 'class' => 'fair', 'color' => '#F97316'],
            ['min' => 0.0, 'max' => 1.99, 'name' => 'راسب', 'class' => 'fail', 'color' => '#EF4444']
        ],
        'en' => [
            ['min' => 3.67, 'max' => 4.0, 'name' => 'Excellent', 'class' => 'excellent', 'color' => '#10B981'],
            ['min' => 3.00, 'max' => 3.66, 'name' => 'Very Good', 'class' => 'very-good', 'color' => '#3B82F6'],
            ['min' => 2.33, 'max' => 2.99, 'name' => 'Good', 'class' => 'good', 'color' => '#F59E0B'],
            ['min' => 2.00, 'max' => 2.32, 'name' => 'Fair', 'class' => 'fair', 'color' => '#F97316'],
            ['min' => 0.0, 'max' => 1.99, 'name' => 'Fail', 'class' => 'fail', 'color' => '#EF4444']
        ]
    ];

    $currentClassifications = $defaultClassifications[$lang] ?? $defaultClassifications['ar'];

    foreach ($currentClassifications as $index => $classification) {
        if ($gpa >= $classification['min'] && $gpa <= $classification['max']) {
            return [
                'name' => $classification['name'],
                'name_ar' => $defaultClassifications['ar'][$index]['name'],
                'name_en' => $defaultClassifications['en'][$index]['name'],
                'class' => $classification['class'],
                'color' => $classification['color'],
                'min' => $classification['min'],
                'max' => $classification['max']
            ];
        }
    }

    // Default fallback
    return [
        'name' => $lang === 'ar' ? 'غير محدد' : 'Undefined',
        'name_ar' => 'غير محدد',
        'name_en' => 'Undefined',
        'class' => 'undefined',
        'color' => '#6B7280',
        'min' => 0,
        'max' => 0
    ];
}

/**
 * Save courses data
 */
function saveCourses($courses, $student_id = null) {
    $student_id = $student_id ?: session_id();
    $filename = DATA_DIR . "courses_{$student_id}.json";
    
    $data = [
        'courses' => $courses,
        'timestamp' => time(),
        'language' => getCurrentLanguage(),
        'grading_system' => $_SESSION['grading_system']
    ];
    
    return file_put_contents($filename, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
}

/**
 * Load courses data
 */
function loadCourses($student_id = null) {
    $student_id = $student_id ?: session_id();
    $filename = DATA_DIR . "courses_{$student_id}.json";
    
    if (!file_exists($filename)) {
        return [];
    }
    
    $data = json_decode(file_get_contents($filename), true);
    return $data['courses'] ?? [];
}

/**
 * Generate unique course ID
 */
function generateCourseId() {
    return 'course_' . uniqid();
}

/**
 * Sanitize input
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Get translation
 */
function t($key, $default = '') {
    $lang = getCurrentLanguage();
    
    $translations = [
        'ar' => [
            'app_title' => 'حاسبة المعدل التراكمي',
            'university_name' => 'الجامعة العربية المفتوحة',
            'add_course' => 'إضافة مادة',
            'calculate_gpa' => 'احسب المعدل',
            'course_name' => 'اسم المادة',
            'course_hours' => 'عدد الساعات',
            'course_grade' => 'التقدير',
            'gpa_result' => 'المعدل التراكمي',
            'total_hours' => 'إجمالي الساعات',
            'classification' => 'التصنيف',
            'save_data' => 'حفظ البيانات',
            'load_data' => 'تحميل البيانات',
            'share' => 'مشاركة',
            'language' => 'اللغة',
            'grading_system' => 'نظام التقدير'
        ],
        'en' => [
            'app_title' => 'GPA Calculator',
            'university_name' => 'Arab Open University',
            'add_course' => 'Add Course',
            'calculate_gpa' => 'Calculate GPA',
            'course_name' => 'Course Name',
            'course_hours' => 'Credit Hours',
            'course_grade' => 'Grade',
            'gpa_result' => 'GPA Result',
            'total_hours' => 'Total Hours',
            'classification' => 'Classification',
            'save_data' => 'Save Data',
            'load_data' => 'Load Data',
            'share' => 'Share',
            'language' => 'Language',
            'grading_system' => 'Grading System'
        ]
    ];
    
    return $translations[$lang][$key] ?? $default;
}

/**
 * Handle AJAX requests
 */
if (isset($_POST['action'])) {
    global $all_grading_systems;

    switch ($_POST['action']) {
        case 'calculate_gpa':
            $courses = json_decode($_POST['courses'], true);
            $result = calculateGPA($courses);
            $result['classification'] = getGPAClassification($result['gpa']);
            echo json_encode($result);
            break;

        case 'save_courses':
            $courses = json_decode($_POST['courses'], true);
            $success = saveCourses($courses);
            echo json_encode(['success' => $success]);
            break;

        case 'load_courses':
            $courses = loadCourses();
            echo json_encode(['courses' => $courses]);
            break;

        case 'change_language':
            $language = sanitizeInput($_POST['language']);
            if (in_array($language, ['ar', 'en'])) {
                $_SESSION['language'] = $language;
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['error' => 'لغة غير صحيحة']);
            }
            break;

        case 'change_grading_system':
            $system = sanitizeInput($_POST['system']);
            if (isset($all_grading_systems[$system])) {
                $_SESSION['grading_system'] = $system;
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['error' => 'نظام تقدير غير صحيح']);
            }
            break;

        case 'save_student_data':
            $name = sanitizeInput($_POST['name']);
            $phone = sanitizeInput($_POST['phone']);
            $gpa = floatval($_POST['gpa']);
            $courses = json_decode($_POST['courses'], true);
            $university = $_SESSION['selected_university'] ?? 'aou';

            $student_data = [
                'name' => $name,
                'phone' => $phone,
                'gpa' => $gpa,
                'courses' => $courses,
                'university' => $university,
                'date' => date('Y-m-d'),
                'timestamp' => date('Y-m-d H:i:s')
            ];

            $filename = 'student_' . time() . '_' . substr(md5($phone), 0, 8) . '.json';
            $filepath = __DIR__ . '/data/' . $filename;

            if (!is_dir(__DIR__ . '/data/')) {
                mkdir(__DIR__ . '/data/', 0755, true);
            }

            if (file_put_contents($filepath, json_encode($student_data, JSON_PRETTY_PRINT))) {
                echo json_encode(['success' => true, 'id' => $filename]);
            } else {
                echo json_encode(['error' => 'فشل في حفظ البيانات']);
            }
            break;
    }
    exit;
}

/**
 * Sanitize input (alias for backward compatibility)
 */
function sanitize_input($input) {
    return sanitizeInput($input);
}

/**
 * Load courses (alias for backward compatibility)
 */
function load_courses($student_id = null) {
    return loadCourses($student_id);
}

/**
 * Save courses (alias for backward compatibility)
 */
function save_courses($courses, $student_id = null) {
    return saveCourses($courses, $student_id);
}
?>
