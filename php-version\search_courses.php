<?php
header('Content-Type: application/json');

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit;
}

$query = trim($_POST['query'] ?? '');

if (empty($query) || strlen($query) < 2) {
    echo json_encode(['success' => false, 'error' => 'Query too short']);
    exit;
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Search courses by code or title
    $searchQuery = "%$query%";
    $stmt = $pdo->prepare("
        SELECT 
            course_code,
            course_title_ar,
            course_title_en,
            prerequisites,
            credit_hours,
            course_description_ar,
            course_description_en,
            course_objectives_ar,
            course_objectives_en,
            course_outcomes_ar,
            course_outcomes_en,
            university_id
        FROM course_catalog 
        WHERE is_active = 1 
        AND (
            course_code LIKE ? 
            OR course_title_ar LIKE ? 
            OR course_title_en LIKE ?
        )
        ORDER BY 
            CASE 
                WHEN course_code LIKE ? THEN 1
                WHEN course_title_ar LIKE ? THEN 2
                WHEN course_title_en LIKE ? THEN 3
                ELSE 4
            END,
            course_code
        LIMIT 10
    ");
    
    $stmt->execute([
        $searchQuery, $searchQuery, $searchQuery,
        $searchQuery, $searchQuery, $searchQuery
    ]);
    
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'courses' => $courses,
        'count' => count($courses)
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'General error: ' . $e->getMessage()
    ]);
}
?>
