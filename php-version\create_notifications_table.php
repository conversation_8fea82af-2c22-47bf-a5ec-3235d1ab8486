<?php
// Create notifications table

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Creating notifications table...\n";
    
    // Create notifications table
    $sql = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title_ar VARCHAR(255) NOT NULL,
        title_en VARCHAR(255) NOT NULL,
        message_ar TEXT NOT NULL,
        message_en TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
        display_type ENUM('popup', 'inline') DEFAULT 'popup',
        is_active BOOLEAN DEFAULT TRUE,
        start_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        end_date DATETIME NULL,
        target_audience ENUM('all', 'students', 'admins') DEFAULT 'all',
        priority INT DEFAULT 1,
        created_by INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_active (is_active),
        INDEX idx_type (type),
        INDEX idx_display_type (display_type),
        INDEX idx_dates (start_date, end_date),
        INDEX idx_priority (priority)
    )";
    
    $pdo->exec($sql);
    echo "Notifications table created successfully!\n";
    
    // Create notification views table (to track which users have seen which notifications)
    $sql2 = "CREATE TABLE IF NOT EXISTS notification_views (
        id INT AUTO_INCREMENT PRIMARY KEY,
        notification_id INT NOT NULL,
        user_session VARCHAR(255) NOT NULL,
        viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
        UNIQUE KEY unique_view (notification_id, user_session),
        INDEX idx_notification (notification_id),
        INDEX idx_session (user_session)
    )";
    
    $pdo->exec($sql2);
    echo "Notification views table created successfully!\n";
    
    // Check if sample data exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM notifications");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "Inserting sample notification data...\n";
        
        // Insert sample notifications
        $notifications = [
            [
                'title_ar' => 'مرحباً بكم في حاسبة المعدل',
                'title_en' => 'Welcome to GPA Calculator',
                'message_ar' => 'نرحب بكم في نظام حاسبة المعدل الجديد. يمكنكم الآن حساب معدلكم التراكمي بسهولة ودقة.',
                'message_en' => 'Welcome to the new GPA Calculator system. You can now calculate your cumulative GPA easily and accurately.',
                'type' => 'success',
                'display_type' => 'popup',
                'priority' => 1
            ],
            [
                'title_ar' => 'تحديث النظام',
                'title_en' => 'System Update',
                'message_ar' => 'تم تحديث النظام بميزات جديدة تشمل البحث عن المواد وعرض تفاصيلها.',
                'message_en' => 'The system has been updated with new features including course search and details display.',
                'type' => 'info',
                'display_type' => 'inline',
                'priority' => 2
            ],
            [
                'title_ar' => 'نصائح لتحسين المعدل',
                'title_en' => 'Tips to Improve GPA',
                'message_ar' => 'احرص على حضور المحاضرات والمشاركة الفعالة، وقم بمراجعة المواد بانتظام.',
                'message_en' => 'Make sure to attend lectures and participate actively, and review materials regularly.',
                'type' => 'warning',
                'display_type' => 'inline',
                'priority' => 3
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO notifications (
                title_ar, title_en, message_ar, message_en, type, display_type, priority
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($notifications as $notification) {
            $stmt->execute([
                $notification['title_ar'],
                $notification['title_en'],
                $notification['message_ar'],
                $notification['message_en'],
                $notification['type'],
                $notification['display_type'],
                $notification['priority']
            ]);
        }
        
        echo "Sample notification data inserted successfully!\n";
    } else {
        echo "Notification data already exists. Skipping insertion.\n";
    }
    
    echo "Setup completed successfully!\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
