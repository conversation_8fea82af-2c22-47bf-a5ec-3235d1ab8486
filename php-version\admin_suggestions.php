<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['admin_id']) || $_SESSION['admin_role'] !== 'admin') {
    header('Location: simple_admin_login.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_global_settings':
                    // Update global suggestion settings
                    $settings = [
                        'suggestions_enabled' => isset($_POST['suggestions_enabled']) ? 1 : 0,
                        'default_complexity' => $_POST['default_complexity'] ?? 'detailed',
                        'default_motivation_level' => $_POST['default_motivation_level'] ?? 'balanced',
                        'max_suggestions_per_user' => (int)($_POST['max_suggestions_per_user'] ?? 8),
                        'suggestion_refresh_interval' => (int)($_POST['suggestion_refresh_interval'] ?? 24)
                    ];

                    foreach ($settings as $key => $value) {
                        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
                        $stmt->execute([$key, $value, $value]);
                    }

                    $message = 'تم تحديث الإعدادات العامة بنجاح';
                    break;

                case 'clear_user_preferences':
                    // This would clear user preferences from localStorage (handled by JavaScript)
                    $message = 'تم إرسال أمر مسح تفضيلات المستخدمين';
                    break;

                case 'reset_suggestion_history':
                    // Clear suggestion history from database if stored
                    $stmt = $pdo->prepare("DELETE FROM activity_logs WHERE action LIKE '%suggestion%'");
                    $stmt->execute();
                    $message = 'تم مسح تاريخ الاقتراحات';
                    break;

                case 'toggle_suggestion_type':
                    $type = $_POST['type'] ?? '';
                    $enabled = (int)($_POST['enabled'] ?? 0);

                    if ($type) {
                        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
                        $stmt->execute(["suggestion_type_{$type}", $enabled, $enabled]);

                        header('Content-Type: application/json');
                        echo json_encode(['success' => true]);
                        exit;
                    }
                    break;

                case 'update_advanced_settings':
                    $advancedSettings = [
                        'ai_sensitivity' => $_POST['ai_sensitivity'] ?? 'medium',
                        'personalization_level' => $_POST['personalization_level'] ?? 'standard',
                        'learning_rate' => (int)($_POST['learning_rate'] ?? 50),
                        'suggestion_frequency' => $_POST['suggestion_frequency'] ?? 'daily',
                        'supported_languages' => implode(',', $_POST['languages'] ?? ['ar', 'en'])
                    ];

                    foreach ($advancedSettings as $key => $value) {
                        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
                        $stmt->execute([$key, $value, $value]);
                    }

                    $message = 'تم تحديث الإعدادات المتقدمة بنجاح';
                    break;

                case 'reset_to_defaults':
                    // Reset all suggestion settings to defaults
                    $defaultSettings = [
                        'suggestions_enabled' => 1,
                        'default_complexity' => 'detailed',
                        'default_motivation_level' => 'balanced',
                        'max_suggestions_per_user' => 8,
                        'suggestion_refresh_interval' => 24,
                        'ai_sensitivity' => 'medium',
                        'personalization_level' => 'standard',
                        'learning_rate' => 50,
                        'suggestion_frequency' => 'daily',
                        'supported_languages' => 'ar,en'
                    ];

                    // Enable all suggestion types by default
                    $suggestionTypes = [
                        'academic_insights', 'career_guidance', 'study_tips', 'future_projections',
                        'motivational_insights', 'ai_recommendations', 'progress_tracking', 'trend_analysis'
                    ];

                    foreach ($suggestionTypes as $type) {
                        $defaultSettings["suggestion_type_{$type}"] = 1;
                    }

                    foreach ($defaultSettings as $key => $value) {
                        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
                        $stmt->execute([$key, $value, $value]);
                    }

                    header('Content-Type: application/json');
                    echo json_encode(['success' => true]);
                    exit;
            }
        }
    } catch (PDOException $e) {
        $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// Get current settings
$currentSettings = [];
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE '%suggestion%' OR setting_key LIKE '%default_%'");
    while ($row = $stmt->fetch()) {
        $currentSettings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    $error = 'خطأ في تحميل الإعدادات: ' . $e->getMessage();
}

// Default values
$defaults = [
    'suggestions_enabled' => 1,
    'default_complexity' => 'detailed',
    'default_motivation_level' => 'balanced',
    'max_suggestions_per_user' => 8,
    'suggestion_refresh_interval' => 24
];

foreach ($defaults as $key => $value) {
    if (!isset($currentSettings[$key])) {
        $currentSettings[$key] = $value;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الاقتراحات الذكية - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-lightbulb text-yellow-500 ml-3"></i>
                            إعدادات الاقتراحات الذكية
                        </h1>
                        <p class="text-gray-600 mt-1">إدارة وتخصيص نظام الاقتراحات الذكية للمستخدمين</p>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="p-6">
                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        <i class="fas fa-check-circle ml-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <i class="fas fa-exclamation-circle ml-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Global Settings -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-cog text-blue-600 ml-2"></i>
                        الإعدادات العامة
                    </h2>

                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="action" value="update_global_settings">

                        <!-- Enable/Disable Suggestions -->
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h3 class="font-semibold text-gray-800">تفعيل نظام الاقتراحات</h3>
                                <p class="text-sm text-gray-600">تشغيل أو إيقاف نظام الاقتراحات الذكية للمستخدمين</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" name="suggestions_enabled" value="1" 
                                       <?php echo $currentSettings['suggestions_enabled'] ? 'checked' : ''; ?>
                                       class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>

                        <!-- Default Complexity -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">مستوى التفصيل الافتراضي</label>
                                <select name="default_complexity" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="simple" <?php echo $currentSettings['default_complexity'] === 'simple' ? 'selected' : ''; ?>>بسيط</option>
                                    <option value="detailed" <?php echo $currentSettings['default_complexity'] === 'detailed' ? 'selected' : ''; ?>>مفصل</option>
                                    <option value="advanced" <?php echo $currentSettings['default_complexity'] === 'advanced' ? 'selected' : ''; ?>>متقدم</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">مستوى التحفيز الافتراضي</label>
                                <select name="default_motivation_level" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="low" <?php echo $currentSettings['default_motivation_level'] === 'low' ? 'selected' : ''; ?>>منخفض</option>
                                    <option value="balanced" <?php echo $currentSettings['default_motivation_level'] === 'balanced' ? 'selected' : ''; ?>>متوازن</option>
                                    <option value="high" <?php echo $currentSettings['default_motivation_level'] === 'high' ? 'selected' : ''; ?>>عالي</option>
                                </select>
                            </div>
                        </div>

                        <!-- Numeric Settings -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الحد الأقصى للاقتراحات لكل مستخدم</label>
                                <input type="number" name="max_suggestions_per_user" min="1" max="20" 
                                       value="<?php echo htmlspecialchars($currentSettings['max_suggestions_per_user']); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">فترة تحديث الاقتراحات (ساعة)</label>
                                <input type="number" name="suggestion_refresh_interval" min="1" max="168" 
                                       value="<?php echo htmlspecialchars($currentSettings['suggestion_refresh_interval']); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-save ml-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Suggestion Types Management -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-list text-purple-600 ml-2"></i>
                        إدارة أنواع الاقتراحات
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php
                        $suggestionTypes = [
                            'academic_insights' => ['name' => 'الرؤى الأكاديمية', 'icon' => 'fas fa-graduation-cap', 'color' => 'blue'],
                            'career_guidance' => ['name' => 'التوجيه المهني', 'icon' => 'fas fa-briefcase', 'color' => 'green'],
                            'study_tips' => ['name' => 'نصائح الدراسة', 'icon' => 'fas fa-book-open', 'color' => 'yellow'],
                            'future_projections' => ['name' => 'التوقعات المستقبلية', 'icon' => 'fas fa-chart-line', 'color' => 'purple'],
                            'motivational_insights' => ['name' => 'الرؤى التحفيزية', 'icon' => 'fas fa-heart', 'color' => 'pink'],
                            'ai_recommendations' => ['name' => 'التوصيات الذكية', 'icon' => 'fas fa-robot', 'color' => 'indigo'],
                            'progress_tracking' => ['name' => 'تتبع التقدم', 'icon' => 'fas fa-chart-bar', 'color' => 'teal'],
                            'trend_analysis' => ['name' => 'تحليل الاتجاهات', 'icon' => 'fas fa-trending-up', 'color' => 'orange']
                        ];

                        foreach ($suggestionTypes as $type => $info):
                            $isEnabled = $currentSettings["suggestion_type_{$type}"] ?? 1;
                        ?>
                            <div class="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-<?php echo $info['color']; ?>-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="<?php echo $info['icon']; ?> text-<?php echo $info['color']; ?>-600 text-sm"></i>
                                        </div>
                                        <span class="font-medium text-gray-800"><?php echo $info['name']; ?></span>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="suggestion-type-toggle sr-only peer"
                                               data-type="<?php echo $type; ?>" <?php echo $isEnabled ? 'checked' : ''; ?>>
                                        <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <p class="text-xs text-gray-600">
                                    <?php
                                    $descriptions = [
                                        'academic_insights' => 'تحليل الأداء الأكاديمي والتفوق',
                                        'career_guidance' => 'نصائح للتحضير المهني والوظيفي',
                                        'study_tips' => 'استراتيجيات وتقنيات الدراسة',
                                        'future_projections' => 'توقعات المعدل والتخرج',
                                        'motivational_insights' => 'رسائل تحفيزية ودعم نفسي',
                                        'ai_recommendations' => 'توصيات ذكية مخصصة',
                                        'progress_tracking' => 'تتبع التقدم والإنجازات',
                                        'trend_analysis' => 'تحليل اتجاهات الأداء'
                                    ];
                                    echo $descriptions[$type] ?? '';
                                    ?>
                                </p>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Advanced Settings -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-sliders-h text-indigo-600 ml-2"></i>
                        الإعدادات المتقدمة
                    </h2>

                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="action" value="update_advanced_settings">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- AI Sensitivity -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">حساسية الذكاء الاصطناعي</label>
                                <select name="ai_sensitivity" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="low">منخفضة - اقتراحات أقل تفصيلاً</option>
                                    <option value="medium" selected>متوسطة - توازن مثالي</option>
                                    <option value="high">عالية - اقتراحات مفصلة جداً</option>
                                </select>
                            </div>

                            <!-- Personalization Level -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">مستوى التخصيص</label>
                                <select name="personalization_level" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="basic">أساسي - تخصيص محدود</option>
                                    <option value="standard" selected>قياسي - تخصيص متوسط</option>
                                    <option value="advanced">متقدم - تخصيص كامل</option>
                                </select>
                            </div>

                            <!-- Learning Rate -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">معدل التعلم من التفاعل</label>
                                <input type="range" name="learning_rate" min="0" max="100" value="50"
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>بطيء</span>
                                    <span>متوسط</span>
                                    <span>سريع</span>
                                </div>
                            </div>

                            <!-- Suggestion Frequency -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">تكرار الاقتراحات</label>
                                <select name="suggestion_frequency" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    <option value="immediate">فوري - مع كل حساب</option>
                                    <option value="daily" selected>يومي - مرة واحدة يومياً</option>
                                    <option value="weekly">أسبوعي - مرة واحدة أسبوعياً</option>
                                </select>
                            </div>
                        </div>

                        <!-- Language Preferences -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تفضيلات اللغة</label>
                            <div class="flex space-x-4 space-x-reverse">
                                <label class="flex items-center">
                                    <input type="checkbox" name="languages[]" value="ar" checked class="mr-2">
                                    <span>العربية</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="languages[]" value="en" checked class="mr-2">
                                    <span>English</span>
                                </label>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors">
                                <i class="fas fa-save ml-2"></i>
                                حفظ الإعدادات المتقدمة
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Management Actions -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-tools text-orange-600 ml-2"></i>
                        إجراءات الإدارة
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Clear User Preferences -->
                        <div class="p-4 border border-yellow-200 rounded-lg bg-yellow-50">
                            <h3 class="font-semibold text-gray-800 mb-2">مسح تفضيلات المستخدمين</h3>
                            <p class="text-sm text-gray-600 mb-4">إعادة تعيين جميع تفضيلات المستخدمين للاقتراحات</p>
                            <button onclick="clearUserPreferences()" class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors w-full">
                                <i class="fas fa-eraser ml-2"></i>
                                مسح التفضيلات
                            </button>
                        </div>

                        <!-- Reset History -->
                        <div class="p-4 border border-red-200 rounded-lg bg-red-50">
                            <h3 class="font-semibold text-gray-800 mb-2">مسح تاريخ الاقتراحات</h3>
                            <p class="text-sm text-gray-600 mb-4">حذف جميع سجلات الاقتراحات السابقة</p>
                            <form method="POST" style="display: inline;" class="w-full">
                                <input type="hidden" name="action" value="reset_suggestion_history">
                                <button type="submit" onclick="return confirm('هل أنت متأكد من حذف جميع سجلات الاقتراحات؟')"
                                        class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors w-full">
                                    <i class="fas fa-trash ml-2"></i>
                                    مسح التاريخ
                                </button>
                            </form>
                        </div>

                        <!-- Export Settings -->
                        <div class="p-4 border border-green-200 rounded-lg bg-green-50">
                            <h3 class="font-semibold text-gray-800 mb-2">تصدير الإعدادات</h3>
                            <p class="text-sm text-gray-600 mb-4">تصدير جميع إعدادات الاقتراحات كملف JSON</p>
                            <button onclick="exportSettings()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors w-full">
                                <i class="fas fa-download ml-2"></i>
                                تصدير الإعدادات
                            </button>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="mt-6 p-4 border border-blue-200 rounded-lg bg-blue-50">
                        <h3 class="font-semibold text-gray-800 mb-4">إجراءات جماعية</h3>
                        <div class="flex flex-wrap gap-3">
                            <button onclick="enableAllSuggestions()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-check-circle ml-2"></i>
                                تفعيل جميع الاقتراحات
                            </button>
                            <button onclick="disableAllSuggestions()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-times-circle ml-2"></i>
                                إيقاف جميع الاقتراحات
                            </button>
                            <button onclick="resetToDefaults()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                                <i class="fas fa-undo ml-2"></i>
                                إعادة للإعدادات الافتراضية
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle suggestion types
        document.addEventListener('DOMContentLoaded', function() {
            const toggles = document.querySelectorAll('.suggestion-type-toggle');
            toggles.forEach(toggle => {
                toggle.addEventListener('change', function() {
                    const type = this.dataset.type;
                    const enabled = this.checked ? 1 : 0;

                    fetch('admin_suggestions.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=toggle_suggestion_type&type=${type}&enabled=${enabled}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification('تم تحديث نوع الاقتراح بنجاح', 'success');
                        } else {
                            showNotification('حدث خطأ في التحديث', 'error');
                            this.checked = !this.checked; // Revert toggle
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('حدث خطأ في الاتصال', 'error');
                        this.checked = !this.checked; // Revert toggle
                    });
                });
            });
        });

        function clearUserPreferences() {
            if (confirm('هل أنت متأكد من مسح جميع تفضيلات المستخدمين؟\nسيؤثر هذا على جميع المستخدمين ولا يمكن التراجع عنه.')) {
                fetch('admin_suggestions.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=clear_user_preferences'
                })
                .then(response => response.text())
                .then(() => {
                    showNotification('تم إرسال أمر مسح التفضيلات لجميع المستخدمين', 'success');
                    setTimeout(() => location.reload(), 2000);
                });
            }
        }

        function exportSettings() {
            fetch('admin_suggestions.php?export=1')
                .then(response => response.json())
                .then(data => {
                    const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `suggestion_settings_${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    showNotification('تم تصدير الإعدادات بنجاح', 'success');
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في تصدير الإعدادات', 'error');
                });
        }

        function enableAllSuggestions() {
            if (confirm('هل تريد تفعيل جميع أنواع الاقتراحات؟')) {
                const toggles = document.querySelectorAll('.suggestion-type-toggle');
                toggles.forEach(toggle => {
                    toggle.checked = true;
                    toggle.dispatchEvent(new Event('change'));
                });
                showNotification('تم تفعيل جميع أنواع الاقتراحات', 'success');
            }
        }

        function disableAllSuggestions() {
            if (confirm('هل تريد إيقاف جميع أنواع الاقتراحات؟\nسيؤثر هذا على تجربة المستخدمين.')) {
                const toggles = document.querySelectorAll('.suggestion-type-toggle');
                toggles.forEach(toggle => {
                    toggle.checked = false;
                    toggle.dispatchEvent(new Event('change'));
                });
                showNotification('تم إيقاف جميع أنواع الاقتراحات', 'warning');
            }
        }

        function resetToDefaults() {
            if (confirm('هل تريد إعادة جميع الإعدادات للقيم الافتراضية؟\nسيتم فقدان جميع التخصيصات الحالية.')) {
                fetch('admin_suggestions.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=reset_to_defaults'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('تم إعادة تعيين الإعدادات للقيم الافتراضية', 'success');
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showNotification('حدث خطأ في إعادة التعيين', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في الاتصال', 'error');
                });
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const colors = {
                success: 'bg-green-100 border-green-400 text-green-700',
                error: 'bg-red-100 border-red-400 text-red-700',
                warning: 'bg-yellow-100 border-yellow-400 text-yellow-700',
                info: 'bg-blue-100 border-blue-400 text-blue-700'
            };

            notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded border ${colors[type]} shadow-lg transition-all duration-300 transform translate-x-full`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} ml-2"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="mr-4 text-lg">&times;</button>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }

        // Real-time settings preview
        document.addEventListener('DOMContentLoaded', function() {
            const rangeInputs = document.querySelectorAll('input[type="range"]');
            rangeInputs.forEach(input => {
                const updateValue = () => {
                    const value = input.value;
                    const max = input.max;
                    const percentage = (value / max) * 100;
                    input.style.background = `linear-gradient(to right, #3B82F6 0%, #3B82F6 ${percentage}%, #E5E7EB ${percentage}%, #E5E7EB 100%)`;
                };

                input.addEventListener('input', updateValue);
                updateValue(); // Initial call
            });
        });
    </script>
</body>
</html>
