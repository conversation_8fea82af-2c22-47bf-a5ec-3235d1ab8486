<?php
/**
 * Update Existing Database Script
 * سكريبت تحديث قاعدة البيانات الموجودة
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator'; // استخدام قاعدة البيانات الموجودة

$success = [];
$errors = [];
$warnings = [];

try {
    // Connect to the existing database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);
    
    $success[] = "تم الاتصال بقاعدة البيانات '$database' بنجاح";
    
    // Check if database exists and has tables
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $success[] = "تم العثور على " . count($tables) . " جدول في قاعدة البيانات";
    
    // Update database structure
    updateDatabaseStructure($pdo, $success, $errors, $warnings);
    
    // Insert default data
    insertDefaultData($pdo, $success, $errors, $warnings);
    
    // Update config file to use existing database
    updateConfigFile($success, $errors);
    
    // Test the updated system
    testUpdatedSystem($pdo, $success, $errors);
    
} catch (PDOException $e) {
    $errors[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "خطأ عام: " . $e->getMessage();
}

/**
 * Update database structure
 */
function updateDatabaseStructure($pdo, &$success, &$errors, &$warnings) {
    // Create admins table
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                login_attempts INT DEFAULT 0,
                locked_until TIMESTAMP NULL,
                avatar VARCHAR(255) NULL,
                phone VARCHAR(20) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $success[] = "تم إنشاء جدول المديرين (admins)";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'already exists') === false) {
            $errors[] = "خطأ في إنشاء جدول المديرين: " . $e->getMessage();
        } else {
            $warnings[] = "جدول المديرين موجود مسبقاً";
        }
    }
    
    // Update or create students table
    try {
        // Check if students table exists
        $tableExists = false;
        try {
            $pdo->query("SELECT 1 FROM students LIMIT 1");
            $tableExists = true;
        } catch (PDOException $e) {
            // Table doesn't exist
        }

        if (!$tableExists) {
            // Create students table from scratch
            $pdo->exec("
                CREATE TABLE students (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    phone VARCHAR(20) NOT NULL,
                    email VARCHAR(100) NULL,
                    student_id VARCHAR(50) NULL,
                    university VARCHAR(50) NOT NULL DEFAULT 'aou',
                    grading_system VARCHAR(50) NOT NULL DEFAULT 'aou',
                    semester_gpa DECIMAL(3,2) NULL,
                    cumulative_gpa DECIMAL(3,2) NULL,
                    total_hours INT DEFAULT 0,
                    previous_gpa DECIMAL(3,2) NULL,
                    previous_hours INT DEFAULT 0,
                    classification VARCHAR(50) NULL,
                    ip_address VARCHAR(45) NULL,
                    user_agent TEXT NULL,
                    share_link VARCHAR(100) UNIQUE NULL,
                    link_expires_at TIMESTAMP NULL,
                    is_verified BOOLEAN DEFAULT FALSE,
                    notes TEXT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_university (university),
                    INDEX idx_created_at (created_at),
                    INDEX idx_share_link (share_link),
                    INDEX idx_gpa (cumulative_gpa)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            $success[] = "تم إنشاء جدول الطلاب من الصفر";
        } else {
            // Update existing table
            // First, check what columns exist in the students table
            $existingColumns = [];
            $result = $pdo->query("DESCRIBE students");
            while ($row = $result->fetch()) {
                $existingColumns[] = $row['Field'];
            }

        // Define columns to add with their positions
        $columnsToAdd = [
            'email' => "ADD COLUMN email VARCHAR(100) NULL",
            'student_id' => "ADD COLUMN student_id VARCHAR(50) NULL",
            'university' => "ADD COLUMN university VARCHAR(50) NOT NULL DEFAULT 'aou'",
            'grading_system' => "ADD COLUMN grading_system VARCHAR(50) NOT NULL DEFAULT 'aou'",
            'semester_gpa' => "ADD COLUMN semester_gpa DECIMAL(3,2) NULL",
            'cumulative_gpa' => "ADD COLUMN cumulative_gpa DECIMAL(3,2) NULL",
            'total_hours' => "ADD COLUMN total_hours INT DEFAULT 0",
            'previous_gpa' => "ADD COLUMN previous_gpa DECIMAL(3,2) NULL",
            'previous_hours' => "ADD COLUMN previous_hours INT DEFAULT 0",
            'classification' => "ADD COLUMN classification VARCHAR(50) NULL",
            'ip_address' => "ADD COLUMN ip_address VARCHAR(45) NULL",
            'user_agent' => "ADD COLUMN user_agent TEXT NULL",
            'share_link' => "ADD COLUMN share_link VARCHAR(100) UNIQUE NULL",
            'link_expires_at' => "ADD COLUMN link_expires_at TIMESTAMP NULL",
            'is_verified' => "ADD COLUMN is_verified BOOLEAN DEFAULT FALSE",
            'notes' => "ADD COLUMN notes TEXT NULL",
            'updated_at' => "ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ];

        $addedColumns = 0;
        foreach ($columnsToAdd as $columnName => $alterCommand) {
            if (!in_array($columnName, $existingColumns)) {
                try {
                    $pdo->exec("ALTER TABLE students $alterCommand");
                    $addedColumns++;
                    $success[] = "تم إضافة العمود '$columnName' لجدول الطلاب";
                } catch (PDOException $e) {
                    $warnings[] = "تحذير في إضافة العمود '$columnName': " . $e->getMessage();
                }
            } else {
                $warnings[] = "العمود '$columnName' موجود مسبقاً في جدول الطلاب";
            }
        }

        // Handle gpa column rename if it exists
        if (in_array('gpa', $existingColumns) && !in_array('cumulative_gpa', $existingColumns)) {
            try {
                $pdo->exec("ALTER TABLE students CHANGE COLUMN gpa cumulative_gpa DECIMAL(3,2) NULL");
                $success[] = "تم تغيير اسم العمود 'gpa' إلى 'cumulative_gpa'";
            } catch (PDOException $e) {
                $warnings[] = "تحذير في تغيير اسم العمود gpa: " . $e->getMessage();
            }
        }

        // Add indexes
        $indexesToAdd = [
            'idx_university' => "ADD INDEX idx_university (university)",
            'idx_created_at' => "ADD INDEX idx_created_at (created_at)",
            'idx_share_link' => "ADD INDEX idx_share_link (share_link)",
            'idx_gpa' => "ADD INDEX idx_gpa (cumulative_gpa)"
        ];

        foreach ($indexesToAdd as $indexName => $indexCommand) {
            try {
                $pdo->exec("ALTER TABLE students $indexCommand");
                $success[] = "تم إضافة الفهرس '$indexName'";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate key') === false) {
                    $warnings[] = "تحذير في إضافة الفهرس '$indexName': " . $e->getMessage();
                }
            }
        }

            $success[] = "تم تحديث جدول الطلاب بنجاح - تم إضافة $addedColumns عمود جديد";
        }

    } catch (PDOException $e) {
        $errors[] = "خطأ في تحديث جدول الطلاب: " . $e->getMessage();
    }
    
    // Create other tables
    $tables = [
        'courses' => "
            CREATE TABLE IF NOT EXISTS courses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id INT NOT NULL,
                course_name VARCHAR(100) NOT NULL,
                course_code VARCHAR(20) NULL,
                credit_hours INT NOT NULL,
                grade VARCHAR(5) NOT NULL,
                grade_points DECIMAL(3,2) NOT NULL,
                semester VARCHAR(20) NULL,
                year VARCHAR(10) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
                INDEX idx_student_id (student_id),
                INDEX idx_grade (grade)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'activity_logs' => "
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NULL,
                student_id INT NULL,
                action VARCHAR(100) NOT NULL,
                description TEXT NULL,
                old_data JSON NULL,
                new_data JSON NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE SET NULL,
                FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE SET NULL,
                INDEX idx_action (action),
                INDEX idx_created_at (created_at),
                INDEX idx_admin_id (admin_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'settings' => "
            CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT NULL,
                setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
                description TEXT NULL,
                category VARCHAR(50) DEFAULT 'general',
                is_public BOOLEAN DEFAULT FALSE,
                updated_by INT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (updated_by) REFERENCES admins(id) ON DELETE SET NULL,
                INDEX idx_category (category)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];
    
    foreach ($tables as $tableName => $sql) {
        try {
            $pdo->exec($sql);
            $success[] = "تم إنشاء جدول '$tableName'";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') === false) {
                $errors[] = "خطأ في إنشاء جدول '$tableName': " . $e->getMessage();
            } else {
                $warnings[] = "جدول '$tableName' موجود مسبقاً";
            }
        }
    }
}

/**
 * Insert default data
 */
function insertDefaultData($pdo, &$success, &$errors, &$warnings) {
    try {
        // Insert default admins
        $adminData = [
            ['admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام الرئيسي', 'super_admin'],
            ['moderator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مشرف النظام', 'moderator'],
            ['viewer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مراقب النظام', 'admin']
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO admins (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)");
        $insertedAdmins = 0;
        foreach ($adminData as $admin) {
            if ($stmt->execute($admin)) {
                $insertedAdmins++;
            }
        }
        $success[] = "تم إدراج $insertedAdmins من المديرين الافتراضيين";
        
        // Insert default settings
        $settingsData = [
            ['site_name', 'حاسبة المعدل التراكمي - الجامعة العربية المفتوحة', 'text', 'اسم الموقع', 'general', 1],
            ['default_language', 'ar', 'text', 'اللغة الافتراضية', 'general', 1],
            ['default_university', 'aou', 'text', 'الجامعة الافتراضية', 'general', 1],
            ['max_courses_per_calculation', '20', 'number', 'الحد الأقصى للمواد في الحساب الواحد', 'limits', 1],
            ['link_expiry_days', '30', 'number', 'مدة انتهاء صلاحية الروابط بالأيام', 'security', 1],
            ['enable_registration', '1', 'boolean', 'تفعيل التسجيل للطلاب', 'features', 1],
            ['enable_sharing', '1', 'boolean', 'تفعيل مشاركة النتائج', 'features', 1],
            ['maintenance_mode', '0', 'boolean', 'وضع الصيانة', 'system', 0],
            ['max_login_attempts', '5', 'number', 'الحد الأقصى لمحاولات تسجيل الدخول', 'security', 0],
            ['lockout_duration', '30', 'number', 'مدة الحظر بالدقائق', 'security', 0],
            ['session_timeout', '120', 'number', 'انتهاء صلاحية الجلسة بالدقائق', 'security', 0]
        ];
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description, category, is_public) VALUES (?, ?, ?, ?, ?, ?)");
        $insertedSettings = 0;
        foreach ($settingsData as $setting) {
            if ($stmt->execute($setting)) {
                $insertedSettings++;
            }
        }
        $success[] = "تم إدراج $insertedSettings من الإعدادات الافتراضية";
        
        // Log the update activity
        $pdo->exec("INSERT INTO activity_logs (action, description, ip_address, created_at) VALUES 
                   ('system_update', 'تم تحديث قاعدة البيانات الموجودة وإضافة الجداول الجديدة', '" . ($_SERVER['REMOTE_ADDR'] ?? '127.0.0.1') . "', NOW())");
        
    } catch (PDOException $e) {
        $errors[] = "خطأ في إدراج البيانات الافتراضية: " . $e->getMessage();
    }
}

/**
 * Update config file
 */
function updateConfigFile(&$success, &$errors) {
    try {
        $configDir = __DIR__ . '/config';
        if (!is_dir($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        $configContent = "<?php
/**
 * Database Configuration - Updated
 * إعدادات قاعدة البيانات - محدثة
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'gpa_calculator'); // استخدام قاعدة البيانات الموجودة
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Include the main database class
require_once __DIR__ . '/database.php';
?>";
        
        file_put_contents($configDir . '/db_config.php', $configContent);
        $success[] = "تم تحديث ملف إعدادات قاعدة البيانات";
        
    } catch (Exception $e) {
        $errors[] = "خطأ في تحديث ملف الإعدادات: " . $e->getMessage();
    }
}

/**
 * Test updated system
 */
function testUpdatedSystem($pdo, &$success, &$errors) {
    try {
        // Test admin table
        $adminCount = $pdo->query("SELECT COUNT(*) FROM admins")->fetchColumn();
        $success[] = "تم العثور على $adminCount مدير في النظام";
        
        // Test students table
        $studentCount = $pdo->query("SELECT COUNT(*) FROM students")->fetchColumn();
        $success[] = "تم العثور على $studentCount طالب في النظام";
        
        // Test settings table
        $settingsCount = $pdo->query("SELECT COUNT(*) FROM settings")->fetchColumn();
        $success[] = "تم العثور على $settingsCount إعداد في النظام";
        
        // Test if auth system can be loaded
        if (file_exists(__DIR__ . '/config/database.php') && file_exists(__DIR__ . '/config/auth.php')) {
            $success[] = "ملفات نظام المصادقة متوفرة ويمكن تحميلها";
        }
        
    } catch (Exception $e) {
        $errors[] = "خطأ في اختبار النظام المحدث: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث قاعدة البيانات الموجودة - حاسبة المعدل التراكمي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .update-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
        }
        
        .success-item {
            animation: slideInRight 0.5s ease-out;
        }
        
        .error-item {
            animation: slideInLeft 0.5s ease-out;
        }
        
        .warning-item {
            animation: slideInUp 0.5s ease-out;
        }
        
        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <div class="update-card w-full max-w-6xl p-8 rounded-2xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-sync-alt text-white text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">تحديث قاعدة البيانات الموجودة</h1>
            <p class="text-gray-600">تحديث قاعدة البيانات gpa_calculator وإضافة الجداول الجديدة</p>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Success Messages -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-green-700 flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    العمليات الناجحة (<?php echo count($success); ?>)
                </h2>
                
                <div class="max-h-96 overflow-y-auto space-y-2">
                    <?php if (empty($success)): ?>
                        <div class="bg-gray-100 border border-gray-300 text-gray-600 px-4 py-3 rounded-lg">
                            لا توجد عمليات ناجحة
                        </div>
                    <?php else: ?>
                        <?php foreach ($success as $index => $message): ?>
                            <div class="success-item bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" 
                                 style="animation-delay: <?php echo $index * 0.1; ?>s">
                                <div class="flex items-center">
                                    <i class="fas fa-check text-green-600 ml-2"></i>
                                    <span class="text-sm"><?php echo htmlspecialchars($message); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Warning Messages -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-yellow-700 flex items-center">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    التحذيرات (<?php echo count($warnings); ?>)
                </h2>
                
                <div class="max-h-96 overflow-y-auto space-y-2">
                    <?php if (empty($warnings)): ?>
                        <div class="bg-gray-100 border border-gray-300 text-gray-600 px-4 py-3 rounded-lg">
                            لا توجد تحذيرات
                        </div>
                    <?php else: ?>
                        <?php foreach ($warnings as $index => $warning): ?>
                            <div class="warning-item bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg"
                                 style="animation-delay: <?php echo $index * 0.1; ?>s">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation text-yellow-600 ml-2"></i>
                                    <span class="text-sm"><?php echo htmlspecialchars($warning); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Error Messages -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-red-700 flex items-center">
                    <i class="fas fa-times-circle ml-2"></i>
                    الأخطاء (<?php echo count($errors); ?>)
                </h2>
                
                <div class="max-h-96 overflow-y-auto space-y-2">
                    <?php if (empty($errors)): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-thumbs-up ml-2"></i>
                                <span>لا توجد أخطاء! تم التحديث بنجاح</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($errors as $index => $error): ?>
                            <div class="error-item bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg"
                                 style="animation-delay: <?php echo $index * 0.1; ?>s">
                                <div class="flex items-center">
                                    <i class="fas fa-times text-red-600 ml-2"></i>
                                    <span class="text-sm"><?php echo htmlspecialchars($error); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Database Information -->
        <div class="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 class="text-lg font-semibold text-blue-800 mb-4">معلومات قاعدة البيانات المحدثة</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <span class="font-medium text-blue-700">الخادم:</span>
                    <span class="text-blue-600"><?php echo htmlspecialchars($host); ?></span>
                </div>
                <div>
                    <span class="font-medium text-blue-700">قاعدة البيانات:</span>
                    <span class="text-blue-600"><?php echo htmlspecialchars($database); ?></span>
                </div>
                <div>
                    <span class="font-medium text-blue-700">المستخدم:</span>
                    <span class="text-blue-600"><?php echo htmlspecialchars($username); ?></span>
                </div>
                <div>
                    <span class="font-medium text-blue-700">الترميز:</span>
                    <span class="text-blue-600">UTF-8</span>
                </div>
            </div>
        </div>

        <!-- Default Admin Credentials -->
        <div class="mt-6 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 class="text-lg font-semibold text-yellow-800 mb-4">بيانات المديرين الافتراضية</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div class="bg-white p-3 rounded border">
                    <div class="font-medium text-yellow-700">المدير الرئيسي</div>
                    <div class="text-yellow-600">المستخدم: admin</div>
                    <div class="text-yellow-600">كلمة المرور: admin123</div>
                </div>
                <div class="bg-white p-3 rounded border">
                    <div class="font-medium text-yellow-700">المشرف</div>
                    <div class="text-yellow-600">المستخدم: moderator</div>
                    <div class="text-yellow-600">كلمة المرور: admin123</div>
                </div>
                <div class="bg-white p-3 rounded border">
                    <div class="font-medium text-yellow-700">المراقب</div>
                    <div class="text-yellow-600">المستخدم: viewer</div>
                    <div class="text-yellow-600">كلمة المرور: admin123</div>
                </div>
            </div>
            <div class="mt-3 text-xs text-yellow-700">
                <i class="fas fa-exclamation-triangle ml-1"></i>
                يرجى تغيير كلمات المرور الافتراضية بعد تسجيل الدخول
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <?php if (empty($errors)): ?>
                <a href="admin_login.php" 
                   class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300 text-center">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل دخول الإدارة
                </a>
                
                <a href="admin_dashboard.php" 
                   class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-300 text-center">
                    <i class="fas fa-tachometer-alt ml-2"></i>
                    لوحة الإدارة
                </a>
            <?php else: ?>
                <button onclick="location.reload()" 
                        class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-300">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة المحاولة
                </button>
            <?php endif; ?>
            
            <a href="index.php" 
               class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-300 text-center">
                <i class="fas fa-home ml-2"></i>
                الصفحة الرئيسية
            </a>
        </div>

        <!-- Footer -->
        <div class="mt-8 pt-6 border-t border-gray-200 text-center">
            <p class="text-sm text-gray-500">
                © 2024 الجامعة العربية المفتوحة. جميع الحقوق محفوظة.
            </p>
            <div class="mt-2 text-xs text-gray-400">
                تم تحديث النظام لدعم لوحة الإدارة المتقدمة
            </div>
        </div>
    </div>

    <script>
        // Show success message if update completed successfully
        <?php if (empty($errors) && !empty($success)): ?>
        setTimeout(() => {
            alert('تم تحديث قاعدة البيانات بنجاح! يمكنك الآن استخدام لوحة الإدارة الجديدة.');
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
