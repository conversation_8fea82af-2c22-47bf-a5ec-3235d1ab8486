<?php
/**
 * Simple GPA Calculator - Works without Database
 * Fallback version for easy setup
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Simple configuration
$current_language = $_SESSION['language'] ?? 'ar';
$page_direction = $current_language === 'ar' ? 'rtl' : 'ltr';

// Handle language change
if (isset($_POST['action']) && $_POST['action'] === 'change_language') {
    $new_language = $_POST['language'] === 'ar' ? 'en' : 'ar';
    $_SESSION['language'] = $new_language;
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Grading systems
$grading_systems = [
    'aou' => [
        'name' => 'الجامعة العربية المفتوحة',
        'name_en' => 'Arab Open University',
        'grades' => [
            'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'ممتاز'],
            'B+' => ['points' => 3.5, 'range' => '85-89', 'description' => 'جيد جداً مرتفع'],
            'B' => ['points' => 3.0, 'range' => '80-84', 'description' => 'جيد جداً'],
            'C+' => ['points' => 2.5, 'range' => '75-79', 'description' => 'جيد مرتفع'],
            'C' => ['points' => 2.0, 'range' => '70-74', 'description' => 'جيد'],
            'D' => ['points' => 1.5, 'range' => '60-69', 'description' => 'مقبول'],
            'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'راسب']
        ]
    ],
    'standard' => [
        'name' => 'النظام الأمريكي القياسي',
        'name_en' => 'Standard American System',
        'grades' => [
            'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'Excellent'],
            'B' => ['points' => 3.0, 'range' => '80-89', 'description' => 'Good'],
            'C' => ['points' => 2.0, 'range' => '70-79', 'description' => 'Average'],
            'D' => ['points' => 1.0, 'range' => '60-69', 'description' => 'Below Average'],
            'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'Fail']
        ]
    ],
    'simple' => [
        'name' => 'النظام المبسط',
        'name_en' => 'Simple System',
        'grades' => [
            'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'ممتاز'],
            'B' => ['points' => 3.0, 'range' => '80-89', 'description' => 'جيد'],
            'C' => ['points' => 2.0, 'range' => '70-79', 'description' => 'مقبول'],
            'D' => ['points' => 1.0, 'range' => '60-69', 'description' => 'ضعيف'],
            'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'راسب']
        ]
    ]
];

$current_system = $_SESSION['grading_system'] ?? 'aou';
$current_grading_system = $grading_systems[$current_system];

// Handle AJAX requests
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'calculate_gpa':
            $courses = json_decode($_POST['courses'], true);
            $previousGPA = floatval($_POST['previous_gpa'] ?? 0);
            $previousHours = intval($_POST['previous_hours'] ?? 0);
            
            $totalPoints = 0;
            $totalHours = 0;
            
            foreach ($courses as $course) {
                $hours = intval($course['hours']);
                $grade = $course['grade'];
                
                if (isset($current_grading_system['grades'][$grade])) {
                    $points = $current_grading_system['grades'][$grade]['points'];
                    $totalPoints += $points * $hours;
                    $totalHours += $hours;
                }
            }
            
            $semesterGPA = $totalHours > 0 ? $totalPoints / $totalHours : 0;
            $cumulativeGPA = $semesterGPA;
            
            if ($previousHours > 0) {
                $totalCumulativePoints = $totalPoints + ($previousGPA * $previousHours);
                $totalCumulativeHours = $totalHours + $previousHours;
                $cumulativeGPA = $totalCumulativeHours > 0 ? $totalCumulativePoints / $totalCumulativeHours : 0;
            }
            
            // Get classification
            $classification = getGPAClassification($cumulativeGPA);
            
            echo json_encode([
                'semester_gpa' => round($semesterGPA, 2),
                'cumulative_gpa' => round($cumulativeGPA, 2),
                'total_hours' => $totalHours,
                'total_points' => round($totalPoints, 2),
                'classification' => $classification
            ]);
            break;
            
        case 'change_grading_system':
            $system = $_POST['system'];
            if (isset($grading_systems[$system])) {
                $_SESSION['grading_system'] = $system;
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['error' => 'نظام تقدير غير صحيح']);
            }
            break;
    }
    exit;
}

function getGPAClassification($gpa) {
    global $current_language;
    
    if ($gpa >= 3.75) {
        return [
            'name' => $current_language === 'ar' ? 'ممتاز' : 'Excellent',
            'class' => 'excellent'
        ];
    } elseif ($gpa >= 3.25) {
        return [
            'name' => $current_language === 'ar' ? 'جيد جداً مرتفع' : 'Very Good High',
            'class' => 'very-good-high'
        ];
    } elseif ($gpa >= 2.75) {
        return [
            'name' => $current_language === 'ar' ? 'جيد جداً' : 'Very Good',
            'class' => 'very-good'
        ];
    } elseif ($gpa >= 2.25) {
        return [
            'name' => $current_language === 'ar' ? 'جيد مرتفع' : 'Good High',
            'class' => 'good-high'
        ];
    } elseif ($gpa >= 2.0) {
        return [
            'name' => $current_language === 'ar' ? 'جيد' : 'Good',
            'class' => 'good'
        ];
    } elseif ($gpa >= 1.0) {
        return [
            'name' => $current_language === 'ar' ? 'مقبول' : 'Pass',
            'class' => 'pass'
        ];
    } else {
        return [
            'name' => $current_language === 'ar' ? 'راسب' : 'Fail',
            'class' => 'fail'
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo $page_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $current_language === 'ar' ? 'حاسبة المعدل التراكمي' : 'GPA Calculator'; ?> - <?php echo $current_language === 'ar' ? 'الجامعة العربية المفتوحة' : 'Arab Open University'; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .fade-in { animation: fadeIn 0.5s ease-in; }
        .slide-in { animation: slideIn 0.5s ease-out; }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .course-item {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .course-item:hover {
            border-color: #3B82F6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }
        
        .classification-excellent { background: linear-gradient(135deg, #10B981, #059669); }
        .classification-very-good-high { background: linear-gradient(135deg, #3B82F6, #1D4ED8); }
        .classification-very-good { background: linear-gradient(135deg, #6366F1, #4F46E5); }
        .classification-good-high { background: linear-gradient(135deg, #8B5CF6, #7C3AED); }
        .classification-good { background: linear-gradient(135deg, #F59E0B, #D97706); }
        .classification-pass { background: linear-gradient(135deg, #EF4444, #DC2626); }
        .classification-fail { background: linear-gradient(135deg, #6B7280, #4B5563); }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen <?php echo $page_direction; ?>">

    <!-- Top Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Title -->
                <div class="flex items-center gap-4">
                    <div class="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xl">
                        AOU
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800"><?php echo $current_language === 'ar' ? 'حاسبة المعدل التراكمي' : 'GPA Calculator'; ?></h1>
                        <p class="text-sm text-gray-600"><?php echo $current_language === 'ar' ? 'الجامعة العربية المفتوحة' : 'Arab Open University'; ?></p>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex items-center gap-4">
                    <form method="POST" class="inline">
                        <input type="hidden" name="action" value="change_language">
                        <input type="hidden" name="language" value="<?php echo $current_language; ?>">
                        <button type="submit" class="bg-blue-100 text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors">
                            <i class="fas fa-language <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <span><?php echo $current_language === 'ar' ? 'English' : 'العربية'; ?></span>
                        </button>
                    </form>
                    
                    <a href="setup.php" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-database <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'إعداد قاعدة البيانات' : 'Setup Database'; ?></span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        
        <!-- Alert for Simple Version -->
        <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-info-circle <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                <span>
                    <?php echo $current_language === 'ar' ? 
                        'هذه النسخة المبسطة تعمل بدون قاعدة بيانات. لاستخدام النسخة الكاملة مع جميع المميزات، يرجى إعداد قاعدة البيانات أولاً.' : 
                        'This is a simplified version that works without a database. To use the full version with all features, please set up the database first.'; ?>
                </span>
            </div>
        </div>

        <!-- University Info Section -->
        <div class="bg-white rounded-2xl shadow-xl p-8 mb-8 fade-in">
            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div>
                    <h2 class="text-3xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-university text-blue-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <?php echo $current_language === 'ar' ? 'نبذة عن الجامعة العربية المفتوحة' : 'About Arab Open University'; ?>
                    </h2>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        <?php echo $current_language === 'ar' ? 
                            'الجامعة العربية المفتوحة هي مؤسسة تعليمية رائدة تأسست عام 2002، وتقدم برامج أكاديمية متميزة في مختلف التخصصات. تتميز الجامعة بنظام التعليم المدمج الذي يجمع بين التعليم الإلكتروني والتعليم التقليدي.' : 
                            'Arab Open University is a leading educational institution established in 2002, offering distinguished academic programs in various specializations. The university is characterized by a blended learning system that combines e-learning and traditional education.'; ?>
                    </p>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <i class="fas fa-calendar text-blue-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'تأسست:' : 'Founded:'; ?></strong> 2002
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg">
                            <i class="fas fa-users text-green-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'الطلاب:' : 'Students:'; ?></strong> +40,000
                        </div>
                        <div class="bg-purple-50 p-3 rounded-lg">
                            <i class="fas fa-graduation-cap text-purple-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'البرامج:' : 'Programs:'; ?></strong> +50 <?php echo $current_language === 'ar' ? 'برنامج' : 'programs'; ?>
                        </div>
                        <div class="bg-yellow-50 p-3 rounded-lg">
                            <i class="fas fa-globe text-yellow-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'الفروع:' : 'Branches:'; ?></strong> 9 <?php echo $current_language === 'ar' ? 'دول عربية' : 'Arab countries'; ?>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="w-full h-64 bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl shadow-lg mb-4 flex items-center justify-center text-white">
                        <div class="text-center">
                            <div class="text-6xl mb-4">🏛️</div>
                            <h3 class="text-2xl font-bold"><?php echo $current_language === 'ar' ? 'الجامعة العربية المفتوحة' : 'Arab Open University'; ?></h3>
                            <p class="text-blue-100 mt-2"><?php echo $current_language === 'ar' ? 'السعودية' : 'Saudi Arabia'; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid lg:grid-cols-3 gap-8">

            <!-- Left Panel - GPA Calculator -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-2xl shadow-xl p-6 mb-8 slide-in">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                        <i class="fas fa-calculator text-blue-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'حاسبة المعدل التراكمي' : 'GPA Calculator'; ?></span>
                    </h2>

                    <!-- Grading System Selection -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <?php echo $current_language === 'ar' ? 'نظام التقدير' : 'Grading System'; ?>
                        </label>
                        <select id="gradingSystemSelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <?php foreach ($grading_systems as $key => $system): ?>
                                <option value="<?php echo $key; ?>" <?php echo $key === $current_system ? 'selected' : ''; ?>>
                                    <?php echo $current_language === 'ar' ? $system['name'] : $system['name_en']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Calculation Type -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <?php echo $current_language === 'ar' ? 'نوع الحساب' : 'Calculation Type'; ?>
                        </label>
                        <select id="calculationType" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="semester"><?php echo $current_language === 'ar' ? 'المعدل الفصلي' : 'Semester GPA'; ?></option>
                            <option value="cumulative"><?php echo $current_language === 'ar' ? 'المعدل التراكمي' : 'Cumulative GPA'; ?></option>
                        </select>
                    </div>

                    <!-- Previous GPA Section -->
                    <div id="previousGpaSection" class="hidden mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4"><?php echo $current_language === 'ar' ? 'المعلومات السابقة' : 'Previous Information'; ?></h3>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <?php echo $current_language === 'ar' ? 'المعدل التراكمي السابق' : 'Previous Cumulative GPA'; ?>
                                </label>
                                <input type="number" id="previousGpa" step="0.01" min="0" max="4"
                                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="0.00">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <?php echo $current_language === 'ar' ? 'الساعات المكتسبة السابقة' : 'Previous Credit Hours'; ?>
                                </label>
                                <input type="number" id="previousHours" min="0"
                                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="0">
                            </div>
                        </div>
                    </div>

                    <!-- Courses Section -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold"><?php echo $current_language === 'ar' ? 'المواد الدراسية' : 'Courses'; ?></h3>
                            <button id="addCourseBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-300 flex items-center">
                                <i class="fas fa-plus <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                                <span><?php echo $current_language === 'ar' ? 'إضافة مادة' : 'Add Course'; ?></span>
                            </button>
                        </div>

                        <div id="coursesContainer">
                            <!-- Courses will be added dynamically -->
                        </div>
                    </div>

                    <!-- Calculate Button -->
                    <button id="calculateBtn" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-calculator <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'احسب المعدل' : 'Calculate GPA'; ?></span>
                    </button>
                </div>

                <!-- Results Section -->
                <div id="resultsSection" class="bg-white rounded-2xl shadow-xl p-6 hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                        <i class="fas fa-chart-line text-green-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'النتائج' : 'Results'; ?></span>
                    </h2>

                    <div class="grid md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-graduation-cap text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2"><?php echo $current_language === 'ar' ? 'المعدل الفصلي' : 'Semester GPA'; ?></h3>
                            <p id="semesterGpaValue" class="text-3xl font-bold">0.00</p>
                        </div>

                        <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-trophy text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2"><?php echo $current_language === 'ar' ? 'المعدل التراكمي' : 'Cumulative GPA'; ?></h3>
                            <p id="cumulativeGpaValue" class="text-3xl font-bold">0.00</p>
                        </div>

                        <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-clock text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2"><?php echo $current_language === 'ar' ? 'مجموع الساعات' : 'Total Hours'; ?></h3>
                            <p id="totalHoursValue" class="text-3xl font-bold">0</p>
                        </div>
                    </div>

                    <!-- Classification Display -->
                    <div id="classificationDisplay" class="mb-6 p-6 rounded-xl text-center text-white">
                        <i class="fas fa-medal text-4xl mb-4"></i>
                        <h3 class="text-2xl font-bold mb-2" id="classificationText">-</h3>
                        <p class="text-sm opacity-90"><?php echo $current_language === 'ar' ? 'التقدير العام' : 'Overall Classification'; ?></p>
                    </div>
                </div>
            </div>

            <!-- Right Panel -->
            <div class="lg:col-span-1">
                <!-- Grade Scale -->
                <div class="bg-white rounded-2xl shadow-xl p-6 mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-chart-bar text-blue-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'سلم الدرجات' : 'Grade Scale'; ?></span>
                    </h2>

                    <div class="space-y-3" id="gradeScaleContainer">
                        <?php foreach ($current_grading_system['grades'] as $grade => $info): ?>
                            <div class="flex justify-between items-center p-3 <?php
                                echo $grade === 'A' ? 'bg-green-50 border border-green-200' :
                                    (in_array($grade, ['B+', 'B']) ? 'bg-blue-50 border border-blue-200' :
                                    (in_array($grade, ['C+', 'C']) ? 'bg-yellow-50 border border-yellow-200' :
                                    ($grade === 'D' ? 'bg-orange-50 border border-orange-200' : 'bg-red-50 border border-red-200')));
                            ?> rounded-lg">
                                <span class="font-bold text-lg <?php
                                    echo $grade === 'A' ? 'text-green-800' :
                                        (in_array($grade, ['B+', 'B']) ? 'text-blue-800' :
                                        (in_array($grade, ['C+', 'C']) ? 'text-yellow-800' :
                                        ($grade === 'D' ? 'text-orange-800' : 'text-red-800')));
                                ?>"><?php echo $grade; ?></span>
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-700"><?php echo $info['range']; ?></div>
                                    <div class="text-xs text-gray-500"><?php echo $info['description']; ?></div>
                                </div>
                                <span class="text-lg font-bold <?php
                                    echo $grade === 'A' ? 'text-green-700' :
                                        (in_array($grade, ['B+', 'B']) ? 'text-blue-700' :
                                        (in_array($grade, ['C+', 'C']) ? 'text-yellow-700' :
                                        ($grade === 'D' ? 'text-orange-700' : 'text-red-700')));
                                ?>"><?php echo $info['points']; ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Quick Tips -->
                <div class="bg-white rounded-2xl shadow-xl p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'نصائح سريعة' : 'Quick Tips'; ?></span>
                    </h2>

                    <div class="space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="font-semibold text-blue-800 mb-2">
                                <i class="fas fa-info-circle <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                                <?php echo $current_language === 'ar' ? 'كيفية الحساب' : 'How to Calculate'; ?>
                            </h4>
                            <p class="text-sm text-blue-700">
                                <?php echo $current_language === 'ar' ?
                                    'المعدل = مجموع (نقاط التقدير × عدد الساعات) ÷ إجمالي الساعات' :
                                    'GPA = Sum of (Grade Points × Credit Hours) ÷ Total Hours'; ?>
                            </p>
                        </div>

                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="font-semibold text-green-800 mb-2">
                                <i class="fas fa-target <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                                <?php echo $current_language === 'ar' ? 'للحصول على الامتياز' : 'For Excellence'; ?>
                            </h4>
                            <p class="text-sm text-green-700">
                                <?php echo $current_language === 'ar' ?
                                    'تحتاج معدل 3.75 أو أكثر للحصول على تقدير ممتاز' :
                                    'You need a GPA of 3.75 or higher for excellent grade'; ?>
                            </p>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h4 class="font-semibold text-yellow-800 mb-2">
                                <i class="fas fa-exclamation-triangle <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                                <?php echo $current_language === 'ar' ? 'تحذير' : 'Warning'; ?>
                            </h4>
                            <p class="text-sm text-yellow-700">
                                <?php echo $current_language === 'ar' ?
                                    'المعدل أقل من 2.0 يعتبر إنذار أكاديمي' :
                                    'GPA below 2.0 is considered academic warning'; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        class SimpleGPACalculator {
            constructor() {
                this.currentLanguage = '<?php echo $current_language; ?>';
                this.gradingSystem = <?php echo json_encode($current_grading_system); ?>;
                this.allGradingSystems = <?php echo json_encode($grading_systems); ?>;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.addInitialCourse();
            }

            setupEventListeners() {
                // Add course button
                document.getElementById('addCourseBtn').addEventListener('click', () => this.addCourse());

                // Calculate button
                document.getElementById('calculateBtn').addEventListener('click', () => this.calculateGPA());

                // Grading system selector
                document.getElementById('gradingSystemSelect').addEventListener('change', (e) => {
                    this.changeGradingSystem(e.target.value);
                });

                // Calculation type selector
                document.getElementById('calculationType').addEventListener('change', (e) => {
                    this.togglePreviousGpaSection(e.target.value);
                });
            }

            addCourse(courseData = null) {
                const container = document.getElementById('coursesContainer');
                const courseId = 'course_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

                const courseDiv = document.createElement('div');
                courseDiv.className = 'course-item bg-gray-50 rounded-lg p-4 border border-gray-200 mb-4';
                courseDiv.id = courseId;

                courseDiv.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                ${this.currentLanguage === 'ar' ? 'اسم المادة' : 'Course Name'}
                            </label>
                            <input type="text"
                                   class="course-name w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="${this.currentLanguage === 'ar' ? 'مثال: الرياضيات' : 'e.g., Mathematics'}"
                                   value="${courseData?.name || ''}">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                ${this.currentLanguage === 'ar' ? 'عدد الساعات' : 'Credit Hours'}
                            </label>
                            <input type="number"
                                   class="course-hours w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   min="1" max="6"
                                   placeholder="3"
                                   value="${courseData?.hours || ''}">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                ${this.currentLanguage === 'ar' ? 'التقدير' : 'Grade'}
                            </label>
                            <select class="course-grade w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">${this.currentLanguage === 'ar' ? 'اختر التقدير' : 'Select Grade'}</option>
                                ${this.getGradeOptions(courseData?.grade)}
                            </select>
                        </div>

                        <div class="flex justify-center">
                            <button type="button"
                                    class="remove-course bg-red-500 text-white p-2 rounded-lg hover:bg-red-600 transition-colors duration-300"
                                    onclick="gpaCalculator.removeCourse('${courseId}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;

                container.appendChild(courseDiv);

                // Add animation
                courseDiv.style.opacity = '0';
                courseDiv.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    courseDiv.style.transition = 'all 0.3s ease';
                    courseDiv.style.opacity = '1';
                    courseDiv.style.transform = 'translateY(0)';
                }, 10);
            }

            removeCourse(courseId) {
                const courseElement = document.getElementById(courseId);
                if (courseElement) {
                    courseElement.style.transition = 'all 0.3s ease';
                    courseElement.style.opacity = '0';
                    courseElement.style.transform = 'translateX(-100%)';

                    setTimeout(() => {
                        courseElement.remove();
                    }, 300);
                }
            }

            getGradeOptions(selectedGrade = '') {
                let options = '';
                if (this.gradingSystem && this.gradingSystem.grades) {
                    Object.keys(this.gradingSystem.grades).forEach(grade => {
                        const gradeInfo = this.gradingSystem.grades[grade];
                        const selected = grade === selectedGrade ? 'selected' : '';
                        options += `<option value="${grade}" ${selected}>${grade} (${gradeInfo.points}) - ${gradeInfo.description}</option>`;
                    });
                }
                return options;
            }

            collectCoursesData() {
                const coursesData = [];
                const courseElements = document.querySelectorAll('.course-item');

                courseElements.forEach(element => {
                    const name = element.querySelector('.course-name').value.trim();
                    const hours = parseInt(element.querySelector('.course-hours').value);
                    const grade = element.querySelector('.course-grade').value;

                    if (name && hours && grade) {
                        coursesData.push({ name, hours, grade });
                    }
                });

                return coursesData;
            }

            async calculateGPA() {
                try {
                    const coursesData = this.collectCoursesData();

                    if (coursesData.length === 0) {
                        this.showAlert(this.currentLanguage === 'ar' ? 'يرجى إضافة مادة واحدة على الأقل' : 'Please add at least one course', 'error');
                        return;
                    }

                    const calculationType = document.getElementById('calculationType').value;
                    const previousGpa = parseFloat(document.getElementById('previousGpa')?.value || 0);
                    const previousHours = parseInt(document.getElementById('previousHours')?.value || 0);

                    const response = await fetch('index_simple.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'calculate_gpa',
                            courses: JSON.stringify(coursesData),
                            previous_gpa: previousGpa,
                            previous_hours: previousHours
                        })
                    });

                    const result = await response.json();

                    if (result.error) {
                        throw new Error(result.error);
                    }

                    this.displayResults(result);

                } catch (error) {
                    console.error('Error calculating GPA:', error);
                    this.showAlert(error.message || 'حدث خطأ في حساب المعدل', 'error');
                }
            }

            displayResults(result) {
                const resultsSection = document.getElementById('resultsSection');

                // Update values with animation
                this.animateNumber(document.getElementById('semesterGpaValue'), result.semester_gpa);
                this.animateNumber(document.getElementById('cumulativeGpaValue'), result.cumulative_gpa);
                this.animateNumber(document.getElementById('totalHoursValue'), result.total_hours);

                // Update classification
                if (result.classification) {
                    document.getElementById('classificationText').textContent = result.classification.name;
                    document.getElementById('classificationDisplay').className = `mb-6 p-6 rounded-xl text-center text-white classification-${result.classification.class}`;
                }

                // Show results with animation
                resultsSection.classList.remove('hidden');
                resultsSection.classList.add('fade-in');
                resultsSection.scrollIntoView({ behavior: 'smooth' });
            }

            animateNumber(element, targetValue) {
                const startValue = 0;
                const duration = 1000;
                const startTime = performance.now();

                const animate = (currentTime) => {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    const currentValue = startValue + (targetValue - startValue) * progress;

                    if (element.id.includes('Gpa')) {
                        element.textContent = currentValue.toFixed(2);
                    } else {
                        element.textContent = Math.floor(currentValue);
                    }

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    }
                };

                requestAnimationFrame(animate);
            }

            async changeGradingSystem(system) {
                try {
                    const response = await fetch('index_simple.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'change_grading_system',
                            system: system
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        location.reload();
                    }

                } catch (error) {
                    console.error('Error changing grading system:', error);
                }
            }

            togglePreviousGpaSection(type) {
                const section = document.getElementById('previousGpaSection');
                if (section) {
                    if (type === 'cumulative') {
                        section.classList.remove('hidden');
                        section.classList.add('slide-in');
                    } else {
                        section.classList.add('hidden');
                    }
                }
            }

            addInitialCourse() {
                const container = document.getElementById('coursesContainer');
                if (container && container.children.length === 0) {
                    this.addCourse();
                }
            }

            showAlert(message, type = 'info') {
                const alert = document.createElement('div');
                alert.className = `fixed top-4 ${this.currentLanguage === 'ar' ? 'left-4' : 'right-4'} z-50 p-4 rounded-lg shadow-lg text-white max-w-sm ${
                    type === 'success' ? 'bg-green-500' :
                    type === 'error' ? 'bg-red-500' :
                    'bg-blue-500'
                }`;
                alert.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-triangle' :
                            'fa-info-circle'
                        }"></i>
                        <span>${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-auto text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                document.body.appendChild(alert);

                setTimeout(() => {
                    if (alert.parentElement) {
                        alert.remove();
                    }
                }, 5000);
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.gpaCalculator = new SimpleGPACalculator();
        });
    </script>
</body>
</html>
