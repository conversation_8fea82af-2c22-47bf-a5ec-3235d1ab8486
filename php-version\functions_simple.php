<?php
/**
 * Simple GPA Calculator Functions
 * Works without database
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Simple grading systems
$grading_systems = [
    'aou' => [
        'name' => 'الجامعة العربية المفتوحة',
        'name_en' => 'Arab Open University',
        'grades' => [
            'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'ممتاز'],
            'B+' => ['points' => 3.5, 'range' => '85-89', 'description' => 'جيد جداً مرتفع'],
            'B' => ['points' => 3.0, 'range' => '80-84', 'description' => 'جيد جداً'],
            'C+' => ['points' => 2.5, 'range' => '75-79', 'description' => 'جيد مرتفع'],
            'C' => ['points' => 2.0, 'range' => '70-74', 'description' => 'جيد'],
            'D' => ['points' => 1.5, 'range' => '60-69', 'description' => 'مقبول'],
            'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'راسب']
        ]
    ],
    'standard' => [
        'name' => 'النظام الأمريكي القياسي',
        'name_en' => 'Standard American System',
        'grades' => [
            'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'Excellent'],
            'B' => ['points' => 3.0, 'range' => '80-89', 'description' => 'Good'],
            'C' => ['points' => 2.0, 'range' => '70-79', 'description' => 'Average'],
            'D' => ['points' => 1.0, 'range' => '60-69', 'description' => 'Below Average'],
            'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'Fail']
        ]
    ],
    'simple' => [
        'name' => 'النظام المبسط',
        'name_en' => 'Simple System',
        'grades' => [
            'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'ممتاز'],
            'B' => ['points' => 3.0, 'range' => '80-89', 'description' => 'جيد'],
            'C' => ['points' => 2.0, 'range' => '70-79', 'description' => 'مقبول'],
            'D' => ['points' => 1.0, 'range' => '60-69', 'description' => 'ضعيف'],
            'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'راسب']
        ]
    ]
];

// Universities data
$universities = [
    'aou' => [
        'name' => 'الجامعة العربية المفتوحة',
        'name_en' => 'Arab Open University',
        'grading_system' => 'aou'
    ],
    'ksu' => [
        'name' => 'جامعة الملك سعود',
        'name_en' => 'King Saud University',
        'grading_system' => 'aou'
    ],
    'kau' => [
        'name' => 'جامعة الملك عبدالعزيز',
        'name_en' => 'King Abdulaziz University',
        'grading_system' => 'aou'
    ]
];

// Current settings
$current_language = $_SESSION['language'] ?? 'ar';
$current_university = $_SESSION['university'] ?? 'aou';

/**
 * Translation function
 */
function t($key, $default = '') {
    global $current_language;
    
    $translations = [
        'ar' => [
            'app_title' => 'حاسبة المعدل التراكمي',
            'university_name' => 'الجامعة العربية المفتوحة'
        ],
        'en' => [
            'app_title' => 'GPA Calculator',
            'university_name' => 'Arab Open University'
        ]
    ];
    
    return $translations[$current_language][$key] ?? $default;
}

/**
 * Get current language
 */
function getCurrentLanguage() {
    return $_SESSION['language'] ?? 'ar';
}

/**
 * Get current grading system
 */
function getCurrentGradingSystem() {
    global $grading_systems;
    $system = $_SESSION['grading_system'] ?? 'aou';
    return $grading_systems[$system] ?? $grading_systems['aou'];
}

/**
 * Get all grading systems
 */
function getAllGradingSystems() {
    global $grading_systems;
    return $grading_systems;
}

/**
 * Calculate GPA from courses
 */
function calculateGPA($courses) {
    $totalPoints = 0;
    $totalHours = 0;
    $gradingSystem = getCurrentGradingSystem();
    
    foreach ($courses as $course) {
        $hours = intval($course['hours'] ?? 0);
        $grade = $course['grade'] ?? '';
        
        if ($hours > 0 && isset($gradingSystem['grades'][$grade])) {
            $points = $gradingSystem['grades'][$grade]['points'];
            $totalPoints += $points * $hours;
            $totalHours += $hours;
        }
    }
    
    $gpa = $totalHours > 0 ? $totalPoints / $totalHours : 0;
    
    return [
        'gpa' => round($gpa, 2),
        'total_points' => round($totalPoints, 2),
        'total_hours' => $totalHours
    ];
}

/**
 * Get GPA classification
 */
function getGPAClassification($gpa) {
    $language = getCurrentLanguage();
    
    if ($gpa >= 3.75) {
        return [
            'name' => $language === 'ar' ? 'ممتاز' : 'Excellent',
            'class' => 'excellent'
        ];
    } elseif ($gpa >= 3.25) {
        return [
            'name' => $language === 'ar' ? 'جيد جداً مرتفع' : 'Very Good High',
            'class' => 'very-good-high'
        ];
    } elseif ($gpa >= 2.75) {
        return [
            'name' => $language === 'ar' ? 'جيد جداً' : 'Very Good',
            'class' => 'very-good'
        ];
    } elseif ($gpa >= 2.25) {
        return [
            'name' => $language === 'ar' ? 'جيد مرتفع' : 'Good High',
            'class' => 'good-high'
        ];
    } elseif ($gpa >= 2.0) {
        return [
            'name' => $language === 'ar' ? 'جيد' : 'Good',
            'class' => 'good'
        ];
    } elseif ($gpa >= 1.0) {
        return [
            'name' => $language === 'ar' ? 'مقبول' : 'Pass',
            'class' => 'pass'
        ];
    } else {
        return [
            'name' => $language === 'ar' ? 'راسب' : 'Fail',
            'class' => 'fail'
        ];
    }
}

/**
 * Save courses to session
 */
function saveCourses($courses) {
    $_SESSION['saved_courses'] = $courses;
    return true;
}

/**
 * Load courses from session
 */
function loadCourses() {
    return $_SESSION['saved_courses'] ?? [];
}

/**
 * Check if link expiry is enabled and if a link has expired
 */
function isLinkExpired($expiresAt) {
    if (empty($expiresAt)) {
        return false; // No expiry date means link never expires
    }

    return strtotime($expiresAt) < time();
}

/**
 * Get link expiry settings from database
 */
function getLinkExpirySettings() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('enable_link_expiry', 'link_expiry_days')");
        $stmt->execute();

        $settings = [];
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }

        return [
            'enabled' => ($settings['enable_link_expiry'] ?? '1') === '1',
            'days' => intval($settings['link_expiry_days'] ?? 30)
        ];
    } catch (PDOException $e) {
        // Fallback to default settings
        return [
            'enabled' => true,
            'days' => 30
        ];
    }
}

// Admin functions moved to index.php to avoid conflicts
?>
