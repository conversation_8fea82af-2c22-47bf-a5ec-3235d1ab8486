<?php
/**
 * Test System
 * اختبار النظام
 */

try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>اختبار نظام التقدير</h1>";
    
    // 1. Test grading systems
    echo "<h2>1. أنظمة التقدير:</h2>";
    $systems = $pdo->query("SELECT * FROM grading_systems ORDER BY name_ar")->fetchAll();
    
    if (empty($systems)) {
        echo "❌ لا توجد أنظمة تقدير<br>";
    } else {
        foreach ($systems as $system) {
            echo "✅ {$system['name_ar']} (ID: {$system['id']})<br>";
        }
    }
    
    // 2. Test grading scales
    echo "<h2>2. سلالم الدرجات:</h2>";
    $scales = $pdo->query("
        SELECT gs.*, gsy.name_ar as system_name 
        FROM grading_scales gs 
        LEFT JOIN grading_systems gsy ON gs.grading_system_id = gsy.id 
        ORDER BY gsy.name_ar, gs.points DESC
    ")->fetchAll();
    
    if (empty($scales)) {
        echo "❌ لا توجد سلالم درجات<br>";
    } else {
        $currentSystem = '';
        foreach ($scales as $scale) {
            if ($scale['system_name'] !== $currentSystem) {
                $currentSystem = $scale['system_name'];
                echo "<h3>{$currentSystem}:</h3>";
            }
            echo "- {$scale['grade']} = {$scale['points']} نقطة ({$scale['min_percentage']}-{$scale['max_percentage']}%) - {$scale['description_ar']}<br>";
        }
    }
    
    // 3. Test universities
    echo "<h2>3. الجامعات:</h2>";
    $universities = $pdo->query("SELECT * FROM universities WHERE is_active = 1 ORDER BY name_ar")->fetchAll();
    
    if (empty($universities)) {
        echo "❌ لا توجد جامعات<br>";
    } else {
        foreach ($universities as $uni) {
            echo "✅ {$uni['name_ar']} (ID: {$uni['id']}) - نظام التقدير: {$uni['grading_system']}<br>";
        }
    }
    
    // 4. Test university-grading system relationship
    echo "<h2>4. ربط الجامعات بأنظمة التقدير:</h2>";
    $relationships = $pdo->query("
        SELECT u.name_ar as university, gs.name_ar as grading_system, COUNT(gsc.id) as grades_count
        FROM universities u
        LEFT JOIN grading_systems gs ON u.grading_system = gs.id
        LEFT JOIN grading_scales gsc ON gs.id = gsc.grading_system_id
        WHERE u.is_active = 1
        GROUP BY u.id, gs.id
        ORDER BY u.name_ar
    ")->fetchAll();
    
    foreach ($relationships as $rel) {
        echo "🔗 {$rel['university']} ← {$rel['grading_system']} ({$rel['grades_count']} درجة)<br>";
    }
    
    // 5. Test API endpoint
    echo "<h2>5. اختبار API تغيير الجامعة:</h2>";
    echo "<button onclick=\"testAPI('aou')\">اختبار AOU</button> ";
    echo "<button onclick=\"testAPI('standard')\">اختبار Standard</button> ";
    echo "<button onclick=\"testAPI('simple')\">اختبار Simple</button><br>";
    echo "<div id='apiResult' style='margin-top: 10px; padding: 10px; background: #f0f0f0;'></div>";
    
    echo "<h2>✅ النظام يعمل بشكل صحيح!</h2>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>

<script>
async function testAPI(universityId) {
    const resultDiv = document.getElementById('apiResult');
    resultDiv.innerHTML = 'جاري الاختبار...';
    
    try {
        const response = await fetch('index.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'change_university',
                university: universityId
            })
        });
        
        const result = await response.json();
        
        if (result.success && result.grading_system) {
            let html = `
                <h4>✅ نجح تغيير الجامعة إلى: ${universityId}</h4>
                <p><strong>نظام التقدير:</strong> ${result.grading_system.name}</p>
                <p><strong>عدد الدرجات:</strong> ${Object.keys(result.grading_system.grades).length}</p>
                <p><strong>الدرجات:</strong> ${Object.keys(result.grading_system.grades).join(', ')}</p>
            `;
            resultDiv.innerHTML = html;
        } else {
            resultDiv.innerHTML = `<p style="color: red;">❌ فشل: ${result.error || 'خطأ غير معروف'}</p>`;
        }
        
    } catch (error) {
        resultDiv.innerHTML = `<p style="color: red;">❌ خطأ في الشبكة: ${error.message}</p>`;
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
button:hover { background: #005a8b; }
</style>

<br><br>
<a href="index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
    العودة للصفحة الرئيسية
</a>
<a href="smart_fix.php" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
    تشغيل الإصلاح الذكي
</a>
