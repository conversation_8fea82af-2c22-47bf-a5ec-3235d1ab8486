# 🎓 حاسبة المعدل التراكمي - الجامعة العربية المفتوحة
## GPA Calculator - Arab Open University

### 🚀 **الإصدار 2.0 - Enhanced Edition**

حاسبة معدل تراكمي متطورة ومحسنة للجامعة العربية المفتوحة مع دعم قاعدة البيانات وميزات متقدمة.

---

## 🎯 **الوصول السريع / Quick Access**

### 📱 **الاستخدام المباشر (بدون قاعدة بيانات)**
```
http://localhost/gpa/php-version/
```
- ✅ يعمل فوراً بدون إعداد
- ✅ جميع المميزات الأساسية
- ✅ 3 أنظمة تقدير مختلفة
- ✅ دعم 6 جامعات

### 🗄️ **النسخة الكاملة (مع قاعدة البيانات)**
```
http://localhost/gpa/php-version/setup.php
```
- 🔧 إعداد قاعدة البيانات تلقائياً
- 📊 إحصائيات متقدمة
- 🔗 نظام مشاركة الروابط
- 👥 لوحة إدارة شاملة

### 🏛️ **إدارة الجامعات**
```
http://localhost/gpa/php-version/universities.php
```
- 📋 عرض جميع الجامعات المدعومة
- ⚙️ أنظمة التقدير لكل جامعة
- 🔄 تبديل سهل بين الجامعات

### 👨‍💼 **لوحة الإدارة**
```
http://localhost/gpa/php-version/admin.php
```
- 📈 إحصائيات فورية
- 👥 إدارة بيانات الطلاب
- 🏛️ إدارة الجامعات وأنظمة التقدير
- 📊 تقارير مفصلة

**🔐 بيانات الدخول:** admin / admin123

---

## 🏛️ **الجامعات المدعومة / Supported Universities**

| الجامعة | University | نظام التقدير | الطلاب |
|---------|------------|-------------|--------|
| 🏛️ الجامعة العربية المفتوحة | Arab Open University | AOU | 40,000+ |
| 👑 جامعة الملك سعود | King Saud University | AOU | 65,000+ |
| 🕌 جامعة الملك عبدالعزيز | King Abdulaziz University | AOU | 80,000+ |
| 🇦🇪 جامعة الإمارات | UAE University | Standard | 14,000+ |
| 🏺 الجامعة الأمريكية بالقاهرة | American University in Cairo | Standard | 6,500+ |
| 🏛️ الجامعة الأردنية | University of Jordan | Simple | 47,000+ |

---

## ⚙️ **أنظمة التقدير / Grading Systems**

### 🎯 **نظام الجامعة العربية المفتوحة (AOU)**
- **A**: 4.0 (90-100) - ممتاز
- **B+**: 3.5 (85-89) - جيد جداً مرتفع
- **B**: 3.0 (80-84) - جيد جداً
- **C+**: 2.5 (75-79) - جيد مرتفع
- **C**: 2.0 (70-74) - جيد
- **D**: 1.5 (60-69) - مقبول
- **F**: 0.0 (0-59) - راسب

### 🇺🇸 **النظام الأمريكي القياسي (Standard)**
- **A**: 4.0 (90-100) - Excellent
- **B**: 3.0 (80-89) - Good
- **C**: 2.0 (70-79) - Average
- **D**: 1.0 (60-69) - Below Average
- **F**: 0.0 (0-59) - Fail

### 📊 **النظام المبسط (Simple)**
- **A**: 4.0 (90-100) - ممتاز
- **B**: 3.0 (80-89) - جيد
- **C**: 2.0 (70-79) - مقبول
- **D**: 1.0 (60-69) - ضعيف
- **F**: 0.0 (0-59) - راسب

---

## ✨ **المميزات الرئيسية / Key Features**

### 🎯 **الوظائف الأساسية**
- ✅ حساب المعدل الفصلي والتراكمي
- ✅ دعم أنظمة تقدير متعددة
- ✅ واجهة ثنائية اللغة (عربي/إنجليزي)
- ✅ تصميم متجاوب لجميع الأجهزة
- ✅ حفظ وتحميل البيانات

### 🗄️ **قاعدة البيانات المتقدمة**
- 📊 إحصائيات شاملة ومفصلة
- 🔗 نظام مشاركة الروابط الآمن
- 👥 إدارة بيانات الطلاب
- 📈 تقارير الأداء والتحليلات
- 🏛️ إدارة الجامعات وأنظمة التقدير

### 🎨 **واجهة المستخدم**
- 🎯 تصميم عصري وجذاب
- ⚡ أداء سريع ومحسن
- 📱 متوافق مع الهواتف الذكية
- 🌙 ألوان مريحة للعين
- 🔄 انيميشن سلس وتفاعلي

---

## 🛠️ **التقنيات المستخدمة / Technologies**

### **Backend**
- **PHP 7.4+** - لغة البرمجة الأساسية
- **MySQL 5.7+** - قاعدة البيانات
- **PDO** - واجهة قاعدة البيانات الآمنة

### **Frontend**
- **HTML5** - هيكل الصفحات
- **Tailwind CSS** - إطار عمل التصميم
- **JavaScript ES6+** - البرمجة التفاعلية
- **Font Awesome** - الأيقونات

### **Database Schema**
- **9 جداول رئيسية** للبيانات
- **Views محسنة** للاستعلامات
- **Indexes متقدمة** للأداء
- **Foreign Keys** للتكامل

---

## 🚀 **التثبيت والإعداد / Installation**

### **الطريقة الأولى: الاستخدام المباشر**
1. **افتح المتصفح:**
   ```
   http://localhost/gpa/php-version/
   ```
2. **ابدأ الاستخدام فوراً!**

### **الطريقة الثانية: مع قاعدة البيانات**
1. **تشغيل XAMPP**
2. **إنشاء قاعدة البيانات:**
   ```
   http://localhost/gpa/php-version/setup.php
   ```
3. **اتباع خطوات الإعداد التلقائي**
4. **الاستمتاع بجميع المميزات!**

---

## 📊 **لوحة الإدارة / Admin Panel**

### **الإحصائيات المتاحة**
- 📈 إجمالي المستخدمين والحسابات
- 📊 متوسط المعدلات التراكمية
- 🏛️ توزيع الطلاب على الجامعات
- 📅 إحصائيات يومية وشهرية
- 🎯 توزيع التقديرات والدرجات

### **إدارة البيانات**
- 👥 عرض وإدارة بيانات الطلاب
- 🏛️ إدارة الجامعات وأنظمة التقدير
- 📤 تصدير البيانات إلى CSV
- 🔗 إدارة الروابط المشاركة
- ⚙️ إعدادات النظام

---

## 🔐 **الأمان / Security**

### **ميزات الأمان المطبقة**
- 🛡️ تنظيف المدخلات (Input Sanitization)
- 🔒 حماية من حقن SQL
- 🚫 حماية من XSS
- 🔐 تشفير كلمات المرور
- 🔑 إدارة آمنة للجلسات

---

## 📱 **التوافق / Compatibility**

### **المتصفحات المدعومة**
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### **الأجهزة**
- 💻 أجهزة الكمبيوتر
- 📱 الهواتف الذكية
- 📟 الأجهزة اللوحية

---

## 🎯 **مثال عملي / Example**

### **حساب معدل فصلي**
```
المواد:
- الرياضيات: 3 ساعات، A (4.0)
- الفيزياء: 4 ساعات، B+ (3.5)
- الكيمياء: 3 ساعات، B (3.0)

الحساب:
= (3×4.0 + 4×3.5 + 3×3.0) ÷ 10
= 35 ÷ 10 = 3.50

النتيجة: معدل فصلي 3.50 (جيد جداً مرتفع)
```

---

## 📞 **الدعم والمساعدة / Support**

### **للمساعدة السريعة:**
- 📖 راجع هذا الدليل
- 🔧 تحقق من متطلبات النظام
- 🌐 تأكد من تشغيل XAMPP

### **المشاكل الشائعة:**
- **صفحة بيضاء:** تأكد من تشغيل Apache
- **خطأ قاعدة البيانات:** استخدم النسخة المبسطة
- **مشكلة الترميز:** تأكد من UTF-8

---

## 📄 **الترخيص / License**

هذا المشروع مرخص تحت رخصة MIT - مجاني للاستخدام التعليمي والشخصي.

---

**© 2024 الجامعة العربية المفتوحة - جميع الحقوق محفوظة**

**🎓 احسب معدلك بدقة، خطط لمستقبلك بثقة!**
