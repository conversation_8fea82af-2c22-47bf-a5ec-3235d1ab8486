<?php
/**
 * Admin Grading Scales Management
 * إدارة سلم الدرجات
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

// Redirect publishers to their own dashboard
if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'publisher') {
    header('Location: publisher_dashboard.php');
    exit;
}

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$currentUser = [
    'id' => $_SESSION['admin_id'],
    'username' => $_SESSION['admin_username'] ?? 'admin',
    'role' => $_SESSION['admin_role'] ?? 'admin',
    'full_name' => $_SESSION['admin_name'] ?? 'مدير النظام'
];

$success = '';
$error = '';

// Function to get default grades for a system
function getDefaultGrades($systemId) {
    switch ($systemId) {
        case 'aou':
            return [
                ['A', 4.00, 'ممتاز', 90, 100],
                ['B+', 3.50, 'جيد جداً مرتفع', 85, 89],
                ['B', 3.00, 'جيد جداً', 80, 84],
                ['C+', 2.50, 'جيد مرتفع', 75, 79],
                ['C', 2.00, 'جيد', 70, 74],
                ['D+', 1.50, 'مقبول مرتفع', 65, 69],
                ['D', 1.00, 'مقبول', 60, 64],
                ['F', 0.00, 'راسب', 0, 59]
            ];
        case 'standard':
            return [
                ['A+', 4.00, 'ممتاز مرتفع', 95, 100],
                ['A', 4.00, 'ممتاز', 90, 94],
                ['B+', 3.50, 'جيد جداً مرتفع', 85, 89],
                ['B', 3.00, 'جيد جداً', 80, 84],
                ['C+', 2.50, 'جيد مرتفع', 75, 79],
                ['C', 2.00, 'جيد', 70, 74],
                ['D+', 1.50, 'مقبول مرتفع', 65, 69],
                ['D', 1.00, 'مقبول', 60, 64],
                ['F', 0.00, 'راسب', 0, 59]
            ];
        case 'simple':
            return [
                ['A', 4.00, 'ممتاز', 90, 100],
                ['B', 3.00, 'جيد جداً', 80, 89],
                ['C', 2.00, 'جيد', 70, 79],
                ['D', 1.00, 'مقبول', 60, 69],
                ['F', 0.00, 'راسب', 0, 59]
            ];
        default:
            // Generic system
            return [
                ['A', 4.00, 'ممتاز', 90, 100],
                ['B', 3.00, 'جيد جداً', 80, 89],
                ['C', 2.00, 'جيد', 70, 79],
                ['D', 1.00, 'مقبول', 60, 69],
                ['F', 0.00, 'راسب', 0, 59]
            ];
    }
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_grade':
                    $gradingSystemId = trim($_POST['grading_system_id'] ?? '');
                    $grade = trim($_POST['grade'] ?? '');
                    $points = floatval($_POST['points'] ?? 0);
                    $minPercentage = floatval($_POST['min_percentage'] ?? 0);
                    $maxPercentage = floatval($_POST['max_percentage'] ?? 0);
                    $description = trim($_POST['description'] ?? '');

                    if (empty($gradingSystemId) || empty($grade)) {
                        $error = 'يرجى ملء جميع الحقول المطلوبة';
                    } else {
                        $stmt = $pdo->prepare("
                            INSERT INTO grading_scales (grading_system_id, grade, points, min_percentage, max_percentage, description_ar)
                            VALUES (?, ?, ?, ?, ?, ?)
                            ON DUPLICATE KEY UPDATE
                            points = VALUES(points),
                            min_percentage = VALUES(min_percentage),
                            max_percentage = VALUES(max_percentage),
                            description_ar = VALUES(description_ar)
                        ");
                        $stmt->execute([$gradingSystemId, $grade, $points, $minPercentage, $maxPercentage, $description]);

                        $success = 'تم حفظ التقدير بنجاح';
                    }
                    break;

                case 'edit_grade':
                    $gradeId = intval($_POST['grade_id'] ?? 0);
                    $gradingSystemId = trim($_POST['grading_system_id'] ?? '');
                    $grade = trim($_POST['grade'] ?? '');
                    $points = floatval($_POST['points'] ?? 0);
                    $minPercentage = floatval($_POST['min_percentage'] ?? 0);
                    $maxPercentage = floatval($_POST['max_percentage'] ?? 0);
                    $description = trim($_POST['description'] ?? '');

                    if ($gradeId <= 0 || empty($gradingSystemId) || empty($grade)) {
                        $error = 'يرجى ملء جميع الحقول المطلوبة';
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE grading_scales
                            SET grading_system_id = ?, grade = ?, points = ?, min_percentage = ?, max_percentage = ?, description_ar = ?
                            WHERE id = ?
                        ");
                        $stmt->execute([$gradingSystemId, $grade, $points, $minPercentage, $maxPercentage, $description, $gradeId]);

                        $success = 'تم تحديث التقدير بنجاح';
                    }
                    break;
                    
                case 'delete_grade':
                    $id = intval($_POST['grade_id'] ?? 0);

                    if ($id <= 0) {
                        $error = 'معرف التقدير غير صحيح';
                    } else {
                        $stmt = $pdo->prepare("DELETE FROM grading_scales WHERE id = ?");
                        $stmt->execute([$id]);

                        $success = 'تم حذف التقدير بنجاح';
                    }
                    break;

                case 'delete_system_grades':
                    $systemId = trim($_POST['system_id'] ?? '');

                    if (empty($systemId)) {
                        $error = 'معرف النظام غير صحيح';
                    } else {
                        $stmt = $pdo->prepare("DELETE FROM grading_scales WHERE grading_system_id = ?");
                        $stmt->execute([$systemId]);

                        $deletedCount = $stmt->rowCount();
                        $success = "تم حذف $deletedCount تقدير من النظام بنجاح";
                    }
                    break;

                case 'reset_system':
                    $systemId = trim($_POST['system_id'] ?? '');

                    if (empty($systemId)) {
                        $error = 'معرف النظام غير صحيح';
                    } else {
                        // Delete existing grades
                        $stmt = $pdo->prepare("DELETE FROM grading_scales WHERE grading_system_id = ?");
                        $stmt->execute([$systemId]);

                        // Add default grades based on system type
                        $defaultGrades = getDefaultGrades($systemId);

                        if (!empty($defaultGrades)) {
                            $stmt = $pdo->prepare("INSERT INTO grading_scales (grading_system_id, grade, points, description_ar, min_percentage, max_percentage) VALUES (?, ?, ?, ?, ?, ?)");
                            foreach ($defaultGrades as $grade) {
                                $stmt->execute([$systemId, $grade[0], $grade[1], $grade[2], $grade[3], $grade[4]]);
                            }
                            $success = "تم إعادة تعيين النظام بنجاح وإضافة " . count($defaultGrades) . " تقدير افتراضي";
                        } else {
                            $success = "تم حذف جميع التقديرات من النظام";
                        }
                    }
                    break;

                case 'add_defaults':
                    $systemId = trim($_GET['system'] ?? $_POST['system'] ?? '');

                    if (empty($systemId)) {
                        $error = 'معرف النظام غير صحيح';
                    } else {
                        // Check if system exists
                        $stmt = $pdo->prepare("SELECT name_ar FROM grading_systems WHERE id = ?");
                        $stmt->execute([$systemId]);
                        $system = $stmt->fetch();

                        if (!$system) {
                            $error = 'نظام التقدير غير موجود';
                        } else {
                            // Add default grades
                            $defaultGrades = getDefaultGrades($systemId);

                            if (!empty($defaultGrades)) {
                                $stmt = $pdo->prepare("INSERT INTO grading_scales (grading_system_id, grade, points, description_ar, min_percentage, max_percentage) VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE points = VALUES(points), description_ar = VALUES(description_ar), min_percentage = VALUES(min_percentage), max_percentage = VALUES(max_percentage)");
                                foreach ($defaultGrades as $grade) {
                                    $stmt->execute([$systemId, $grade[0], $grade[1], $grade[2], $grade[3], $grade[4]]);
                                }
                                $success = "تم إضافة " . count($defaultGrades) . " تقدير افتراضي لنظام {$system['name_ar']}";
                            } else {
                                $error = "لا توجد درجات افتراضية لهذا النظام";
                            }
                        }
                    }
                    break;
            }
        }
    }
    
    // Get all universities
    $universities = $pdo->query("SELECT id, name_ar FROM universities WHERE is_active = 1 ORDER BY name_ar")->fetchAll();

    // Get all grading systems
    $gradingSystems = $pdo->query("SELECT id, name_ar FROM grading_systems WHERE is_active = 1 ORDER BY name_ar")->fetchAll();

    // Get all grading scales with system names
    $gradingScales = $pdo->query("
        SELECT gs.*,
               gsy.name_ar as system_name
        FROM grading_scales gs
        LEFT JOIN grading_systems gsy ON gs.grading_system_id = gsy.id
        ORDER BY gsy.name_ar, gs.points DESC
    ")->fetchAll();
    
} catch (PDOException $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
    $gradingScales = [];
    $gradingSystems = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة سلم الدرجات - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إدارة سلم الدرجات</h1>
                        <p class="text-gray-600">إدارة وتعديل سلم الدرجات لكل جامعة</p>
                    </div>
                    
                    <div class="flex space-x-3 space-x-reverse">
                        <button onclick="showAddModal()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة تقدير جديد
                        </button>

                        <div class="relative">
                            <button onclick="toggleActionsMenu()" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-300">
                                <i class="fas fa-cog ml-2"></i>
                                إجراءات إضافية
                                <i class="fas fa-chevron-down mr-2"></i>
                            </button>

                            <div id="actionsMenu" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
                                <button onclick="exportData()" class="w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-t-lg">
                                    <i class="fas fa-download ml-2"></i>
                                    تصدير البيانات
                                </button>
                                <button onclick="importData()" class="w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-upload ml-2"></i>
                                    استيراد البيانات
                                </button>
                                <button onclick="showBulkActions()" class="w-full text-right px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-b-lg">
                                    <i class="fas fa-tasks ml-2"></i>
                                    عمليات جماعية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="mx-6 mt-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle ml-2"></i>
                        <span><?php echo htmlspecialchars($success); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mx-6 mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        <span><?php echo htmlspecialchars($error); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Grading Scales by University -->
            <div class="p-6">
                <?php
                // Group grading scales by grading system
                $groupedScales = [];
                foreach ($gradingScales as $scale) {
                    $groupedScales[$scale['grading_system_id']][] = $scale;
                }
                
                if (empty($groupedScales)): ?>
                    <div class="bg-white rounded-lg shadow-lg p-8 text-center">
                        <i class="fas fa-graduation-cap text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد أنظمة تقدير</h3>
                        <p class="text-gray-500 mb-4">ابدأ بإضافة أنظمة التقدير للجامعات</p>
                        <button onclick="showAddModal()" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة أول تقدير
                        </button>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($groupedScales as $systemId => $scales): ?>
                            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                                <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h3 class="text-lg font-bold">
                                                <?php echo htmlspecialchars($scales[0]['system_name'] ?? $systemId); ?>
                                            </h3>
                                            <p class="text-blue-100 text-sm">
                                                نظام التقدير - <?php echo strtoupper($systemId); ?>
                                            </p>
                                        </div>
                                        <div class="flex space-x-1 space-x-reverse">
                                            <button onclick="resetSystem('<?php echo $systemId; ?>', '<?php echo htmlspecialchars($scales[0]['system_name'] ?? $systemId); ?>')"
                                                    class="text-yellow-200 hover:text-yellow-100 p-1" title="إعادة تعيين">
                                                <i class="fas fa-redo text-sm"></i>
                                            </button>
                                            <button onclick="deleteSystemGrades('<?php echo $systemId; ?>', '<?php echo htmlspecialchars($scales[0]['system_name'] ?? $systemId); ?>')"
                                                    class="text-red-200 hover:text-red-100 p-1" title="حذف جميع التقديرات">
                                                <i class="fas fa-trash text-sm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="p-4">
                                    <div class="space-y-2 max-h-64 overflow-y-auto">
                                        <?php foreach ($scales as $scale): ?>
                                            <div class="flex items-center justify-between p-2 border rounded hover:bg-gray-50">
                                                <div class="flex items-center space-x-3 space-x-reverse">
                                                    <span class="font-bold text-lg text-blue-600"><?php echo htmlspecialchars($scale['grade']); ?></span>
                                                    <span class="font-medium"><?php echo $scale['points']; ?> نقطة</span>
                                                    <span class="text-xs text-gray-500">
                                                        (<?php echo $scale['min_percentage']; ?>-<?php echo $scale['max_percentage']; ?>)
                                                    </span>
                                                </div>
                                                <div class="flex items-center space-x-1 space-x-reverse">
                                                    <button onclick="editGrade(<?php echo htmlspecialchars(json_encode($scale)); ?>)"
                                                            class="text-blue-600 hover:text-blue-800 p-1" title="تعديل">
                                                        <i class="fas fa-edit text-sm"></i>
                                                    </button>
                                                    <button onclick="duplicateGrade(<?php echo htmlspecialchars(json_encode($scale)); ?>)"
                                                            class="text-green-600 hover:text-green-800 p-1" title="نسخ">
                                                        <i class="fas fa-copy text-sm"></i>
                                                    </button>
                                                    <button onclick="deleteGrade(<?php echo $scale['id']; ?>, '<?php echo htmlspecialchars($scale['grade']); ?>')"
                                                            class="text-red-600 hover:text-red-800 p-1" title="حذف">
                                                        <i class="fas fa-trash text-sm"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    
                                    <div class="mt-4 pt-4 border-t">
                                        <button onclick="addGradeForSystem('<?php echo $systemId; ?>')"
                                                class="w-full px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors">
                                            <i class="fas fa-plus ml-1"></i>
                                            إضافة تقدير لهذا النظام
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <!-- GPA Classification Table -->
                <div class="bg-white rounded-lg shadow-lg p-6 mt-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-800">
                            <i class="fas fa-chart-bar text-blue-600 ml-2"></i>
                            جدول تصنيف المعدل التراكمي (GPA Scale)
                        </h2>
                        <button onclick="editGPAScale()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-edit ml-2"></i>
                            تعديل التصنيف
                        </button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border border-gray-300 rounded-lg overflow-hidden">
                            <thead>
                                <tr class="bg-blue-600 text-white">
                                    <th class="border border-gray-300 px-6 py-4 text-center font-bold text-lg">GPA</th>
                                    <th class="border border-gray-300 px-6 py-4 text-center font-bold text-lg">Classification</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="bg-green-50 hover:bg-green-100 transition-colors">
                                    <td class="border border-gray-300 px-6 py-4 text-center font-semibold text-lg">4.00 – 3.67</td>
                                    <td class="border border-gray-300 px-6 py-4 text-center font-semibold text-lg text-green-700">Excellent</td>
                                </tr>
                                <tr class="bg-blue-50 hover:bg-blue-100 transition-colors">
                                    <td class="border border-gray-300 px-6 py-4 text-center font-semibold text-lg">3.66 – 3.00</td>
                                    <td class="border border-gray-300 px-6 py-4 text-center font-semibold text-lg text-blue-700">Very Good</td>
                                </tr>
                                <tr class="bg-yellow-50 hover:bg-yellow-100 transition-colors">
                                    <td class="border border-gray-300 px-6 py-4 text-center font-semibold text-lg">2.99 – 2.33</td>
                                    <td class="border border-gray-300 px-6 py-4 text-center font-semibold text-lg text-yellow-700">Good</td>
                                </tr>
                                <tr class="bg-orange-50 hover:bg-orange-100 transition-colors">
                                    <td class="border border-gray-300 px-6 py-4 text-center font-semibold text-lg">2.32 – 2.00</td>
                                    <td class="border border-gray-300 px-6 py-4 text-center font-semibold text-lg text-orange-700">Fair</td>
                                </tr>
                                <tr class="bg-red-50 hover:bg-red-100 transition-colors">
                                    <td class="border border-gray-300 px-6 py-4 text-center font-semibold text-lg">Below 2.00</td>
                                    <td class="border border-gray-300 px-6 py-4 text-center font-semibold text-lg text-red-700">Fail</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Arabic Translation -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-bold text-gray-800 mb-3">
                            <i class="fas fa-language text-green-600 ml-2"></i>
                            التصنيف باللغة العربية
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            <div class="text-center p-3 bg-green-100 rounded-lg border border-green-200">
                                <div class="font-bold text-green-800">4.00 – 3.67</div>
                                <div class="text-green-700 font-semibold">ممتاز</div>
                            </div>
                            <div class="text-center p-3 bg-blue-100 rounded-lg border border-blue-200">
                                <div class="font-bold text-blue-800">3.66 – 3.00</div>
                                <div class="text-blue-700 font-semibold">جيد جداً</div>
                            </div>
                            <div class="text-center p-3 bg-yellow-100 rounded-lg border border-yellow-200">
                                <div class="font-bold text-yellow-800">2.99 – 2.33</div>
                                <div class="text-yellow-700 font-semibold">جيد</div>
                            </div>
                            <div class="text-center p-3 bg-orange-100 rounded-lg border border-orange-200">
                                <div class="font-bold text-orange-800">2.32 – 2.00</div>
                                <div class="text-orange-700 font-semibold">مقبول</div>
                            </div>
                            <div class="text-center p-3 bg-red-100 rounded-lg border border-red-200">
                                <div class="font-bold text-red-800">Below 2.00</div>
                                <div class="text-red-700 font-semibold">راسب</div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <h4 class="font-bold text-blue-800 mb-2">
                            <i class="fas fa-info-circle ml-2"></i>
                            معلومات إضافية
                        </h4>
                        <ul class="text-blue-700 text-sm space-y-1">
                            <li>• يتم حساب المعدل التراكمي على أساس 4.00 نقاط</li>
                            <li>• التصنيف يعتمد على النظام الأمريكي المعتمد دولياً</li>
                            <li>• يمكن تخصيص هذا التصنيف حسب متطلبات كل جامعة</li>
                            <li>• المعدل أقل من 2.00 يعتبر غير مقبول أكاديمياً</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Grade Modal -->
    <div id="gradeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6 border-b">
                    <div class="flex items-center justify-between">
                        <h3 id="modalTitle" class="text-lg font-semibold text-gray-800">إضافة تقدير جديد</h3>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                
                <form id="gradeForm" method="POST" class="p-6">
                    <input type="hidden" name="action" id="formAction" value="add_grade">
                    <input type="hidden" name="grade_id" id="gradeId" value="">
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">نظام التقدير</label>
                            <select name="grading_system_id" id="gradingSystemId" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">اختر نظام التقدير</option>
                                <?php foreach ($gradingSystems as $system): ?>
                                    <option value="<?php echo htmlspecialchars($system['id']); ?>">
                                        <?php echo htmlspecialchars($system['name_ar']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">التقدير</label>
                                <input type="text" name="grade" id="grade" required maxlength="5"
                                       placeholder="مثل: A+"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">النقاط</label>
                                <input type="number" name="points" id="points" required step="0.01" min="0" max="4"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">أقل نسبة %</label>
                                <input type="number" name="min_percentage" id="minPercentage" step="0.01" min="0" max="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">أعلى نسبة %</label>
                                <input type="number" name="max_percentage" id="maxPercentage" step="0.01" min="0" max="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                            <input type="text" name="description" id="description" maxlength="100"
                                   placeholder="مثل: ممتاز"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3 space-x-reverse">
                        <button type="button" onclick="closeModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                            إلغاء
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-save ml-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة تقدير جديد';
            document.getElementById('formAction').value = 'add_grade';
            document.getElementById('gradeForm').reset();
            document.getElementById('gradeModal').classList.remove('hidden');
        }

        function addGradeForSystem(systemId) {
            showAddModal();
            document.getElementById('gradingSystemId').value = systemId;
        }

        function editGrade(grade) {
            console.log('Editing grade:', grade); // Debug log

            document.getElementById('modalTitle').textContent = 'تعديل التقدير';
            document.getElementById('formAction').value = 'edit_grade';
            document.getElementById('gradeId').value = grade.id || '';
            document.getElementById('gradingSystemId').value = grade.grading_system_id || '';
            document.getElementById('grade').value = grade.grade || '';
            document.getElementById('points').value = grade.points || '';
            document.getElementById('minPercentage').value = grade.min_percentage || '';
            document.getElementById('maxPercentage').value = grade.max_percentage || '';
            document.getElementById('description').value = grade.description_ar || '';

            // Ensure required fields have values
            if (!grade.grading_system_id) {
                console.error('Missing grading_system_id');
            }
            if (!grade.grade) {
                console.error('Missing grade');
            }
            if (!grade.points && grade.points !== 0) {
                console.error('Missing points');
            }

            document.getElementById('gradeModal').classList.remove('hidden');
        }

        function duplicateGrade(grade) {
            document.getElementById('modalTitle').textContent = 'نسخ التقدير';
            document.getElementById('formAction').value = 'add_grade';
            document.getElementById('gradeId').value = '';
            document.getElementById('gradingSystemId').value = grade.grading_system_id;
            document.getElementById('grade').value = grade.grade + '_copy';
            document.getElementById('points').value = grade.points;
            document.getElementById('minPercentage').value = grade.min_percentage;
            document.getElementById('maxPercentage').value = grade.max_percentage;
            document.getElementById('description').value = (grade.description_ar || '') + ' (نسخة)';
            document.getElementById('gradeModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('gradeModal').classList.add('hidden');
        }

        function deleteGrade(id, grade) {
            if (confirm(`هل أنت متأكد من حذف التقدير "${grade}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_grade">
                    <input type="hidden" name="grade_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function deleteSystemGrades(systemId, systemName) {
            if (confirm(`هل أنت متأكد من حذف جميع التقديرات من نظام "${systemName}"؟\n\nسيتم حذف جميع الدرجات المرتبطة بهذا النظام.\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_system_grades">
                    <input type="hidden" name="system_id" value="${systemId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function resetSystem(systemId, systemName) {
            if (confirm(`هل أنت متأكد من إعادة تعيين نظام "${systemName}"؟\n\nسيتم حذف جميع التقديرات الحالية وإضافة التقديرات الافتراضية.\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="reset_system">
                    <input type="hidden" name="system_id" value="${systemId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function toggleActionsMenu() {
            const menu = document.getElementById('actionsMenu');
            menu.classList.toggle('hidden');
        }

        function exportData() {
            const format = prompt('اختر تنسيق التصدير:\n\n1. JSON\n2. CSV\n3. Excel\n\nأدخل الرقم (1-3):', '1');

            let exportFormat = 'json';
            switch(format) {
                case '2':
                    exportFormat = 'csv';
                    break;
                case '3':
                    exportFormat = 'excel';
                    break;
                default:
                    exportFormat = 'json';
            }

            const exportUrl = `export_grading_scales.php?format=${exportFormat}`;
            window.open(exportUrl, '_blank');
            toggleActionsMenu();
        }

        function importData() {
            alert('ميزة الاستيراد قيد التطوير');
            toggleActionsMenu();
        }

        function showBulkActions() {
            alert('العمليات الجماعية قيد التطوير');
            toggleActionsMenu();
        }

        function editGPAScale() {
            alert('ميزة تعديل تصنيف المعدل قيد التطوير\n\nسيتم إضافة إمكانية تخصيص نطاقات التصنيف حسب متطلبات كل جامعة');
        }

        // Close modal when clicking outside
        document.getElementById('gradeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Close actions menu when clicking outside
        document.addEventListener('click', function(e) {
            const menu = document.getElementById('actionsMenu');
            const button = e.target.closest('button');

            if (!button || !button.onclick || button.onclick.toString().indexOf('toggleActionsMenu') === -1) {
                menu.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
