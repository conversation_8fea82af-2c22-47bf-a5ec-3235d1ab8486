<?php
require_once 'config_new.php';
require_once 'functions_new.php';
?>

<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo $page_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo t('app_title'); ?> - <?php echo t('university_name'); ?></title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo $current_language === 'ar' ? 'حاسبة المعدل التراكمي للجامعة العربية المفتوحة - احسب معدلك بدقة' : 'GPA Calculator for Arab Open University - Calculate your GPA accurately'; ?>">
    <meta name="keywords" content="GPA, Calculator, Arab Open University, معدل تراكمي, حاسبة">
    <meta name="author" content="Arab Open University">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo t('app_title'); ?>">
    <meta property="og:description" content="<?php echo $current_language === 'ar' ? 'حاسبة المعدل التراكمي للجامعة العربية المفتوحة' : 'GPA Calculator for Arab Open University'; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎓</text></svg>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        /* Enhanced styles for better performance */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        .slide-in {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .course-item {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .course-item:hover {
            border-color: #3B82F6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .course-item.error {
            border-color: #EF4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }

        .loading-spinner {
            display: none;
        }

        .loading-spinner.active {
            display: inline-block;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen <?php echo $page_direction; ?>">

    <!-- Top Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Title -->
                <div class="flex items-center gap-4">
                    <div class="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xl">
                        AOU
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800"><?php echo t('app_title'); ?></h1>
                        <p class="text-sm text-gray-600"><?php echo t('university_name'); ?></p>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex items-center gap-4">
                    <button id="languageToggle" class="bg-blue-100 text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors">
                        <i class="fas fa-language <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="langText"><?php echo $current_language === 'ar' ? 'English' : 'العربية'; ?></span>
                    </button>
                    <button id="shareBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-share-alt <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="shareText"><?php echo $current_language === 'ar' ? 'مشاركة المعدل' : 'Share GPA'; ?></span>
                    </button>
                    <a href="universities.php" class="bg-purple-100 text-purple-600 px-4 py-2 rounded-lg hover:bg-purple-200 transition-colors">
                        <i class="fas fa-university <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'الجامعات' : 'Universities'; ?></span>
                    </a>
                    <a href="admin.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-cog <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="adminText"><?php echo $current_language === 'ar' ? 'لوحة الإدارة' : 'Admin Panel'; ?></span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        
        <!-- University Info Section -->
        <div class="bg-white rounded-2xl shadow-xl p-8 mb-8 fade-in">
            <div class="grid md:grid-cols-2 gap-8 items-center">
                <div>
                    <h2 class="text-3xl font-bold text-gray-800 mb-4">
                        <i class="fas fa-university text-blue-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <?php echo $current_language === 'ar' ? 'نبذة عن ' . $selected_university['name'] : 'About ' . $selected_university['name_en']; ?>
                    </h2>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        <?php echo $current_language === 'ar' ? $selected_university['description'] : $selected_university['description_en']; ?>
                    </p>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <i class="fas fa-calendar text-blue-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'تأسست:' : 'Founded:'; ?></strong> <?php echo $selected_university['established']; ?>
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg">
                            <i class="fas fa-users text-green-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'الطلاب:' : 'Students:'; ?></strong> <?php echo $selected_university['students']; ?>
                        </div>
                        <div class="bg-purple-50 p-3 rounded-lg">
                            <i class="fas fa-graduation-cap text-purple-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'نظام التقدير:' : 'Grading System:'; ?></strong> <?php echo strtoupper($selected_university['grading_system']); ?>
                        </div>
                        <div class="bg-yellow-50 p-3 rounded-lg">
                            <i class="fas fa-globe text-yellow-600 <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <strong><?php echo $current_language === 'ar' ? 'الموقع:' : 'Country:'; ?></strong> <?php echo $current_language === 'ar' ? $selected_university['country'] : $selected_university['country_en']; ?>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <div class="w-full h-64 bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl shadow-lg mb-4 flex items-center justify-center text-white">
                        <div class="text-center">
                            <div class="text-6xl mb-4"><?php echo $selected_university['logo']; ?></div>
                            <h3 class="text-2xl font-bold"><?php echo $current_language === 'ar' ? $selected_university['name'] : $selected_university['name_en']; ?></h3>
                            <p class="text-blue-100 mt-2"><?php echo $current_language === 'ar' ? $selected_university['country'] : $selected_university['country_en']; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid lg:grid-cols-3 gap-8">

            <!-- Left Panel - GPA Calculator -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-2xl shadow-xl p-6 mb-8 slide-in">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                        <i class="fas fa-calculator text-blue-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <span id="calculatorTitle"><?php echo t('app_title'); ?></span>
                    </h2>

                    <!-- University and Grading System Selection -->
                    <div class="grid md:grid-cols-2 gap-4 mb-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo $current_language === 'ar' ? 'الجامعة' : 'University'; ?>
                            </label>
                            <select id="universitySelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php foreach ($universities as $id => $university): ?>
                                    <option value="<?php echo $id; ?>" <?php echo $id === $current_university ? 'selected' : ''; ?>>
                                        <?php echo $current_language === 'ar' ? $university['name'] : $university['name_en']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <?php echo $current_language === 'ar' ? 'نظام التقدير' : 'Grading System'; ?>
                            </label>
                            <select id="gradingSystemSelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php foreach ($all_grading_systems as $key => $system): ?>
                                    <option value="<?php echo $key; ?>" <?php echo $key === $_SESSION['grading_system'] ? 'selected' : ''; ?>>
                                        <?php echo $current_language === 'ar' ? $system['name'] : $system['name_en']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Calculation Type -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2" id="calcTypeLabel">
                            <?php echo $current_language === 'ar' ? 'نوع الحساب' : 'Calculation Type'; ?>
                        </label>
                        <select id="calculationType" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="semester"><?php echo $current_language === 'ar' ? 'المعدل الفصلي' : 'Semester GPA'; ?></option>
                            <option value="cumulative"><?php echo $current_language === 'ar' ? 'المعدل التراكمي' : 'Cumulative GPA'; ?></option>
                        </select>
                    </div>

                    <!-- Previous GPA Section -->
                    <div id="previousGpaSection" class="hidden mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4" id="prevInfoTitle"><?php echo $current_language === 'ar' ? 'المعلومات السابقة' : 'Previous Information'; ?></h3>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" id="prevGpaLabel">
                                    <?php echo $current_language === 'ar' ? 'المعدل التراكمي السابق' : 'Previous Cumulative GPA'; ?>
                                </label>
                                <input type="number" id="previousGpa" step="0.01" min="0" max="4"
                                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="0.00">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2" id="prevHoursLabel">
                                    <?php echo $current_language === 'ar' ? 'الساعات المكتسبة السابقة' : 'Previous Credit Hours'; ?>
                                </label>
                                <input type="number" id="previousHours" min="0"
                                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="0">
                            </div>
                        </div>
                    </div>

                    <!-- Courses Section -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold" id="coursesTitle"><?php echo $current_language === 'ar' ? 'المواد الدراسية' : 'Courses'; ?></h3>
                            <button id="addCourseBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-300 flex items-center">
                                <i class="fas fa-plus <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                                <span id="addCourseText"><?php echo $current_language === 'ar' ? 'إضافة مادة' : 'Add Course'; ?></span>
                            </button>
                        </div>

                        <div id="coursesContainer">
                            <!-- Courses will be added dynamically -->
                        </div>
                    </div>

                    <!-- Calculate Button -->
                    <button id="calculateBtn" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-calculator <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span id="calculateText"><?php echo $current_language === 'ar' ? 'احسب المعدل' : 'Calculate GPA'; ?></span>
                        <i class="fas fa-spinner loading-spinner <?php echo $current_language === 'ar' ? 'mr-2' : 'ml-2'; ?>"></i>
                    </button>
                </div>

                <!-- Results Section -->
                <div id="resultsSection" class="bg-white rounded-2xl shadow-xl p-6 hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                        <i class="fas fa-chart-line text-green-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                        <span id="resultsTitle"><?php echo $current_language === 'ar' ? 'النتائج' : 'Results'; ?></span>
                    </h2>

                    <div class="grid md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-graduation-cap text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2" id="semesterLabel"><?php echo $current_language === 'ar' ? 'المعدل الفصلي' : 'Semester GPA'; ?></h3>
                            <p id="semesterGpaValue" class="text-3xl font-bold">0.00</p>
                        </div>

                        <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-trophy text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2" id="cumulativeLabel"><?php echo $current_language === 'ar' ? 'المعدل التراكمي' : 'Cumulative GPA'; ?></h3>
                            <p id="cumulativeGpaValue" class="text-3xl font-bold">0.00</p>
                        </div>

                        <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-6 rounded-xl text-center">
                            <i class="fas fa-clock text-3xl mb-3"></i>
                            <h3 class="text-sm font-medium mb-2" id="hoursLabel"><?php echo $current_language === 'ar' ? 'مجموع الساعات' : 'Total Hours'; ?></h3>
                            <p id="totalHoursValue" class="text-3xl font-bold">0</p>
                        </div>
                    </div>

                    <!-- Classification Display -->
                    <div id="classificationDisplay" class="mb-6 p-6 rounded-xl text-center text-white">
                        <i class="fas fa-medal text-4xl mb-4"></i>
                        <h3 class="text-2xl font-bold mb-2" id="classificationText">-</h3>
                        <p class="text-sm opacity-90" id="classificationDesc"><?php echo $current_language === 'ar' ? 'التقدير العام' : 'Overall Classification'; ?></p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-wrap gap-4 justify-center">
                        <button id="saveDataBtn" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors duration-300 flex items-center">
                            <i class="fas fa-save <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <span id="saveText"><?php echo $current_language === 'ar' ? 'حفظ البيانات' : 'Save Data'; ?></span>
                        </button>

                        <button id="loadDataBtn" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors duration-300 flex items-center">
                            <i class="fas fa-upload <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <span id="loadText"><?php echo $current_language === 'ar' ? 'تحميل البيانات' : 'Load Data'; ?></span>
                        </button>

                        <button id="printBtn" class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-300 flex items-center">
                            <i class="fas fa-print <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                            <span><?php echo $current_language === 'ar' ? 'طباعة' : 'Print'; ?></span>
                        </button>
                    </div>
                </div>
            </div>
