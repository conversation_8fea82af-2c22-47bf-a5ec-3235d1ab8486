<?php
/**
 * GPA Calculator Configuration
 * Arab Open University System - Enhanced with MySQL
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database configuration
require_once __DIR__ . '/db_config.php';

// Application Settings
define('APP_NAME', 'حاسبة المعدل التراكمي');
define('APP_VERSION', '2.0.0');
define('APP_AUTHOR', 'Arab Open University');
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_GRADING_SYSTEM', 'aou');

// File Paths
define('DATA_DIR', __DIR__ . '/data/');
define('ASSETS_DIR', __DIR__ . '/assets/');

// Create data directory if it doesn't exist
if (!is_dir(DATA_DIR)) {
    mkdir(DATA_DIR, 0755, true);
}

// Grading Systems
$grading_systems = [
    'aou' => [
        'name' => 'الجامعة العربية المفتوحة',
        'name_en' => 'Arab Open University',
        'grades' => [
            'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'ممتاز'],
            'B+' => ['points' => 3.5, 'range' => '85-89', 'description' => 'جيد جداً مرتفع'],
            'B' => ['points' => 3.0, 'range' => '80-84', 'description' => 'جيد جداً'],
            'C+' => ['points' => 2.5, 'range' => '75-79', 'description' => 'جيد مرتفع'],
            'C' => ['points' => 2.0, 'range' => '70-74', 'description' => 'جيد'],
            'D' => ['points' => 1.5, 'range' => '60-69', 'description' => 'مقبول'],
            'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'راسب']
        ]
    ],
    'standard' => [
        'name' => 'النظام الأمريكي القياسي',
        'name_en' => 'Standard American System',
        'grades' => [
            'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'ممتاز'],
            'B' => ['points' => 3.0, 'range' => '80-89', 'description' => 'جيد جداً'],
            'C' => ['points' => 2.0, 'range' => '70-79', 'description' => 'جيد'],
            'D' => ['points' => 1.0, 'range' => '60-69', 'description' => 'مقبول'],
            'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'راسب']
        ]
    ],
    'simple' => [
        'name' => 'النظام المبسط',
        'name_en' => 'Simple System',
        'grades' => [
            'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'ممتاز'],
            'B' => ['points' => 3.0, 'range' => '80-89', 'description' => 'جيد'],
            'C' => ['points' => 2.0, 'range' => '70-79', 'description' => 'مقبول'],
            'D' => ['points' => 1.0, 'range' => '60-69', 'description' => 'ضعيف'],
            'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'راسب']
        ]
    ]
];

// Universities with their grading systems
$universities = [
    'aou' => [
        'name' => 'الجامعة العربية المفتوحة',
        'name_en' => 'Arab Open University',
        'logo' => '🏛️',
        'country' => 'السعودية',
        'country_en' => 'Saudi Arabia',
        'established' => 2002,
        'students' => '40,000+',
        'grading_system' => 'aou',
        'website' => 'https://www.aou.edu.sa',
        'description' => 'مؤسسة تعليمية رائدة تأسست عام 2002 وتقدم برامج أكاديمية متميزة',
        'description_en' => 'Leading educational institution established in 2002 offering distinguished academic programs'
    ],
    'ksu' => [
        'name' => 'جامعة الملك سعود',
        'name_en' => 'King Saud University',
        'logo' => '👑',
        'country' => 'السعودية',
        'country_en' => 'Saudi Arabia',
        'established' => 1957,
        'students' => '65,000+',
        'grading_system' => 'aou',
        'website' => 'https://www.ksu.edu.sa',
        'description' => 'أول جامعة سعودية تأسست عام 1957 وتعتبر من أعرق الجامعات',
        'description_en' => 'First Saudi university established in 1957, one of the most prestigious universities'
    ],
    'kau' => [
        'name' => 'جامعة الملك عبدالعزيز',
        'name_en' => 'King Abdulaziz University',
        'logo' => '🕌',
        'country' => 'السعودية',
        'country_en' => 'Saudi Arabia',
        'established' => 1967,
        'students' => '80,000+',
        'grading_system' => 'aou',
        'website' => 'https://www.kau.edu.sa',
        'description' => 'جامعة حكومية تأسست عام 1967 في جدة',
        'description_en' => 'Public university established in 1967 in Jeddah'
    ],
    'uae_university' => [
        'name' => 'جامعة الإمارات العربية المتحدة',
        'name_en' => 'United Arab Emirates University',
        'logo' => '🇦🇪',
        'country' => 'الإمارات',
        'country_en' => 'UAE',
        'established' => 1976,
        'students' => '14,000+',
        'grading_system' => 'standard',
        'website' => 'https://www.uaeu.ac.ae',
        'description' => 'أول جامعة وطنية في دولة الإمارات العربية المتحدة',
        'description_en' => 'First national university in the United Arab Emirates'
    ],
    'auc' => [
        'name' => 'الجامعة الأمريكية بالقاهرة',
        'name_en' => 'American University in Cairo',
        'logo' => '🏺',
        'country' => 'مصر',
        'country_en' => 'Egypt',
        'established' => 1919,
        'students' => '6,500+',
        'grading_system' => 'standard',
        'website' => 'https://www.aucegypt.edu',
        'description' => 'جامعة أمريكية خاصة في القاهرة تأسست عام 1919',
        'description_en' => 'Private American university in Cairo established in 1919'
    ],
    'ju' => [
        'name' => 'الجامعة الأردنية',
        'name_en' => 'University of Jordan',
        'logo' => '🏛️',
        'country' => 'الأردن',
        'country_en' => 'Jordan',
        'established' => 1962,
        'students' => '47,000+',
        'grading_system' => 'simple',
        'website' => 'https://www.ju.edu.jo',
        'description' => 'أول جامعة أردنية تأسست عام 1962',
        'description_en' => 'First Jordanian university established in 1962'
    ]
];

// Language Settings
$languages = [
    'ar' => [
        'name' => 'العربية',
        'direction' => 'rtl',
        'locale' => 'ar_SA'
    ],
    'en' => [
        'name' => 'English',
        'direction' => 'ltr',
        'locale' => 'en_US'
    ]
];

// Default Settings
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_GRADING_SYSTEM', 'aou');

// Create data directory if it doesn't exist
if (!file_exists(DATA_DIR)) {
    mkdir(DATA_DIR, 0755, true);
}

// Session Settings
ini_set('session.cookie_lifetime', 86400); // 24 hours
session_start();

// Set default language if not set
if (!isset($_SESSION['language'])) {
    $_SESSION['language'] = DEFAULT_LANGUAGE;
}

// Set default grading system if not set
if (!isset($_SESSION['grading_system'])) {
    $_SESSION['grading_system'] = DEFAULT_GRADING_SYSTEM;
}

// Set default university if not set
if (!isset($_SESSION['selected_university'])) {
    $_SESSION['selected_university'] = 'aou';
}

// Get current grading system
$current_grading_system = $all_grading_systems[$_SESSION['grading_system']] ?? $all_grading_systems['aou'];

// Make grading systems available globally
$grading_systems = $all_grading_systems;

// Get current university
$current_university = $_SESSION['selected_university'] ?? 'aou';
$selected_university = $universities[$current_university] ?? $universities['aou'];
?>
