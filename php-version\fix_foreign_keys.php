<?php
/**
 * Fix Foreign Key Constraints
 * إصلاح قيود المفاتيح الخارجية
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$fixes = [];
$errors = [];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $fixes[] = "✅ اتصال قاعدة البيانات ناجح";
    
    // Disable foreign key checks temporarily
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    $fixes[] = "✅ تم تعطيل فحص المفاتيح الخارجية مؤقتاً";
    
    // Drop existing foreign key constraints that cause problems
    $constraintsToRemove = [
        "ALTER TABLE settings DROP FOREIGN KEY settings_ibfk_1",
        "ALTER TABLE activity_logs DROP FOREIGN KEY activity_logs_ibfk_1",
        "ALTER TABLE activity_logs DROP FOREIGN KEY activity_logs_ibfk_2"
    ];
    
    foreach ($constraintsToRemove as $sql) {
        try {
            $pdo->exec($sql);
            $fixes[] = "✅ تم حذف قيد مفتاح خارجي";
        } catch (PDOException $e) {
            // Constraint might not exist, which is fine
            if (strpos($e->getMessage(), "check that column/key exists") === false) {
                $fixes[] = "ℹ️ قيد المفتاح الخارجي غير موجود (طبيعي)";
            }
        }
    }
    
    // Recreate settings table without foreign key constraints
    $pdo->exec("DROP TABLE IF EXISTS settings_backup");
    $pdo->exec("CREATE TABLE settings_backup AS SELECT * FROM settings");
    $fixes[] = "✅ تم إنشاء نسخة احتياطية من جدول الإعدادات";
    
    $pdo->exec("DROP TABLE settings");
    $pdo->exec("
        CREATE TABLE settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT NULL,
            setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
            description TEXT NULL,
            category VARCHAR(50) DEFAULT 'general',
            is_public BOOLEAN DEFAULT FALSE,
            updated_by INT NULL,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_category (category),
            INDEX idx_category_public (category, is_public)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $fixes[] = "✅ تم إعادة إنشاء جدول الإعدادات بدون قيود خارجية";
    
    // Restore data from backup
    $pdo->exec("INSERT INTO settings SELECT * FROM settings_backup");
    $fixes[] = "✅ تم استعادة بيانات الإعدادات";
    
    // Drop backup table
    $pdo->exec("DROP TABLE settings_backup");
    $fixes[] = "✅ تم حذف النسخة الاحتياطية";
    
    // Recreate activity_logs table without problematic foreign keys
    $pdo->exec("DROP TABLE IF EXISTS activity_logs_backup");
    $pdo->exec("CREATE TABLE activity_logs_backup AS SELECT * FROM activity_logs");
    $fixes[] = "✅ تم إنشاء نسخة احتياطية من جدول سجل الأنشطة";
    
    $pdo->exec("DROP TABLE activity_logs");
    $pdo->exec("
        CREATE TABLE activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NULL,
            student_id INT NULL,
            action VARCHAR(100) NOT NULL,
            description TEXT NULL,
            old_data JSON NULL,
            new_data JSON NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_action (action),
            INDEX idx_created_at (created_at),
            INDEX idx_admin_id (admin_id),
            INDEX idx_admin_action (admin_id, action),
            INDEX idx_student_id (student_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $fixes[] = "✅ تم إعادة إنشاء جدول سجل الأنشطة بدون قيود خارجية";
    
    // Restore activity logs data
    $pdo->exec("INSERT INTO activity_logs SELECT * FROM activity_logs_backup");
    $fixes[] = "✅ تم استعادة بيانات سجل الأنشطة";
    
    // Drop backup table
    $pdo->exec("DROP TABLE activity_logs_backup");
    $fixes[] = "✅ تم حذف النسخة الاحتياطية لسجل الأنشطة";
    
    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    $fixes[] = "✅ تم إعادة تفعيل فحص المفاتيح الخارجية";
    
    // Test settings insertion
    $testSettings = [
        'site_name' => 'حاسبة المعدل التراكمي - الجامعة العربية المفتوحة',
        'default_language' => 'ar',
        'default_university' => 'aou',
        'max_courses_per_calculation' => '20',
        'link_expiry_days' => '30',
        'enable_registration' => '1',
        'enable_sharing' => '1',
        'maintenance_mode' => '0',
        'max_login_attempts' => '5',
        'lockout_duration' => '30',
        'session_timeout' => '120'
    ];
    
    $insertedCount = 0;
    foreach ($testSettings as $key => $value) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO settings (setting_key, setting_value, updated_by) 
                VALUES (?, ?, NULL) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                updated_by = VALUES(updated_by)
            ");
            $stmt->execute([$key, $value]);
            $insertedCount++;
        } catch (PDOException $e) {
            $errors[] = "خطأ في إدراج الإعداد $key: " . $e->getMessage();
        }
    }
    $fixes[] = "✅ تم اختبار إدراج $insertedCount إعداد بنجاح";
    
    // Log this fix
    try {
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (action, description, ip_address, created_at) 
            VALUES ('foreign_key_fix', 'تم إصلاح قيود المفاتيح الخارجية', ?, NOW())
        ");
        $stmt->execute([$_SERVER['REMOTE_ADDR'] ?? '127.0.0.1']);
        $fixes[] = "✅ تم تسجيل عملية الإصلاح";
    } catch (Exception $e) {
        $errors[] = "تحذير: فشل في تسجيل عملية الإصلاح: " . $e->getMessage();
    }
    
} catch (PDOException $e) {
    $errors[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح قيود المفاتيح الخارجية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .fix-item {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-link text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">إصلاح قيود المفاتيح الخارجية</h1>
                <p class="text-gray-600">حل مشكلة Foreign Key Constraints في قاعدة البيانات</p>
            </div>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Fixes -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-green-700 mb-4 flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    الإصلاحات المكتملة (<?php echo count($fixes); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php foreach ($fixes as $index => $fix): ?>
                        <div class="fix-item bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" 
                             style="animation-delay: <?php echo $index * 0.1; ?>s">
                            <?php echo htmlspecialchars($fix); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Errors -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-red-700 mb-4 flex items-center">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    المشاكل المتبقية (<?php echo count($errors); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($errors)): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-thumbs-up ml-2"></i>
                                <span>تم إصلاح جميع المشاكل بنجاح!</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($errors as $index => $error): ?>
                            <div class="fix-item bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg"
                                 style="animation-delay: <?php echo $index * 0.1; ?>s">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- What was fixed -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">ما تم إصلاحه</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-gray-700 mb-2">جدول الإعدادات (settings)</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ إزالة قيد المفتاح الخارجي المشكل</li>
                        <li>✅ إعادة إنشاء الجدول بدون قيود</li>
                        <li>✅ استعادة جميع البيانات</li>
                        <li>✅ اختبار عمليات الإدراج والتحديث</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold text-gray-700 mb-2">جدول سجل الأنشطة (activity_logs)</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ إزالة قيود المفاتيح الخارجية المشكلة</li>
                        <li>✅ إعادة إنشاء الجدول بدون قيود</li>
                        <li>✅ استعادة جميع البيانات</li>
                        <li>✅ الحفاظ على الفهارس للأداء</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Test Settings Form -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">اختبار حفظ الإعدادات</h2>
            
            <form method="POST" action="test_settings.php" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم الموقع</label>
                    <input type="text" name="site_name" value="حاسبة المعدل التراكمي - محدث" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">اللغة الافتراضية</label>
                    <select name="default_language" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                    </select>
                </div>
                
                <div class="flex items-end">
                    <button type="submit" name="test_save" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-save ml-2"></i>
                        اختبار الحفظ
                    </button>
                </div>
            </form>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
            <?php if (empty($errors)): ?>
                <a href="admin_settings.php" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center">
                    <i class="fas fa-cog ml-2"></i>
                    اختبار صفحة الإعدادات
                </a>
                
                <a href="test_settings.php" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center">
                    <i class="fas fa-test-tube ml-2"></i>
                    اختبار حفظ الإعدادات
                </a>
                
                <a href="simple_admin_login.php" class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل دخول الإدارة
                </a>
            <?php else: ?>
                <button onclick="location.reload()" class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة المحاولة
                </button>
            <?php endif; ?>
            
            <a href="index.php" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-center">
                <i class="fas fa-home ml-2"></i>
                الصفحة الرئيسية
            </a>
        </div>

        <!-- Footer -->
        <div class="mt-8 text-center text-sm text-gray-500">
            <p>تم إصلاح قيود المفاتيح الخارجية في: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p class="mt-1">الآن يمكن حفظ الإعدادات بدون مشاكل!</p>
        </div>
    </div>

    <script>
        // Show success message
        <?php if (empty($errors)): ?>
        setTimeout(() => {
            alert('تم إصلاح مشكلة قيود المفاتيح الخارجية بنجاح!\nيمكنك الآن استخدام صفحة الإعدادات بدون مشاكل.');
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
