# 🎓 حاسبة المعدل التراكمي - النسخة المبسطة
## Simple GPA Calculator - Works Without Database

### 🚀 **الإصدار 2.0.0 - Simple Edition**

نسخة مبسطة من حاسبة المعدل التراكمي تعمل بدون قاعدة بيانات، مثالية للاستخدام السريع والمباشر.

---

## ✨ **المميزات / Features**

### 🎯 **الوظائف الأساسية**
- ✅ **حساب المعدل الفصلي** - Semester GPA calculation
- ✅ **حساب المعدل التراكمي** - Cumulative GPA calculation
- ✅ **أنظمة تقدير متعددة** - Multiple grading systems
- ✅ **واجهة ثنائية اللغة** - Bilingual interface (Arabic/English)
- ✅ **تصميم متجاوب** - Responsive design

### 🏛️ **أنظمة التقدير المدعومة**
1. **نظام الجامعة العربية المفتوحة (AOU)**
   - A: 4.0 (90-100) - ممتاز
   - B+: 3.5 (85-89) - جيد جداً مرتفع
   - B: 3.0 (80-84) - جيد جداً
   - C+: 2.5 (75-79) - جيد مرتفع
   - C: 2.0 (70-74) - جيد
   - D: 1.5 (60-69) - مقبول
   - F: 0.0 (0-59) - راسب

2. **النظام الأمريكي القياسي (Standard)**
   - A: 4.0 (90-100) - Excellent
   - B: 3.0 (80-89) - Good
   - C: 2.0 (70-79) - Average
   - D: 1.0 (60-69) - Below Average
   - F: 0.0 (0-59) - Fail

3. **النظام المبسط (Simple)**
   - A: 4.0 (90-100) - ممتاز
   - B: 3.0 (80-89) - جيد
   - C: 2.0 (70-79) - مقبول
   - D: 1.0 (60-69) - ضعيف
   - F: 0.0 (0-59) - راسب

---

## 🛠️ **التقنيات المستخدمة / Technologies**

### **Frontend**
- **HTML5** - هيكل الصفحة
- **Tailwind CSS** - التصميم والتنسيق
- **JavaScript ES6+** - التفاعل والحسابات
- **Font Awesome** - الأيقونات

### **Backend**
- **PHP 7.4+** - معالجة الطلبات
- **Session Management** - إدارة الجلسات
- **JSON** - تبادل البيانات

---

## 📋 **متطلبات التشغيل / Requirements**

### **الحد الأدنى**
- **PHP 7.4** أو أحدث
- **Apache/Nginx** خادم ويب
- **متصفح حديث** يدعم JavaScript

### **لا يتطلب**
- ❌ قاعدة بيانات MySQL
- ❌ إعدادات معقدة
- ❌ مكتبات إضافية

---

## 🚀 **التثبيت والاستخدام / Installation & Usage**

### **التثبيت السريع**

1. **تحميل الملفات**
```bash
# نسخ الملف إلى مجلد الخادم
cp index_simple.php /path/to/your/webserver/
```

2. **الوصول المباشر**
```
http://localhost/index_simple.php
```

3. **البدء في الاستخدام فوراً!**

### **الاستخدام**

#### **للطلاب:**
1. **اختر نظام التقدير** المناسب لجامعتك
2. **حدد نوع الحساب** (فصلي أو تراكمي)
3. **أضف المواد الدراسية** مع الدرجات والساعات
4. **اضغط احسب المعدل** لرؤية النتائج
5. **راجع التقدير العام** والتصنيف

#### **للمعدل التراكمي:**
1. اختر "المعدل التراكمي" من القائمة
2. أدخل المعدل التراكمي السابق
3. أدخل عدد الساعات المكتسبة السابقة
4. أضف مواد الفصل الحالي
5. احسب المعدل الجديد

---

## 🎯 **مثال عملي / Practical Example**

### **حساب معدل فصلي**
```
المواد الدراسية:
- الرياضيات: 3 ساعات، تقدير A (4.0)
- الفيزياء: 4 ساعات، تقدير B+ (3.5)
- الكيمياء: 3 ساعات، تقدير B (3.0)

الحساب:
= (3×4.0 + 4×3.5 + 3×3.0) ÷ (3+4+3)
= (12 + 14 + 9) ÷ 10
= 35 ÷ 10 = 3.50

النتيجة: معدل فصلي 3.50 (جيد جداً مرتفع)
```

### **حساب معدل تراكمي**
```
المعلومات السابقة:
- المعدل التراكمي السابق: 3.20
- الساعات المكتسبة السابقة: 60 ساعة

الفصل الحالي:
- مجموع نقاط الفصل: 35
- ساعات الفصل: 10

الحساب:
= (3.20×60 + 35) ÷ (60+10)
= (192 + 35) ÷ 70
= 227 ÷ 70 = 3.24

النتيجة: معدل تراكمي جديد 3.24
```

---

## 🎨 **واجهة المستخدم / User Interface**

### **الألوان والتصميم**
- **الأزرق** - العناصر الأساسية والتنقل
- **الأخضر** - النتائج الإيجابية والنجاح
- **الأحمر** - التحذيرات والأخطاء
- **الرمادي** - النصوص والخلفيات

### **التفاعل**
- **انيميشن سلس** عند إضافة/حذف المواد
- **تأثيرات hover** على الأزرار والعناصر
- **تحديث فوري** للنتائج
- **رسائل تنبيه** واضحة

---

## 📱 **التوافق / Compatibility**

### **المتصفحات المدعومة**
- ✅ **Chrome 80+**
- ✅ **Firefox 75+**
- ✅ **Safari 13+**
- ✅ **Edge 80+**
- ✅ **Opera 70+**

### **الأجهزة**
- ✅ **أجهزة الكمبيوتر** - تجربة كاملة
- ✅ **الأجهزة اللوحية** - واجهة متكيفة
- ✅ **الهواتف الذكية** - تصميم متجاوب
- ✅ **الشاشات الكبيرة** - استغلال أمثل للمساحة

---

## 🔧 **التخصيص / Customization**

### **إضافة نظام تقدير جديد**
```php
// في ملف index_simple.php
$grading_systems['new_system'] = [
    'name' => 'النظام الجديد',
    'name_en' => 'New System',
    'grades' => [
        'A+' => ['points' => 4.0, 'range' => '95-100', 'description' => 'ممتاز مرتفع'],
        'A' => ['points' => 3.7, 'range' => '90-94', 'description' => 'ممتاز'],
        // ... باقي الدرجات
    ]
];
```

### **تغيير الألوان**
```css
/* في قسم الـ CSS */
.classification-excellent { 
    background: linear-gradient(135deg, #your-color1, #your-color2); 
}
```

---

## 🐛 **استكشاف الأخطاء / Troubleshooting**

### **مشاكل شائعة**

#### **الصفحة لا تظهر**
```
المشكلة: صفحة بيضاء فارغة
الحل: تأكد من تشغيل خادم PHP
```

#### **الحسابات غير صحيحة**
```
المشكلة: نتائج خاطئة
الحل: تأكد من إدخال الدرجات والساعات بشكل صحيح
```

#### **مشكلة في اللغة**
```
المشكلة: النصوص لا تظهر بالعربية
الحل: تأكد من دعم UTF-8 في الخادم
```

---

## 🔄 **الترقية للنسخة الكاملة / Upgrade to Full Version**

### **للحصول على المميزات الإضافية:**
1. **إعداد قاعدة البيانات** MySQL
2. **تشغيل setup.php** لإعداد النظام
3. **استخدام index_new.php** للنسخة الكاملة

### **المميزات الإضافية في النسخة الكاملة:**
- 🗄️ **حفظ دائم للبيانات**
- 📊 **إحصائيات متقدمة**
- 🔗 **نظام مشاركة الروابط**
- 👥 **لوحة إدارة شاملة**
- 🏛️ **إدارة جامعات متعددة**
- 📈 **تقارير مفصلة**

---

## 📞 **الدعم والمساعدة / Support**

### **للمساعدة السريعة:**
- **المشاكل التقنية**: تحقق من متطلبات PHP
- **أسئلة الاستخدام**: راجع الأمثلة العملية
- **طلب مميزات**: فكر في الترقية للنسخة الكاملة

### **نصائح للاستخدام الأمثل:**
1. **أدخل البيانات بدقة** لضمان صحة النتائج
2. **احفظ نسخة من النتائج** قبل إغلاق المتصفح
3. **استخدم نظام التقدير الصحيح** لجامعتك
4. **راجع التقدير العام** لفهم مستواك الأكاديمي

---

## 📄 **الترخيص / License**

هذا المشروع مرخص تحت رخصة MIT - مجاني للاستخدام الشخصي والتعليمي.

---

## 🙏 **شكر وتقدير / Acknowledgments**

- **الجامعة العربية المفتوحة** - للإلهام والدعم
- **مجتمع PHP** - للأدوات والموارد
- **Tailwind CSS** - لإطار العمل الرائع
- **Font Awesome** - للأيقونات الجميلة

---

**© 2024 الجامعة العربية المفتوحة - النسخة المبسطة**

**🎓 استخدم بسهولة، احسب بدقة، انجح بتميز!**
