<?php
/**
 * <PERSON><PERSON>t to clean up test data for production deployment
 * Run this script once before deploying to production
 */

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to database successfully\n";
} catch (PDOException $e) {
    die("❌ Connection failed: " . $e->getMessage() . "\n");
}

echo "\n🧹 Starting cleanup process...\n\n";

try {
    // 1. Clear test student data
    echo "1. Clearing test student data...\n";
    $stmt = $pdo->query("DELETE FROM students WHERE name LIKE '%test%' OR name LIKE '%تجريب%' OR phone LIKE '0500000%'");
    $deletedStudents = $stmt->rowCount();
    echo "   ✅ Deleted {$deletedStudents} test student records\n";

    // 2. Clear test courses
    echo "2. Clearing test course data...\n";
    $stmt = $pdo->query("DELETE FROM courses WHERE course_name LIKE '%test%' OR course_name LIKE '%تجريب%'");
    $deletedCourses = $stmt->rowCount();
    echo "   ✅ Deleted {$deletedCourses} test course records\n";

    // 3. Clear test GPA calculations
    echo "3. Clearing test GPA calculations...\n";
    $stmt = $pdo->query("DELETE FROM gpa_calculations WHERE student_id NOT IN (SELECT id FROM students)");
    $deletedCalculations = $stmt->rowCount();
    echo "   ✅ Deleted {$deletedCalculations} orphaned GPA calculation records\n";

    // 4. Remove test admin accounts (keep only the main admin)
    echo "4. Cleaning up admin accounts...\n";
    $stmt = $pdo->query("DELETE FROM admin_users WHERE username LIKE '%test%' OR username = 'demo'");
    $deletedAdmins = $stmt->rowCount();
    echo "   ✅ Deleted {$deletedAdmins} test admin accounts\n";

    // 5. Clear auto-fill data from forms (remove default values)
    echo "5. Removing auto-fill data...\n";
    
    // Update admin settings to remove test data
    $stmt = $pdo->prepare("UPDATE settings SET setting_value = '' WHERE setting_key IN ('default_student_name', 'default_phone')");
    $stmt->execute();
    echo "   ✅ Cleared auto-fill settings\n";

    // 6. Reset notification settings to secure defaults
    echo "6. Resetting notification settings...\n";
    $stmt = $pdo->prepare("UPDATE settings SET setting_value = '0' WHERE setting_key = 'show_test_notifications'");
    $stmt->execute();
    echo "   ✅ Disabled test notifications\n";

    // 7. Clear session data and temporary files
    echo "7. Clearing temporary data...\n";
    
    // Clear any test session data
    if (file_exists('data/test_sessions.json')) {
        unlink('data/test_sessions.json');
        echo "   ✅ Removed test session file\n";
    }

    // Clear any test course data files
    if (file_exists('data/test_courses.json')) {
        unlink('data/test_courses.json');
        echo "   ✅ Removed test course file\n";
    }

    // 8. Update security settings
    echo "8. Updating security settings...\n";
    
    // Disable debug mode
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('debug_mode', '0') ON DUPLICATE KEY UPDATE setting_value = '0'");
    $stmt->execute();
    
    // Enable production mode
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('production_mode', '1') ON DUPLICATE KEY UPDATE setting_value = '1'");
    $stmt->execute();
    
    // Set secure session timeout
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('session_timeout', '3600') ON DUPLICATE KEY UPDATE setting_value = '3600'");
    $stmt->execute();
    
    echo "   ✅ Updated security settings for production\n";

    // 9. Optimize database tables
    echo "9. Optimizing database tables...\n";
    $tables = ['students', 'courses', 'gpa_calculations', 'universities', 'grading_systems', 'grading_scales', 'admin_users', 'settings'];
    
    foreach ($tables as $table) {
        try {
            $pdo->exec("OPTIMIZE TABLE {$table}");
            echo "   ✅ Optimized table: {$table}\n";
        } catch (PDOException $e) {
            echo "   ⚠️  Could not optimize table {$table}: " . $e->getMessage() . "\n";
        }
    }

    // 10. Generate production summary
    echo "\n📊 Production Deployment Summary:\n";
    echo "=====================================\n";
    
    // Count remaining data
    $studentCount = $pdo->query("SELECT COUNT(*) FROM students")->fetchColumn();
    $universityCount = $pdo->query("SELECT COUNT(*) FROM universities WHERE id != 'disabled'")->fetchColumn();
    $gradingSystemCount = $pdo->query("SELECT COUNT(*) FROM grading_systems WHERE is_active = 1")->fetchColumn();
    $adminCount = $pdo->query("SELECT COUNT(*) FROM admin_users")->fetchColumn();
    
    echo "📚 Universities: {$universityCount}\n";
    echo "📊 Grading Systems: {$gradingSystemCount}\n";
    echo "👥 Students: {$studentCount}\n";
    echo "🔐 Admin Users: {$adminCount}\n";
    
    // Security checklist
    echo "\n🔒 Security Checklist:\n";
    echo "=====================\n";
    echo "✅ Test data removed\n";
    echo "✅ Auto-fill disabled\n";
    echo "✅ Debug mode disabled\n";
    echo "✅ Production mode enabled\n";
    echo "✅ Session timeout configured\n";
    echo "✅ Database optimized\n";
    
    echo "\n🚀 System is ready for production deployment!\n";
    echo "\n⚠️  IMPORTANT REMINDERS:\n";
    echo "========================\n";
    echo "1. Change default admin password\n";
    echo "2. Update database credentials in config.php\n";
    echo "3. Enable HTTPS in production\n";
    echo "4. Set up regular database backups\n";
    echo "5. Configure proper file permissions\n";
    echo "6. Test all functionality before going live\n";
    
} catch (PDOException $e) {
    echo "❌ Error during cleanup: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✨ Cleanup completed successfully!\n";
?>
