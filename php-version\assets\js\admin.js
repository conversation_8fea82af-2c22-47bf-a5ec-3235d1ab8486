/**
 * Admin Panel JavaScript
 * Enhanced with Charts and Statistics
 */

class AdminPanel {
    constructor() {
        this.charts = {};
        this.data = {
            stats: {},
            students: []
        };
        this.init();
    }

    init() {
        // Check if we're on login page or dashboard
        if (document.getElementById('loginForm')) {
            this.setupLogin();
        } else {
            this.setupDashboard();
        }
    }

    setupLogin() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }
    }

    setupDashboard() {
        this.loadStatistics();
        this.loadStudentsData();
        this.setupRefreshInterval();
    }

    async handleLogin() {
        const password = document.getElementById('adminPassword').value;
        
        try {
            const response = await fetch('admin.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'admin_login',
                    password: password
                })
            });

            const result = await response.json();
            
            if (result.success) {
                location.reload();
            } else {
                this.showAlert(result.error || 'خطأ في تسجيل الدخول', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('حدث خطأ في الاتصال', 'error');
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch('admin.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'get_stats'
                })
            });

            const stats = await response.json();
            
            if (stats.error) {
                console.error('Stats error:', stats.error);
                return;
            }

            this.data.stats = stats;
            this.updateStatCards(stats);
            this.createCharts(stats);
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    async loadStudentsData() {
        try {
            const response = await fetch('admin.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'get_students'
                })
            });

            const students = await response.json();
            
            if (students.error) {
                console.error('Students error:', students.error);
                return;
            }

            this.data.students = students;
            this.updateStudentsTable(students);
        } catch (error) {
            console.error('Error loading students data:', error);
        }
    }

    updateStatCards(stats) {
        // Update stat cards with animation
        this.animateNumber('totalUsers', stats.total_users || 0);
        this.animateNumber('avgGPA', (stats.avg_gpa || 0).toFixed(2));
        this.animateNumber('todayCalculations', stats.today_calculations || 0);
        this.animateNumber('totalCalculations', stats.total_calculations || 0);
    }

    animateNumber(elementId, targetValue) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const startValue = 0;
        const duration = 1000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = startValue + (targetValue - startValue) * progress;
            
            if (elementId === 'avgGPA') {
                element.textContent = currentValue.toFixed(2);
            } else {
                element.textContent = Math.floor(currentValue);
            }

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    createCharts(stats) {
        this.createGradeChart(stats.grade_distribution || {});
        this.createUniversityChart(stats.university_distribution || {});
    }

    createGradeChart(gradeData) {
        const ctx = document.getElementById('gradeChart');
        if (!ctx) return;

        // Destroy existing chart
        if (this.charts.gradeChart) {
            this.charts.gradeChart.destroy();
        }

        const labels = Object.keys(gradeData);
        const data = Object.values(gradeData);
        const colors = [
            '#10B981', // Green for excellent
            '#3B82F6', // Blue for very good
            '#8B5CF6', // Purple for good
            '#F59E0B', // Yellow for average
            '#EF4444', // Red for poor
            '#6B7280', // Gray for fail
            '#EC4899'  // Pink for others
        ];

        this.charts.gradeChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 1000
                }
            }
        });
    }

    createUniversityChart(universityData) {
        const ctx = document.getElementById('universityChart');
        if (!ctx) return;

        // Destroy existing chart
        if (this.charts.universityChart) {
            this.charts.universityChart.destroy();
        }

        const labels = Object.keys(universityData);
        const data = Object.values(universityData);
        const colors = [
            '#3B82F6', // Blue
            '#10B981', // Green
            '#F59E0B', // Yellow
            '#EF4444', // Red
            '#8B5CF6', // Purple
            '#06B6D4', // Cyan
            '#EC4899'  // Pink
        ];

        this.charts.universityChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'عدد الطلاب',
                    data: data,
                    backgroundColor: colors.slice(0, labels.length),
                    borderColor: colors.slice(0, labels.length),
                    borderWidth: 1,
                    borderRadius: 8,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return `جامعة: ${context[0].label}`;
                            },
                            label: function(context) {
                                return `عدد الطلاب: ${context.parsed.y}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    updateStudentsTable(students) {
        const tbody = document.getElementById('studentsTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (students.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                        <i class="fas fa-inbox text-4xl mb-4"></i>
                        <p>لا توجد بيانات طلاب متاحة</p>
                    </td>
                </tr>
            `;
            return;
        }

        students.forEach((student, index) => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50 transition-colors';
            
            const classificationColor = this.getClassificationColor(student.classification);
            
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    ${student.name}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${student.phone}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <span class="font-bold text-lg">${student.gpa.toFixed(2)}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${classificationColor}">
                        ${student.classification}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${student.university.toUpperCase()}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${new Date(student.date).toLocaleDateString('ar-SA')}
                </td>
            `;
            
            tbody.appendChild(row);
            
            // Add animation
            row.style.opacity = '0';
            row.style.transform = 'translateY(20px)';
            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, index * 50);
        });
    }

    getClassificationColor(classification) {
        const colors = {
            'ممتاز': 'bg-green-100 text-green-800',
            'جيد جداً مرتفع': 'bg-blue-100 text-blue-800',
            'جيد جداً': 'bg-blue-100 text-blue-800',
            'جيد مرتفع': 'bg-yellow-100 text-yellow-800',
            'جيد': 'bg-yellow-100 text-yellow-800',
            'مقبول': 'bg-orange-100 text-orange-800',
            'راسب': 'bg-red-100 text-red-800'
        };
        
        return colors[classification] || 'bg-gray-100 text-gray-800';
    }

    setupRefreshInterval() {
        // Refresh data every 30 seconds
        setInterval(() => {
            this.loadStatistics();
            this.loadStudentsData();
        }, 30000);
    }

    showAlert(message, type = 'info') {
        const alert = document.createElement('div');
        alert.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            'bg-blue-500'
        }`;
        alert.innerHTML = `
            <div class="flex items-center gap-2">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' : 
                    type === 'error' ? 'fa-exclamation-triangle' : 
                    'fa-info-circle'
                }"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
}

// Global functions
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        fetch('admin.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'logout'
            })
        }).then(() => {
            location.reload();
        });
    }
}

function exportData() {
    if (window.adminPanel && window.adminPanel.data.students) {
        const students = window.adminPanel.data.students;
        const csv = convertToCSV(students);
        downloadCSV(csv, 'students_data.csv');
    }
}

function convertToCSV(data) {
    const headers = ['الاسم', 'الهاتف', 'المعدل', 'التقدير', 'الجامعة', 'التاريخ'];
    const csvContent = [
        headers.join(','),
        ...data.map(row => [
            row.name,
            row.phone,
            row.gpa.toFixed(2),
            row.classification,
            row.university,
            row.date
        ].join(','))
    ].join('\n');
    
    return csvContent;
}

function downloadCSV(csv, filename) {
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new AdminPanel();
});
