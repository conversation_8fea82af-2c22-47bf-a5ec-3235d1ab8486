<?php
/**
 * Admin Students Management
 * إدارة الطلاب
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

// Redirect publishers to their own dashboard
if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'publisher') {
    header('Location: publisher_dashboard.php');
    exit;
}

require_once 'db_config.php';

$currentUser = [
    'id' => $_SESSION['admin_id'],
    'username' => $_SESSION['admin_username'] ?? 'admin',
    'role' => $_SESSION['admin_role'] ?? 'admin',
    'full_name' => $_SESSION['admin_name'] ?? 'مدير النظام'
];

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'get_students':
            $page = intval($_POST['page'] ?? 1);
            $limit = intval($_POST['limit'] ?? 20);
            $search = trim($_POST['search'] ?? '');
            $university = trim($_POST['university'] ?? '');
            
            $offset = ($page - 1) * $limit;
            
            $whereClause = "WHERE 1=1";
            $params = [];
            
            if (!empty($search)) {
                $whereClause .= " AND (name LIKE ? OR phone LIKE ? OR student_id LIKE ?)";
                $searchTerm = "%$search%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if (!empty($university)) {
                $whereClause .= " AND university = ?";
                $params[] = $university;
            }
            
            // Get total count
            $totalQuery = "SELECT COUNT(*) as total FROM students $whereClause";
            $totalResult = fetchOne($totalQuery, $params);
            $total = $totalResult['total'];

            // Get students
            $studentsQuery = "
                SELECT id, name, phone, email, university, cumulative_gpa, total_hours,
                       classification, created_at, share_link, link_views, link_expires_at, is_verified
                FROM students
                $whereClause
                ORDER BY created_at DESC
                LIMIT $limit OFFSET $offset
            ";

            $students = fetchAll($studentsQuery, $params);
            
            echo json_encode([
                'success' => true,
                'students' => $students,
                'total' => $total,
                'page' => $page,
                'totalPages' => ceil($total / $limit)
            ]);
            exit;
            
        case 'delete_student':
            $studentId = intval($_POST['student_id'] ?? 0);
            
            if ($studentId <= 0) {
                echo json_encode(['success' => false, 'message' => 'معرف الطالب غير صحيح']);
                exit;
            }
            
            try {
                // Get student info for logging
                $student = fetchOne("SELECT name FROM students WHERE id = ?", [$studentId]);

                if (!$student) {
                    echo json_encode(['success' => false, 'message' => 'الطالب غير موجود']);
                    exit;
                }

                // Delete student (courses will be deleted automatically due to foreign key)
                executeQuery("DELETE FROM students WHERE id = ?", [$studentId]);
                
                echo json_encode(['success' => true, 'message' => 'تم حذف الطالب بنجاح']);
                
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'خطأ في حذف الطالب: ' . $e->getMessage()]);
            }
            exit;
            
        case 'get_student_details':
            $studentId = intval($_POST['student_id'] ?? 0);
            
            if ($studentId <= 0) {
                echo json_encode(['success' => false, 'message' => 'معرف الطالب غير صحيح']);
                exit;
            }
            
            try {
                // Get student info
                $student = fetchOne("SELECT * FROM students WHERE id = ?", [$studentId]);

                if (!$student) {
                    echo json_encode(['success' => false, 'message' => 'الطالب غير موجود']);
                    exit;
                }

                // Get student courses
                $courses = fetchAll("
                    SELECT course_name, course_code, credit_hours, grade, grade_points, semester, year
                    FROM courses
                    WHERE student_id = ?
                    ORDER BY created_at DESC
                ", [$studentId]);
                
                echo json_encode([
                    'success' => true,
                    'student' => $student,
                    'courses' => $courses
                ]);
                
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'خطأ في جلب بيانات الطالب: ' . $e->getMessage()]);
            }
            exit;
    }
    
    echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    exit;
}

// Get universities for filter
try {
    $universities = fetchAll("SELECT DISTINCT university FROM students ORDER BY university");
} catch (Exception $e) {
    $universities = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلاب - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .student-row:hover {
            background-color: #f8fafc;
        }
        
        .modal {
            backdrop-filter: blur(4px);
        }
        
        .loading {
            display: none;
        }
        
        .loading.active {
            display: inline-block;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إدارة الطلاب</h1>
                        <p class="text-gray-600">عرض وإدارة بيانات الطلاب المسجلين</p>
                    </div>
                    
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <button onclick="exportStudents()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-300">
                            <i class="fas fa-download ml-2"></i>
                            تصدير البيانات
                        </button>
                        
                        <a href="admin_dashboard.php" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-300">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة للوحة الإدارة
                        </a>
                    </div>
                </div>
            </header>

            <!-- Filters -->
            <div class="p-6 bg-white border-b">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                        <input type="text" id="searchInput" placeholder="اسم الطالب، الهاتف، أو الرقم الجامعي" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الجامعة</label>
                        <select id="universityFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">جميع الجامعات</option>
                            <?php foreach ($universities as $uni): ?>
                                <option value="<?php echo htmlspecialchars($uni['university']); ?>">
                                    <?php echo htmlspecialchars($uni['university']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">عدد النتائج</label>
                        <select id="limitSelect" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    
                    <div class="flex items-end">
                        <button onclick="loadStudents()" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                            <i class="fas fa-search ml-2"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </div>

            <!-- Students Table -->
            <div class="p-6">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-4 border-b bg-gray-50">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-800">قائمة الطلاب</h3>
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin text-blue-600"></i>
                                <span class="mr-2 text-blue-600">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table class="w-full">
                            <thead class="bg-gray-50 sticky top-0">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطالب</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الجامعة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المعدل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الساعات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رابط المشاركة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="studentsTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Students will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div id="pagination" class="px-6 py-3 bg-gray-50 border-t">
                        <!-- Pagination will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Details Modal -->
    <div id="studentModal" class="modal fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6 border-b">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-800">تفاصيل الطالب</h3>
                        <button onclick="closeStudentModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                
                <div id="studentDetails" class="p-6">
                    <!-- Student details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;

        // Load students on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadStudents();
            
            // Add event listeners
            document.getElementById('searchInput').addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    loadStudents();
                }
            });
        });

        async function loadStudents(page = 1) {
            const loading = document.querySelector('.loading');
            loading.classList.add('active');
            
            const search = document.getElementById('searchInput').value;
            const university = document.getElementById('universityFilter').value;
            const limit = document.getElementById('limitSelect').value;
            
            try {
                const response = await fetch('admin_students.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'get_students',
                        page: page,
                        limit: limit,
                        search: search,
                        university: university
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayStudents(result.students);
                    displayPagination(result.page, result.totalPages, result.total);
                    currentPage = result.page;
                    totalPages = result.totalPages;
                } else {
                    alert('خطأ في تحميل البيانات');
                }
                
            } catch (error) {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            } finally {
                loading.classList.remove('active');
            }
        }

        function displayStudents(students) {
            const tbody = document.getElementById('studentsTableBody');
            
            if (students.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center py-8 text-gray-500">لا توجد نتائج</td></tr>';
                return;
            }
            
            tbody.innerHTML = students.map(student => `
                <tr class="student-row">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                                <i class="fas fa-user text-blue-600"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">${student.name}</div>
                                <div class="text-sm text-gray-500">${student.phone}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.university}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-sm font-medium text-gray-900">${student.cumulative_gpa || 'غير محدد'}</span>
                        <div class="text-xs text-gray-500">${student.classification || ''}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${student.total_hours || 0}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${student.share_link ? `
                            <div class="text-xs">
                                <div class="flex items-center mb-1">
                                    <button onclick="copyShareLink('${student.share_link}')" class="text-blue-600 hover:text-blue-800 flex items-center">
                                        <i class="fas fa-link text-xs ml-1"></i>
                                        <span class="text-xs">نسخ الرابط</span>
                                    </button>
                                </div>
                                <div class="text-gray-500">
                                    <i class="fas fa-eye text-xs ml-1"></i>
                                    <span>${student.link_views || 0} زيارة</span>
                                </div>
                                ${student.link_expires_at ? `
                                    <div class="text-gray-400 text-xs">
                                        ينتهي: ${new Date(student.link_expires_at).toLocaleDateString('ar-SA')}
                                    </div>
                                ` : ''}
                            </div>
                        ` : '<span class="text-gray-400 text-xs">لا يوجد رابط</span>'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${new Date(student.created_at).toLocaleDateString('ar-SA')}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewStudent(${student.id})" class="text-blue-600 hover:text-blue-900 ml-2" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <a href="edit_student.php?id=${student.id}" class="text-green-600 hover:text-green-900 ml-2" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button onclick="deleteStudent(${student.id}, '${student.name}')" class="text-red-600 hover:text-red-900" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function displayPagination(page, totalPages, total) {
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = `<div class="text-sm text-gray-700">إجمالي النتائج: ${total}</div>`;
                return;
            }
            
            let paginationHTML = `
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        إجمالي النتائج: ${total} | الصفحة ${page} من ${totalPages}
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
            `;
            
            // Previous button
            if (page > 1) {
                paginationHTML += `<button onclick="loadStudents(${page - 1})" class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">السابق</button>`;
            }
            
            // Page numbers
            for (let i = Math.max(1, page - 2); i <= Math.min(totalPages, page + 2); i++) {
                const activeClass = i === page ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300';
                paginationHTML += `<button onclick="loadStudents(${i})" class="px-3 py-1 ${activeClass} rounded">${i}</button>`;
            }
            
            // Next button
            if (page < totalPages) {
                paginationHTML += `<button onclick="loadStudents(${page + 1})" class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">التالي</button>`;
            }
            
            paginationHTML += '</div></div>';
            pagination.innerHTML = paginationHTML;
        }

        async function viewStudent(studentId) {
            try {
                const response = await fetch('admin_students.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'get_student_details',
                        student_id: studentId
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayStudentDetails(result.student, result.courses);
                    document.getElementById('studentModal').classList.remove('hidden');
                } else {
                    alert(result.message || 'خطأ في تحميل بيانات الطالب');
                }
                
            } catch (error) {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            }
        }

        function displayStudentDetails(student, courses) {
            const detailsContainer = document.getElementById('studentDetails');
            
            let coursesHTML = '';
            if (courses.length > 0) {
                coursesHTML = `
                    <div class="mt-6">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">المواد الدراسية</h4>
                        <div class="overflow-x-auto">
                            <table class="w-full border border-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">اسم المادة</th>
                                        <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الساعات</th>
                                        <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">التقدير</th>
                                        <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">النقاط</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${courses.map(course => `
                                        <tr class="border-t">
                                            <td class="px-4 py-2 text-sm">${course.course_name}</td>
                                            <td class="px-4 py-2 text-sm">${course.credit_hours}</td>
                                            <td class="px-4 py-2 text-sm">${course.grade}</td>
                                            <td class="px-4 py-2 text-sm">${course.grade_points}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            }
            
            detailsContainer.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">المعلومات الشخصية</h4>
                        <div class="space-y-3">
                            <div><span class="font-medium">الاسم:</span> ${student.name}</div>
                            <div><span class="font-medium">الهاتف:</span> ${student.phone}</div>
                            <div><span class="font-medium">البريد الإلكتروني:</span> ${student.email || 'غير محدد'}</div>
                            <div><span class="font-medium">الرقم الجامعي:</span> ${student.student_id || 'غير محدد'}</div>
                            <div><span class="font-medium">الجامعة:</span> ${student.university}</div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">المعلومات الأكاديمية</h4>
                        <div class="space-y-3">
                            <div><span class="font-medium">المعدل التراكمي:</span> ${student.cumulative_gpa || 'غير محدد'}</div>
                            <div><span class="font-medium">المعدل الفصلي:</span> ${student.semester_gpa || 'غير محدد'}</div>
                            <div><span class="font-medium">إجمالي الساعات:</span> ${student.total_hours || 0}</div>
                            <div><span class="font-medium">التقدير:</span> ${student.classification || 'غير محدد'}</div>
                            <div><span class="font-medium">تاريخ التسجيل:</span> ${new Date(student.created_at).toLocaleDateString('ar-SA')}</div>
                        </div>
                    </div>
                </div>

                ${student.share_link ? `
                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">معلومات رابط المشاركة</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <span class="font-medium">رابط المشاركة:</span>
                            <div class="mt-2 flex items-center gap-2">
                                <input type="text" value="${window.location.origin}${window.location.pathname}?share=${student.share_link}"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded text-sm bg-white" readonly>
                                <button onclick="copyShareLink('${student.share_link}')"
                                        class="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <div><span class="font-medium">عدد الزيارات:</span> ${student.link_views || 0}</div>
                            ${student.link_expires_at ? `
                                <div><span class="font-medium">تاريخ انتهاء الرابط:</span> ${new Date(student.link_expires_at).toLocaleDateString('ar-SA')}</div>
                            ` : '<div><span class="font-medium">الرابط:</span> دائم</div>'}
                            <div><span class="font-medium">تاريخ إنشاء الرابط:</span> ${new Date(student.created_at).toLocaleDateString('ar-SA')}</div>
                        </div>
                    </div>
                </div>
                ` : ''}

                <div class="mt-6">
                </div>
                ${coursesHTML}
            `;
        }

        function closeStudentModal() {
            document.getElementById('studentModal').classList.add('hidden');
        }

        async function deleteStudent(studentId, studentName) {
            if (!confirm(`هل أنت متأكد من حذف الطالب "${studentName}"؟\nسيتم حذف جميع بياناته نهائياً.`)) {
                return;
            }
            
            try {
                const response = await fetch('admin_students.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'delete_student',
                        student_id: studentId
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(result.message);
                    loadStudents(currentPage); // Reload current page
                } else {
                    alert(result.message || 'خطأ في حذف الطالب');
                }
                
            } catch (error) {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            }
        }

        function exportStudents() {
            // Simple export functionality
            const search = document.getElementById('searchInput').value;
            const university = document.getElementById('universityFilter').value;

            const params = new URLSearchParams({
                action: 'export_students',
                search: search,
                university: university
            });

            window.open(`admin_export.php?${params.toString()}`, '_blank');
        }

        function copyShareLink(shareLink) {
            const fullUrl = `${window.location.origin}${window.location.pathname}?share=${shareLink}`;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(fullUrl).then(() => {
                    // Show success message
                    const button = event.target.closest('button');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check text-xs ml-1"></i><span class="text-xs">تم النسخ!</span>';
                    button.classList.add('text-green-600');

                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.classList.remove('text-green-600');
                        button.classList.add('text-blue-600');
                    }, 2000);
                }).catch(() => {
                    // Fallback for older browsers
                    fallbackCopyTextToClipboard(fullUrl);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(fullUrl);
            }
        }

        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                alert('تم نسخ الرابط بنجاح!');
            } catch (err) {
                alert('فشل في نسخ الرابط. يرجى النسخ يدوياً: ' + text);
            }

            document.body.removeChild(textArea);
        }
    </script>
</body>
</html>
