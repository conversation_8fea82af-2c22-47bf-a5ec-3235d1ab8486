# 🎓 حاسبة المعدل التراكمي - النسخة المحسنة مع قاعدة البيانات
## Enhanced GPA Calculator with MySQL Database

### 🚀 **الإصدار 2.0.0 - Enhanced Edition**

نسخة محسنة ومطورة من حاسبة المعدل التراكمي للجامعة العربية المفتوحة مع دعم قاعدة البيانات MySQL وميزات متقدمة.

---

## ✨ **المميزات الجديدة / New Features**

### 🗄️ **قاعدة البيانات المتقدمة**
- **MySQL Database Integration** - تكامل كامل مع قاعدة البيانات
- **Data Persistence** - حفظ دائم للبيانات
- **Advanced Statistics** - إحصائيات متقدمة ومفصلة
- **Backup & Recovery** - نسخ احتياطية واستعادة البيانات

### 🏛️ **نظام الجامعات المتعدد**
- **Multi-University Support** - دعم جامعات متعددة
- **Custom Grading Systems** - أنظمة تقدير مخصصة لكل جامعة
- **University Management** - إدارة الجامعات وأنظمة التقدير
- **Dynamic Grade Scales** - سلالم درجات ديناميكية

### 📊 **لوحة الإدارة المتقدمة**
- **Real-time Analytics** - تحليلات فورية
- **Student Data Management** - إدارة بيانات الطلاب
- **Grade Distribution Charts** - مخططات توزيع الدرجات
- **University Statistics** - إحصائيات الجامعات
- **Export Capabilities** - إمكانيات التصدير

### 🔗 **نظام المشاركة المحسن**
- **Secure Link Generation** - إنشاء روابط آمنة
- **Expiry Management** - إدارة انتهاء الصلاحية
- **Access Control** - التحكم في الوصول
- **Usage Tracking** - تتبع الاستخدام

---

## 🛠️ **التقنيات المستخدمة / Technologies Used**

### **Backend**
- **PHP 7.4+** - لغة البرمجة الأساسية
- **MySQL 5.7+** - قاعدة البيانات
- **PDO** - واجهة قاعدة البيانات
- **Session Management** - إدارة الجلسات

### **Frontend**
- **HTML5** - هيكل الصفحات
- **Tailwind CSS** - إطار عمل التصميم
- **JavaScript ES6+** - البرمجة التفاعلية
- **Chart.js** - المخططات والرسوم البيانية
- **Font Awesome** - الأيقونات

### **Database Schema**
```sql
- universities (الجامعات)
- grading_systems (أنظمة التقدير)
- grades (الدرجات)
- students (الطلاب)
- gpa_calculations (حسابات المعدل)
- courses (المواد)
- shared_links (الروابط المشاركة)
- admin_users (مستخدمي الإدارة)
- system_settings (إعدادات النظام)
```

---

## 📋 **متطلبات التشغيل / Requirements**

### **Server Requirements**
- **PHP 7.4** أو أحدث
- **MySQL 5.7** أو أحدث
- **Apache/Nginx** خادم ويب
- **mod_rewrite** مفعل (اختياري)

### **PHP Extensions**
- `pdo_mysql` - للاتصال بقاعدة البيانات
- `json` - لمعالجة JSON
- `session` - لإدارة الجلسات
- `mbstring` - لدعم UTF-8

---

## 🚀 **التثبيت والإعداد / Installation & Setup**

### **الطريقة الأولى: الإعداد التلقائي (موصى به)**

1. **تحميل الملفات**
```bash
# نسخ الملفات إلى مجلد الخادم
cp -r php-version/ /path/to/your/webserver/gpa/
```

2. **الوصول لصفحة الإعداد**
```
http://localhost/gpa/php-version/setup.php
```

3. **اتباع خطوات الإعداد**
   - إدخال بيانات قاعدة البيانات
   - إنشاء قاعدة البيانات والجداول
   - إكمال الإعداد

### **الطريقة الثانية: الإعداد اليدوي**

1. **إنشاء قاعدة البيانات**
```sql
CREATE DATABASE gpa_calculator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **استيراد الهيكل**
```bash
mysql -u username -p gpa_calculator < database.sql
```

3. **تحديث إعدادات الاتصال**
```php
// في ملف db_config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'gpa_calculator');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

---

## 🎯 **الاستخدام / Usage**

### **للطلاب**
1. **اختيار الجامعة** ونظام التقدير
2. **إضافة المواد** الدراسية
3. **حساب المعدل** الفصلي أو التراكمي
4. **مشاركة النتائج** مع الآخرين
5. **طباعة التقرير** النهائي

### **للإداريين**
1. **الدخول للوحة الإدارة** (admin/admin123)
2. **مراجعة الإحصائيات** والتحليلات
3. **إدارة بيانات الطلاب**
4. **تصدير التقارير**
5. **إدارة الجامعات** وأنظمة التقدير

---

## 📊 **الجامعات المدعومة / Supported Universities**

| الجامعة | University | نظام التقدير | Grading System |
|---------|------------|-------------|----------------|
| الجامعة العربية المفتوحة | Arab Open University | AOU | A-F with + grades |
| جامعة الملك سعود | King Saud University | AOU | A-F with + grades |
| جامعة الملك عبدالعزيز | King Abdulaziz University | AOU | A-F with + grades |
| جامعة الإمارات | UAE University | Standard | A-F standard |
| الجامعة الأمريكية بالقاهرة | American University in Cairo | Standard | A-F standard |
| الجامعة الأردنية | University of Jordan | Simple | A-F simple |

---

## 🔧 **إعدادات النظام / System Settings**

### **أنظمة التقدير المتاحة**

#### **نظام AOU (الجامعة العربية المفتوحة)**
- A: 4.0 (90-100) - ممتاز
- B+: 3.5 (85-89) - جيد جداً مرتفع
- B: 3.0 (80-84) - جيد جداً
- C+: 2.5 (75-79) - جيد مرتفع
- C: 2.0 (70-74) - جيد
- D: 1.5 (60-69) - مقبول
- F: 0.0 (0-59) - راسب

#### **النظام الأمريكي القياسي**
- A: 4.0 (90-100) - Excellent
- B: 3.0 (80-89) - Good
- C: 2.0 (70-79) - Average
- D: 1.0 (60-69) - Below Average
- F: 0.0 (0-59) - Fail

#### **النظام المبسط**
- A: 4.0 (90-100) - ممتاز
- B: 3.0 (80-89) - جيد
- C: 2.0 (70-79) - مقبول
- D: 1.0 (60-69) - ضعيف
- F: 0.0 (0-59) - راسب

---

## 🔐 **الأمان / Security**

### **ميزات الأمان المطبقة**
- **Input Sanitization** - تنظيف المدخلات
- **SQL Injection Protection** - حماية من حقن SQL
- **XSS Protection** - حماية من XSS
- **CSRF Protection** - حماية من CSRF
- **Session Security** - أمان الجلسات
- **Password Hashing** - تشفير كلمات المرور

### **إعدادات الأمان الموصى بها**
```php
// في ملف .htaccess
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# منع الوصول للملفات الحساسة
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>
```

---

## 📈 **الأداء / Performance**

### **تحسينات الأداء**
- **Database Indexing** - فهرسة قاعدة البيانات
- **Query Optimization** - تحسين الاستعلامات
- **Caching Strategy** - استراتيجية التخزين المؤقت
- **Lazy Loading** - التحميل الكسول
- **Minified Assets** - ضغط الملفات

### **مقاييس الأداء**
- **Page Load Time**: < 2 ثانية
- **Database Queries**: محسنة ومفهرسة
- **Memory Usage**: < 64MB
- **Concurrent Users**: يدعم 100+ مستخدم

---

## 🐛 **استكشاف الأخطاء / Troubleshooting**

### **مشاكل شائعة وحلولها**

#### **خطأ الاتصال بقاعدة البيانات**
```
Error: Connection failed
Solution: تحقق من إعدادات قاعدة البيانات في db_config.php
```

#### **صفحة بيضاء فارغة**
```
Error: White screen
Solution: فعل عرض الأخطاء في PHP أو تحقق من ملف error.log
```

#### **مشكلة في الترميز**
```
Error: Character encoding issues
Solution: تأكد من أن قاعدة البيانات تستخدم utf8mb4
```

---

## 🔄 **التحديثات المستقبلية / Future Updates**

### **الميزات المخططة**
- [ ] **API REST** للتكامل مع تطبيقات أخرى
- [ ] **Mobile App** تطبيق جوال
- [ ] **Advanced Analytics** تحليلات متقدمة
- [ ] **Multi-language Support** دعم لغات إضافية
- [ ] **Cloud Integration** تكامل سحابي
- [ ] **AI Predictions** توقعات ذكية

---

## 📞 **الدعم والمساعدة / Support**

### **للحصول على المساعدة**
- **التوثيق**: راجع هذا الملف
- **المشاكل التقنية**: تحقق من قسم استكشاف الأخطاء
- **طلب ميزات جديدة**: أنشئ issue جديد

### **معلومات الاتصال**
- **المطور**: فريق الجامعة العربية المفتوحة
- **الإصدار**: 2.0.0 Enhanced
- **التاريخ**: ديسمبر 2024

---

## 📄 **الترخيص / License**

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

## 🙏 **شكر وتقدير / Acknowledgments**

- **الجامعة العربية المفتوحة** - للدعم والتوجيه
- **مجتمع PHP** - للأدوات والمكتبات
- **Tailwind CSS** - لإطار عمل التصميم
- **Chart.js** - لمكتبة الرسوم البيانية

---

**© 2024 الجامعة العربية المفتوحة - جميع الحقوق محفوظة**
