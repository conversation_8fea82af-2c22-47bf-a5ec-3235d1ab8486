<?php
/**
 * Database Setup Script
 * سكريبت إعداد قاعدة البيانات
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_admin';

$success = [];
$errors = [];

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);

    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $success[] = "تم إنشاء قاعدة البيانات '$database' بنجاح";

    // Connect to the specific database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);

    // Create tables manually instead of reading SQL file
    createTables($pdo, $success, $errors);
    insertDefaultData($pdo, $success, $errors);
    
    // Test database connection
    $testQuery = $pdo->query("SELECT COUNT(*) FROM admins");
    $adminCount = $testQuery->fetchColumn();
    $success[] = "تم العثور على $adminCount مدير في قاعدة البيانات";
    
    // Create config directory if not exists
    $configDir = __DIR__ . '/config';
    if (!is_dir($configDir)) {
        mkdir($configDir, 0755, true);
        $success[] = "تم إنشاء مجلد الإعدادات";
    }
    
    // Test authentication system
    require_once 'config/database.php';
    require_once 'config/auth.php';
    $success[] = "تم تحميل نظام المصادقة بنجاح";
    
} catch (PDOException $e) {
    $errors[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "خطأ عام: " . $e->getMessage();
}

/**
 * Create database tables
 */
function createTables($pdo, &$success, &$errors) {
    $tables = [
        'admins' => "
            CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                login_attempts INT DEFAULT 0,
                locked_until TIMESTAMP NULL,
                avatar VARCHAR(255) NULL,
                phone VARCHAR(20) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",

        'students' => "
            CREATE TABLE IF NOT EXISTS students (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20) NOT NULL,
                email VARCHAR(100) NULL,
                student_id VARCHAR(50) NULL,
                university VARCHAR(50) NOT NULL DEFAULT 'aou',
                grading_system VARCHAR(50) NOT NULL DEFAULT 'aou',
                semester_gpa DECIMAL(3,2) NULL,
                cumulative_gpa DECIMAL(3,2) NULL,
                total_hours INT DEFAULT 0,
                previous_gpa DECIMAL(3,2) NULL,
                previous_hours INT DEFAULT 0,
                classification VARCHAR(50) NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                share_link VARCHAR(100) UNIQUE NULL,
                link_expires_at TIMESTAMP NULL,
                is_verified BOOLEAN DEFAULT FALSE,
                notes TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_university (university),
                INDEX idx_created_at (created_at),
                INDEX idx_share_link (share_link),
                INDEX idx_gpa (cumulative_gpa)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",

        'courses' => "
            CREATE TABLE IF NOT EXISTS courses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id INT NOT NULL,
                course_name VARCHAR(100) NOT NULL,
                course_code VARCHAR(20) NULL,
                credit_hours INT NOT NULL,
                grade VARCHAR(5) NOT NULL,
                grade_points DECIMAL(3,2) NOT NULL,
                semester VARCHAR(20) NULL,
                year VARCHAR(10) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
                INDEX idx_student_id (student_id),
                INDEX idx_grade (grade)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",

        'activity_logs' => "
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NULL,
                student_id INT NULL,
                action VARCHAR(100) NOT NULL,
                description TEXT NULL,
                old_data JSON NULL,
                new_data JSON NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE SET NULL,
                FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE SET NULL,
                INDEX idx_action (action),
                INDEX idx_created_at (created_at),
                INDEX idx_admin_id (admin_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",

        'settings' => "
            CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT NULL,
                setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
                description TEXT NULL,
                category VARCHAR(50) DEFAULT 'general',
                is_public BOOLEAN DEFAULT FALSE,
                updated_by INT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (updated_by) REFERENCES admins(id) ON DELETE SET NULL,
                INDEX idx_category (category)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];

    foreach ($tables as $tableName => $sql) {
        try {
            $pdo->exec($sql);
            $success[] = "تم إنشاء جدول '$tableName' بنجاح";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') === false) {
                $errors[] = "خطأ في إنشاء جدول '$tableName': " . $e->getMessage();
            }
        }
    }
}

/**
 * Insert default data
 */
function insertDefaultData($pdo, &$success, &$errors) {
    try {
        // Insert default admins
        $adminData = [
            ['admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام الرئيسي', 'super_admin'],
            ['moderator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مشرف النظام', 'moderator'],
            ['viewer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مراقب النظام', 'admin']
        ];

        $stmt = $pdo->prepare("INSERT IGNORE INTO admins (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)");
        foreach ($adminData as $admin) {
            $stmt->execute($admin);
        }
        $success[] = "تم إدراج بيانات المديرين الافتراضية";

        // Insert default settings
        $settingsData = [
            ['site_name', 'حاسبة المعدل التراكمي - الجامعة العربية المفتوحة', 'text', 'اسم الموقع', 'general', 1],
            ['default_language', 'ar', 'text', 'اللغة الافتراضية', 'general', 1],
            ['default_university', 'aou', 'text', 'الجامعة الافتراضية', 'general', 1],
            ['max_courses_per_calculation', '20', 'number', 'الحد الأقصى للمواد في الحساب الواحد', 'limits', 1],
            ['link_expiry_days', '30', 'number', 'مدة انتهاء صلاحية الروابط بالأيام', 'security', 1],
            ['enable_registration', '1', 'boolean', 'تفعيل التسجيل للطلاب', 'features', 1],
            ['enable_sharing', '1', 'boolean', 'تفعيل مشاركة النتائج', 'features', 1],
            ['maintenance_mode', '0', 'boolean', 'وضع الصيانة', 'system', 0],
            ['max_login_attempts', '5', 'number', 'الحد الأقصى لمحاولات تسجيل الدخول', 'security', 0],
            ['lockout_duration', '30', 'number', 'مدة الحظر بالدقائق', 'security', 0],
            ['session_timeout', '120', 'number', 'انتهاء صلاحية الجلسة بالدقائق', 'security', 0]
        ];

        $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description, category, is_public) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($settingsData as $setting) {
            $stmt->execute($setting);
        }
        $success[] = "تم إدراج الإعدادات الافتراضية";

        // Insert sample student data
        $sampleStudents = [
            ['أحمد محمد علي', '0501234567', 'aou', 3.85, 45, 'ممتاز', '*************'],
            ['فاطمة أحمد', '0507654321', 'aou', 3.45, 60, 'جيد جداً مرتفع', '*************'],
            ['محمد سالم', '0509876543', 'ksu', 2.95, 75, 'جيد جداً', '*************'],
            ['نورا عبدالله', '0502468135', 'aou', 3.95, 30, 'ممتاز', '*************'],
            ['خالد أحمد', '0501357924', 'kau', 2.25, 90, 'جيد مرتفع', '*************']
        ];

        $stmt = $pdo->prepare("INSERT IGNORE INTO students (name, phone, university, cumulative_gpa, total_hours, classification, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?)");
        foreach ($sampleStudents as $student) {
            $stmt->execute($student);
        }
        $success[] = "تم إدراج بيانات الطلاب التجريبية";

    } catch (PDOException $e) {
        $errors[] = "خطأ في إدراج البيانات الافتراضية: " . $e->getMessage();
    }
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - حاسبة المعدل التراكمي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .setup-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
        }
        
        .success-item {
            animation: slideInRight 0.5s ease-out;
        }
        
        .error-item {
            animation: slideInLeft 0.5s ease-out;
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <div class="setup-card w-full max-w-4xl p-8 rounded-2xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-database text-white text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">إعداد قاعدة البيانات</h1>
            <p class="text-gray-600">حاسبة المعدل التراكمي - نظام الإدارة</p>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Success Messages -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-green-700 flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    العمليات الناجحة
                </h2>
                
                <?php if (empty($success)): ?>
                    <div class="bg-gray-100 border border-gray-300 text-gray-600 px-4 py-3 rounded-lg">
                        لا توجد عمليات ناجحة
                    </div>
                <?php else: ?>
                    <?php foreach ($success as $index => $message): ?>
                        <div class="success-item bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" 
                             style="animation-delay: <?php echo $index * 0.1; ?>s">
                            <div class="flex items-center">
                                <i class="fas fa-check text-green-600 ml-2"></i>
                                <span><?php echo htmlspecialchars($message); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- Error Messages -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-red-700 flex items-center">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    الأخطاء والتحذيرات
                </h2>
                
                <?php if (empty($errors)): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-thumbs-up ml-2"></i>
                            <span>لا توجد أخطاء! تم الإعداد بنجاح</span>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($errors as $index => $error): ?>
                        <div class="error-item bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg"
                             style="animation-delay: <?php echo $index * 0.1; ?>s">
                            <div class="flex items-center">
                                <i class="fas fa-times text-red-600 ml-2"></i>
                                <span><?php echo htmlspecialchars($error); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Database Information -->
        <div class="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 class="text-lg font-semibold text-blue-800 mb-4">معلومات قاعدة البيانات</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <span class="font-medium text-blue-700">الخادم:</span>
                    <span class="text-blue-600"><?php echo htmlspecialchars($host); ?></span>
                </div>
                <div>
                    <span class="font-medium text-blue-700">قاعدة البيانات:</span>
                    <span class="text-blue-600"><?php echo htmlspecialchars($database); ?></span>
                </div>
                <div>
                    <span class="font-medium text-blue-700">المستخدم:</span>
                    <span class="text-blue-600"><?php echo htmlspecialchars($username); ?></span>
                </div>
                <div>
                    <span class="font-medium text-blue-700">الترميز:</span>
                    <span class="text-blue-600">UTF-8</span>
                </div>
            </div>
        </div>

        <!-- Default Admin Credentials -->
        <div class="mt-6 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 class="text-lg font-semibold text-yellow-800 mb-4">بيانات المدير الافتراضية</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div class="bg-white p-3 rounded border">
                    <div class="font-medium text-yellow-700">المدير الرئيسي</div>
                    <div class="text-yellow-600">المستخدم: admin</div>
                    <div class="text-yellow-600">كلمة المرور: admin123</div>
                </div>
                <div class="bg-white p-3 rounded border">
                    <div class="font-medium text-yellow-700">المشرف</div>
                    <div class="text-yellow-600">المستخدم: moderator</div>
                    <div class="text-yellow-600">كلمة المرور: admin123</div>
                </div>
                <div class="bg-white p-3 rounded border">
                    <div class="font-medium text-yellow-700">المراقب</div>
                    <div class="text-yellow-600">المستخدم: viewer</div>
                    <div class="text-yellow-600">كلمة المرور: admin123</div>
                </div>
            </div>
            <div class="mt-3 text-xs text-yellow-700">
                <i class="fas fa-exclamation-triangle ml-1"></i>
                يرجى تغيير كلمات المرور الافتراضية بعد تسجيل الدخول
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <?php if (empty($errors)): ?>
                <a href="admin_login.php" 
                   class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300 text-center">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل دخول الإدارة
                </a>
                
                <a href="admin_dashboard.php" 
                   class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-300 text-center">
                    <i class="fas fa-tachometer-alt ml-2"></i>
                    لوحة الإدارة
                </a>
            <?php else: ?>
                <button onclick="location.reload()" 
                        class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-300">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة المحاولة
                </button>
            <?php endif; ?>
            
            <a href="index.php" 
               class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-300 text-center">
                <i class="fas fa-home ml-2"></i>
                الصفحة الرئيسية
            </a>
        </div>

        <!-- Footer -->
        <div class="mt-8 pt-6 border-t border-gray-200 text-center">
            <p class="text-sm text-gray-500">
                © 2024 الجامعة العربية المفتوحة. جميع الحقوق محفوظة.
            </p>
            <div class="mt-2 text-xs text-gray-400">
                تم إنشاء هذا النظام لإدارة حاسبة المعدل التراكمي
            </div>
        </div>
    </div>

    <script>
        // Auto-scroll to show all results
        document.addEventListener('DOMContentLoaded', function() {
            const successItems = document.querySelectorAll('.success-item');
            const errorItems = document.querySelectorAll('.error-item');
            
            // Add staggered animation delays
            successItems.forEach((item, index) => {
                item.style.animationDelay = (index * 0.1) + 's';
            });
            
            errorItems.forEach((item, index) => {
                item.style.animationDelay = (index * 0.1) + 's';
            });
        });
        
        // Show success message if setup completed successfully
        <?php if (empty($errors) && !empty($success)): ?>
        setTimeout(() => {
            alert('تم إعداد قاعدة البيانات بنجاح! يمكنك الآن تسجيل الدخول إلى لوحة الإدارة.');
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
