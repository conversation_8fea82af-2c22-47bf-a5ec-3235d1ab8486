<?php
/**
 * Admin Panel for GPA Calculator
 * Arab Open University System
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Try to include database configuration, fallback to simple mode
$use_database = false;
if (file_exists('db_config.php')) {
    try {
        require_once 'db_config.php';
        $pdo = getDBConnection();
        $use_database = true;
    } catch (Exception $e) {
        $use_database = false;
    }
}

if (!$use_database) {
    require_once 'functions_simple.php';
}

// Simple authentication
$admin_logged_in = $_SESSION['admin_logged_in'] ?? false;

if (isset($_POST['login'])) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    if ($use_database) {
        try {
            $stmt = $pdo->prepare("SELECT id, password_hash, full_name FROM admin_users WHERE username = ? AND is_active = 1");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();

            if ($admin && password_verify($password, $admin['password_hash'])) {
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_name'] = $admin['full_name'];
                $admin_logged_in = true;

                // Update last login
                $stmt = $pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$admin['id']]);
            } else {
                $login_error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch (Exception $e) {
            $login_error = 'خطأ في الاتصال بقاعدة البيانات';
        }
    } else {
        // Simple authentication for non-database mode
        if ($username === 'admin' && $password === 'admin123') {
            $_SESSION['admin_logged_in'] = true;
            $admin_logged_in = true;
        } else {
            $login_error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        }
    }
}

if (isset($_POST['logout'])) {
    session_destroy();
    header('Location: admin.php');
    exit;
}

// Handle AJAX requests
if ($admin_logged_in && isset($_POST['action'])) {
    header('Content-Type: application/json');

    switch ($_POST['action']) {
        case 'get_statistics':
            if ($use_database) {
                $stats = getAdminStatisticsDB();
            } else {
                $stats = getAdminStatistics();
            }
            echo json_encode($stats);
            exit;

        case 'get_students':
            if ($use_database) {
                $students = getStudentsDataDB();
            } else {
                $students = getStudentsData();
            }
            echo json_encode($students);
            exit;

        case 'get_universities':
            if ($use_database) {
                $universities = getUniversitiesDataDB();
            } else {
                $universities = getUniversitiesDataSimple();
            }
            echo json_encode($universities);
            exit;

        case 'get_grading_systems':
            if ($use_database) {
                $systems = getGradingSystemsDataDB();
            } else {
                $systems = getGradingSystemsDataSimple();
            }
            echo json_encode($systems);
            exit;

        case 'export_data':
            $type = $_POST['type'] ?? 'students';
            if ($use_database) {
                $result = exportDataDB($type);
            } else {
                $result = exportDataSimple($type);
            }
            echo json_encode($result);
            exit;
    }
}

/**
 * Database functions
 */
function getAdminStatisticsDB() {
    global $pdo;
    try {
        // Total students
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM students");
        $totalStudents = $stmt->fetch()['total'];

        // Total calculations
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM gpa_calculations");
        $totalCalculations = $stmt->fetch()['total'];

        // Average GPA
        $stmt = $pdo->query("SELECT AVG(cumulative_gpa) as avg_gpa FROM gpa_calculations WHERE cumulative_gpa > 0");
        $avgGPA = round($stmt->fetch()['avg_gpa'] ?? 0, 2);

        // Today's calculations
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM gpa_calculations WHERE DATE(created_at) = CURDATE()");
        $todayCalculations = $stmt->fetch()['total'];

        // Grade distribution
        $stmt = $pdo->query("
            SELECT classification_ar, COUNT(*) as count
            FROM gpa_calculations
            WHERE classification_ar IS NOT NULL
            GROUP BY classification_ar
        ");
        $gradeDistribution = [];
        while ($row = $stmt->fetch()) {
            $gradeDistribution[$row['classification_ar']] = $row['count'];
        }

        // University distribution
        $stmt = $pdo->query("
            SELECT u.name_ar, COUNT(s.id) as count
            FROM universities u
            LEFT JOIN students s ON u.id = s.university_id
            GROUP BY u.id, u.name_ar
        ");
        $universityDistribution = [];
        while ($row = $stmt->fetch()) {
            $universityDistribution[$row['name_ar']] = $row['count'];
        }

        return [
            'total_users' => $totalStudents,
            'total_calculations' => $totalCalculations,
            'avg_gpa' => $avgGPA,
            'today_calculations' => $todayCalculations,
            'grade_distribution' => $gradeDistribution,
            'university_distribution' => $universityDistribution,
            'database_mode' => true
        ];

    } catch (Exception $e) {
        return [
            'error' => 'خطأ في جلب الإحصائيات: ' . $e->getMessage(),
            'total_users' => 0,
            'total_calculations' => 0,
            'avg_gpa' => 0,
            'today_calculations' => 0,
            'grade_distribution' => [],
            'university_distribution' => [],
            'database_mode' => true
        ];
    }
}

function getStudentsDataDB() {
    global $pdo;
    try {
        $stmt = $pdo->query("
            SELECT
                s.name,
                s.phone,
                s.email,
                u.name_ar as university_name,
                gc.cumulative_gpa,
                gc.classification_ar,
                gc.total_hours,
                gc.calculation_date,
                gc.created_at
            FROM students s
            LEFT JOIN universities u ON s.university_id = u.id
            LEFT JOIN gpa_calculations gc ON s.id = gc.student_id
            ORDER BY gc.created_at DESC
            LIMIT 100
        ");

        $students = [];
        while ($row = $stmt->fetch()) {
            $students[] = [
                'name' => $row['name'] ?? 'غير محدد',
                'phone' => $row['phone'] ?? 'غير محدد',
                'email' => $row['email'] ?? 'غير محدد',
                'university' => $row['university_name'] ?? 'غير محدد',
                'gpa' => $row['cumulative_gpa'] ?? 0,
                'classification' => $row['classification_ar'] ?? 'غير محدد',
                'total_hours' => $row['total_hours'] ?? 0,
                'date' => $row['calculation_date'] ?? date('Y-m-d'),
                'created_at' => $row['created_at'] ?? date('Y-m-d H:i:s')
            ];
        }

        return $students;

    } catch (Exception $e) {
        return ['error' => 'خطأ في جلب بيانات الطلاب: ' . $e->getMessage()];
    }
}

function getUniversitiesDataDB() {
    global $pdo;
    try {
        $stmt = $pdo->query("
            SELECT
                u.*,
                gs.name_ar as grading_system_name,
                COUNT(s.id) as student_count
            FROM universities u
            LEFT JOIN grading_systems gs ON u.grading_system = gs.id
            LEFT JOIN students s ON u.id = s.university_id
            GROUP BY u.id
            ORDER BY u.name_ar
        ");

        $universities = [];
        while ($row = $stmt->fetch()) {
            $universities[] = $row;
        }

        return $universities;

    } catch (Exception $e) {
        return ['error' => 'خطأ في جلب بيانات الجامعات: ' . $e->getMessage()];
    }
}

function getGradingSystemsDataDB() {
    global $pdo;
    try {
        $stmt = $pdo->query("
            SELECT
                gs.*,
                COUNT(g.id) as grades_count
            FROM grading_systems gs
            LEFT JOIN grades g ON gs.id = g.grading_system_id
            GROUP BY gs.id
            ORDER BY gs.name_ar
        ");

        $systems = [];
        while ($row = $stmt->fetch()) {
            // Get grades for this system
            $gradeStmt = $pdo->prepare("
                SELECT grade, points, min_percentage, max_percentage, description_ar, description_en
                FROM grades
                WHERE grading_system_id = ?
                ORDER BY points DESC
            ");
            $gradeStmt->execute([$row['id']]);
            $grades = $gradeStmt->fetchAll();

            $row['grades'] = $grades;
            $systems[] = $row;
        }

        return $systems;

    } catch (Exception $e) {
        return ['error' => 'خطأ في جلب أنظمة التقدير: ' . $e->getMessage()];
    }
}

/**
 * Simple mode functions (without database)
 */
function getUniversitiesDataSimple() {
    global $universities;
    $result = [];
    foreach ($universities as $id => $uni) {
        $result[] = [
            'id' => $id,
            'name_ar' => $uni['name'],
            'name_en' => $uni['name_en'],
            'grading_system' => $uni['grading_system'],
            'student_count' => 0
        ];
    }
    return $result;
}

function getGradingSystemsDataSimple() {
    global $grading_systems;
    $result = [];
    foreach ($grading_systems as $id => $system) {
        $grades = [];
        foreach ($system['grades'] as $grade => $info) {
            $grades[] = [
                'grade' => $grade,
                'points' => $info['points'],
                'description_ar' => $info['description']
            ];
        }
        $result[] = [
            'id' => $id,
            'name_ar' => $system['name'],
            'name_en' => $system['name_en'],
            'grades' => $grades
        ];
    }
    return $result;
}

function exportDataDB($type) {
    // Database export functionality
    return ['success' => true, 'message' => 'تم التصدير من قاعدة البيانات'];
}

function exportDataSimple($type) {
    // Simple export functionality
    return ['success' => true, 'message' => 'تم التصدير من الملفات'];
}

$isLoggedIn = $admin_logged_in;
?>

<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo $page_direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $current_language === 'ar' ? 'لوحة الإدارة' : 'Admin Panel'; ?> - <?php echo t('app_title'); ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        .stat-card {
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            position: relative;
            height: 400px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen <?php echo $page_direction; ?>">

    <?php if (!$isLoggedIn): ?>
    <!-- Login Screen -->
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4">
            <div class="text-center mb-8">
                <div class="h-16 w-16 bg-purple-600 rounded-full flex items-center justify-center text-white text-2xl mx-auto mb-4">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-800"><?php echo $current_language === 'ar' ? 'دخول الإدارة' : 'Admin Login'; ?></h2>
                <p class="text-gray-600 mt-2"><?php echo $current_language === 'ar' ? 'أدخل كلمة المرور للوصول للوحة الإدارة' : 'Enter password to access admin panel'; ?></p>
            </div>
            
            <form id="loginForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2"><?php echo $current_language === 'ar' ? 'كلمة المرور' : 'Password'; ?></label>
                    <input type="password" id="adminPassword" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                           placeholder="<?php echo $current_language === 'ar' ? 'أدخل كلمة المرور' : 'Enter password'; ?>">
                </div>
                
                <button type="submit" class="w-full bg-purple-600 text-white py-3 rounded-lg hover:bg-purple-700 transition-colors">
                    <i class="fas fa-sign-in-alt <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                    <?php echo $current_language === 'ar' ? 'دخول' : 'Login'; ?>
                </button>
            </form>
            
            <div class="mt-6 text-center">
                <a href="index.php" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left <?php echo $current_language === 'ar' ? 'ml-1' : 'mr-1'; ?>"></i>
                    <?php echo $current_language === 'ar' ? 'العودة للحاسبة' : 'Back to Calculator'; ?>
                </a>
            </div>
        </div>
    </div>
    
    <?php else: ?>
    <!-- Admin Dashboard -->
    
    <!-- Top Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Title -->
                <div class="flex items-center gap-4">
                    <div class="h-12 w-12 bg-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-xl">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800"><?php echo $current_language === 'ar' ? 'لوحة الإدارة' : 'Admin Panel'; ?></h1>
                        <p class="text-sm text-gray-600"><?php echo t('app_title'); ?></p>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex items-center gap-4">
                    <a href="index.php" class="bg-blue-100 text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors">
                        <i class="fas fa-calculator <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'حاسبة المعدل' : 'GPA Calculator'; ?></span>
                    </a>
                    <a href="universities.php" class="bg-green-100 text-green-600 px-4 py-2 rounded-lg hover:bg-green-200 transition-colors">
                        <i class="fas fa-university <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'الجامعات' : 'Universities'; ?></span>
                    </a>
                    <button onclick="logout()" class="bg-red-100 text-red-600 px-4 py-2 rounded-lg hover:bg-red-200 transition-colors">
                        <i class="fas fa-sign-out-alt <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                        <span><?php echo $current_language === 'ar' ? 'خروج' : 'Logout'; ?></span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        
        <!-- Statistics Cards -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card bg-white rounded-2xl shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-600 text-sm font-medium"><?php echo $current_language === 'ar' ? 'إجمالي المستخدمين' : 'Total Users'; ?></p>
                        <p id="totalUsers" class="text-3xl font-bold text-blue-900">0</p>
                    </div>
                    <div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card bg-white rounded-2xl shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-600 text-sm font-medium"><?php echo $current_language === 'ar' ? 'متوسط المعدل' : 'Average GPA'; ?></p>
                        <p id="avgGPA" class="text-3xl font-bold text-green-900">0.00</p>
                    </div>
                    <div class="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card bg-white rounded-2xl shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-yellow-600 text-sm font-medium"><?php echo $current_language === 'ar' ? 'حسابات اليوم' : 'Today\'s Calculations'; ?></p>
                        <p id="todayCalculations" class="text-3xl font-bold text-yellow-900">0</p>
                    </div>
                    <div class="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calculator text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card bg-white rounded-2xl shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-600 text-sm font-medium"><?php echo $current_language === 'ar' ? 'إجمالي الحسابات' : 'Total Calculations'; ?></p>
                        <p id="totalCalculations" class="text-3xl font-bold text-purple-900">0</p>
                    </div>
                    <div class="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid lg:grid-cols-2 gap-8 mb-8">
            <!-- Grade Distribution Chart -->
            <div class="bg-white rounded-2xl shadow-lg p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-chart-pie text-blue-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                    <?php echo $current_language === 'ar' ? 'توزيع التقديرات' : 'Grade Distribution'; ?>
                </h3>
                <div class="chart-container">
                    <canvas id="gradeChart"></canvas>
                </div>
            </div>

            <!-- University Distribution Chart -->
            <div class="bg-white rounded-2xl shadow-lg p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-university text-green-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                    <?php echo $current_language === 'ar' ? 'توزيع الجامعات' : 'University Distribution'; ?>
                </h3>
                <div class="chart-container">
                    <canvas id="universityChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Students Table -->
        <div class="bg-white rounded-2xl shadow-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-table text-purple-600 <?php echo $current_language === 'ar' ? 'ml-3' : 'mr-3'; ?>"></i>
                    <?php echo $current_language === 'ar' ? 'بيانات الطلاب' : 'Students Data'; ?>
                </h3>
                <button onclick="exportData()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-download <?php echo $current_language === 'ar' ? 'ml-2' : 'mr-2'; ?>"></i>
                    <?php echo $current_language === 'ar' ? 'تصدير البيانات' : 'Export Data'; ?>
                </button>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <?php echo $current_language === 'ar' ? 'الاسم' : 'Name'; ?>
                            </th>
                            <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <?php echo $current_language === 'ar' ? 'الهاتف' : 'Phone'; ?>
                            </th>
                            <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <?php echo $current_language === 'ar' ? 'المعدل' : 'GPA'; ?>
                            </th>
                            <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <?php echo $current_language === 'ar' ? 'التقدير' : 'Classification'; ?>
                            </th>
                            <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <?php echo $current_language === 'ar' ? 'الجامعة' : 'University'; ?>
                            </th>
                            <th class="px-6 py-3 text-<?php echo $current_language === 'ar' ? 'right' : 'left'; ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <?php echo $current_language === 'ar' ? 'التاريخ' : 'Date'; ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody id="studentsTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <?php endif; ?>

    <!-- JavaScript -->
    <script src="assets/js/admin.js"></script>
</body>
</html>
