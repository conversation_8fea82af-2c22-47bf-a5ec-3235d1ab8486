USE gpa_calculator;

-- Add missing columns to students table
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS university VARCHAR(255) DEFAULT '',
ADD COLUMN IF NOT EXISTS cumulative_gpa DECIMAL(4,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS semester_gpa DECIMAL(4,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS total_hours INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS classification VARCHAR(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS share_link VARCHAR(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS link_views INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS link_expires_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_students_share_link ON students(share_link);
CREATE INDEX IF NOT EXISTS idx_students_university_name ON students(university);

-- Update existing records to have university names instead of IDs
UPDATE students s 
JOIN universities u ON s.university_id = u.id 
SET s.university = u.name_ar 
WHERE s.university IS NULL OR s.university = '';

-- Update students with their latest GPA calculations
UPDATE students s 
JOIN (
    SELECT 
        student_id,
        cumulative_gpa,
        semester_gpa,
        total_hours,
        classification_ar,
        ROW_NUMBER() OVER (PARTITION BY student_id ORDER BY created_at DESC) as rn
    FROM gpa_calculations
) latest_calc ON s.id = latest_calc.student_id AND latest_calc.rn = 1
SET 
    s.cumulative_gpa = latest_calc.cumulative_gpa,
    s.semester_gpa = latest_calc.semester_gpa,
    s.total_hours = latest_calc.total_hours,
    s.classification = latest_calc.classification_ar;
