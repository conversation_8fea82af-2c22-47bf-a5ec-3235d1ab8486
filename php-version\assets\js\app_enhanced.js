/**
 * Enhanced GPA Calculator JavaScript
 * PHP Version with MySQL Database Support
 */

class EnhancedGPACalculator {
    constructor() {
        this.courses = [];
        this.currentLanguage = window.currentLanguage || 'ar';
        this.gradingSystem = window.gradingSystem || {};
        this.universities = window.universities || {};
        this.allGradingSystems = window.allGradingSystems || {};
        this.currentResults = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateLanguage();
        this.addInitialCourse();
        this.checkForSharedGPA();
        this.setupCalculationType();
        this.loadSavedData();
    }

    setupEventListeners() {
        // Add course button
        const addCourseBtn = document.getElementById('addCourseBtn');
        if (addCourseBtn) {
            addCourseBtn.addEventListener('click', () => this.addCourse());
        }

        // Calculate button
        const calculateBtn = document.getElementById('calculateBtn');
        if (calculateBtn) {
            calculateBtn.addEventListener('click', () => this.calculateGPA());
        }

        // Language toggle
        const languageToggle = document.getElementById('languageToggle');
        if (languageToggle) {
            languageToggle.addEventListener('click', () => this.toggleLanguage());
        }

        // Share button
        const shareBtn = document.getElementById('shareBtn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.openShareModal());
        }

        // University selector
        const universitySelect = document.getElementById('universitySelect');
        if (universitySelect) {
            universitySelect.addEventListener('change', (e) => {
                this.changeUniversity(e.target.value);
            });
        }

        // Grading system selector
        const gradingSystemSelect = document.getElementById('gradingSystemSelect');
        if (gradingSystemSelect) {
            gradingSystemSelect.addEventListener('change', (e) => {
                this.changeGradingSystem(e.target.value);
            });
        }

        // Calculation type selector
        const calculationType = document.getElementById('calculationType');
        if (calculationType) {
            calculationType.addEventListener('change', (e) => {
                this.togglePreviousGpaSection(e.target.value);
            });
        }

        // Share form
        const shareForm = document.getElementById('shareForm');
        if (shareForm) {
            shareForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleShareForm();
            });
        }

        // Chat functionality
        const sendChatBtn = document.getElementById('sendChatBtn');
        const chatInput = document.getElementById('chatInput');

        if (sendChatBtn) {
            sendChatBtn.addEventListener('click', () => this.sendChatMessage());
        }

        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendChatMessage();
                }
            });
        }

        // Data buttons
        const saveDataBtn = document.getElementById('saveDataBtn');
        const loadDataBtn = document.getElementById('loadDataBtn');
        const printBtn = document.getElementById('printBtn');

        if (saveDataBtn) {
            saveDataBtn.addEventListener('click', () => this.saveCourses());
        }

        if (loadDataBtn) {
            loadDataBtn.addEventListener('click', () => this.loadCourses());
        }

        if (printBtn) {
            printBtn.addEventListener('click', () => this.printResults());
        }
    }

    addCourse(courseData = null) {
        const container = document.getElementById('coursesContainer');
        if (!container) return;

        const courseId = 'course_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        const courseDiv = document.createElement('div');
        courseDiv.className = 'course-item bg-gray-50 rounded-lg p-4 border border-gray-200 mb-4';
        courseDiv.id = courseId;

        courseDiv.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        ${this.currentLanguage === 'ar' ? 'اسم المادة' : 'Course Name'}
                    </label>
                    <input type="text"
                           class="course-name w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="${this.currentLanguage === 'ar' ? 'مثال: الرياضيات' : 'e.g., Mathematics'}"
                           value="${courseData?.name || ''}">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        ${this.currentLanguage === 'ar' ? 'عدد الساعات' : 'Credit Hours'}
                    </label>
                    <input type="number"
                           class="course-hours w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           min="1" max="6"
                           placeholder="3"
                           value="${courseData?.hours || ''}">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        ${this.currentLanguage === 'ar' ? 'التقدير' : 'Grade'}
                    </label>
                    <select class="course-grade w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">${this.currentLanguage === 'ar' ? 'اختر التقدير' : 'Select Grade'}</option>
                        ${this.getGradeOptions(courseData?.grade)}
                    </select>
                </div>

                <div class="flex justify-center">
                    <button type="button"
                            class="remove-course bg-red-500 text-white p-2 rounded-lg hover:bg-red-600 transition-colors duration-300"
                            onclick="gpaCalculator.removeCourse('${courseId}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        container.appendChild(courseDiv);

        // Add animation
        courseDiv.style.opacity = '0';
        courseDiv.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            courseDiv.style.transition = 'all 0.3s ease';
            courseDiv.style.opacity = '1';
            courseDiv.style.transform = 'translateY(0)';
        }, 10);

        // Add course change listeners
        this.addCourseListeners(courseDiv);
    }

    addCourseListeners(courseDiv) {
        const inputs = courseDiv.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.validateCourse(courseDiv);
                this.updateSmartSuggestions();
            });
        });
    }

    validateCourse(courseDiv) {
        const name = courseDiv.querySelector('.course-name').value.trim();
        const hours = courseDiv.querySelector('.course-hours').value;
        const grade = courseDiv.querySelector('.course-grade').value;

        if (name && hours && grade) {
            courseDiv.classList.remove('error');
            courseDiv.classList.add('valid');
        } else {
            courseDiv.classList.remove('valid');
            if (name || hours || grade) {
                courseDiv.classList.add('error');
            }
        }
    }

    removeCourse(courseId) {
        const courseElement = document.getElementById(courseId);
        if (courseElement) {
            courseElement.style.transition = 'all 0.3s ease';
            courseElement.style.opacity = '0';
            courseElement.style.transform = 'translateX(-100%)';

            setTimeout(() => {
                courseElement.remove();
                this.updateSmartSuggestions();
            }, 300);
        }
    }

    getGradeOptions(selectedGrade = '') {
        let options = '';
        if (this.gradingSystem && this.gradingSystem.grades) {
            Object.keys(this.gradingSystem.grades).forEach(grade => {
                const gradeInfo = this.gradingSystem.grades[grade];
                const selected = grade === selectedGrade ? 'selected' : '';
                options += `<option value="${grade}" ${selected}>${grade} (${gradeInfo.points}) - ${gradeInfo.description}</option>`;
            });
        }
        return options;
    }

    async calculateGPA() {
        const calculateBtn = document.getElementById('calculateBtn');
        const loadingSpinner = calculateBtn.querySelector('.loading-spinner');

        // Show loading
        loadingSpinner.classList.add('active');
        calculateBtn.disabled = true;

        try {
            // Collect course data
            const coursesData = this.collectCoursesData();

            if (coursesData.length === 0) {
                throw new Error(this.currentLanguage === 'ar' ? 'يرجى إضافة مادة واحدة على الأقل' : 'Please add at least one course');
            }

            // Get calculation type and previous data
            const calculationType = document.getElementById('calculationType').value;
            const previousGpa = parseFloat(document.getElementById('previousGpa')?.value || 0);
            const previousHours = parseInt(document.getElementById('previousHours')?.value || 0);

            // Send to server
            const response = await fetch('functions_new.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'calculate_gpa',
                    courses: JSON.stringify(coursesData),
                    previous_gpa: previousGpa,
                    previous_hours: previousHours
                })
            });

            const result = await response.json();

            if (result.error) {
                throw new Error(result.error);
            }

            this.currentResults = result;
            this.displayResults(result);
            this.updateSmartSuggestions();

        } catch (error) {
            console.error('Error calculating GPA:', error);
            this.showAlert(error.message || 'حدث خطأ في حساب المعدل', 'error');
        } finally {
            // Hide loading
            loadingSpinner.classList.remove('active');
            calculateBtn.disabled = false;
        }
    }

    collectCoursesData() {
        const coursesData = [];
        const courseElements = document.querySelectorAll('.course-item');

        courseElements.forEach(element => {
            const name = element.querySelector('.course-name').value.trim();
            const hours = parseInt(element.querySelector('.course-hours').value);
            const grade = element.querySelector('.course-grade').value;

            if (name && hours && grade) {
                coursesData.push({ name, hours, grade });
            }
        });

        return coursesData;
    }

    displayResults(result) {
        const resultsSection = document.getElementById('resultsSection');
        const semesterGpaValue = document.getElementById('semesterGpaValue');
        const cumulativeGpaValue = document.getElementById('cumulativeGpaValue');
        const totalHoursValue = document.getElementById('totalHoursValue');
        const classificationDisplay = document.getElementById('classificationDisplay');
        const classificationText = document.getElementById('classificationText');

        // Update values with animation
        this.animateNumber(semesterGpaValue, result.semester_gpa);
        this.animateNumber(cumulativeGpaValue, result.cumulative_gpa);
        this.animateNumber(totalHoursValue, result.total_hours);

        // Update classification
        if (result.classification) {
            classificationText.textContent = this.currentLanguage === 'ar' ?
                result.classification.name : result.classification.name_en;

            // Update classification card style
            classificationDisplay.className = `mb-6 p-6 rounded-xl text-center text-white classification-${result.classification.class}`;
        }

        // Show results with animation
        resultsSection.classList.remove('hidden');
        resultsSection.classList.add('fade-in');
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    animateNumber(element, targetValue) {
        const startValue = 0;
        const duration = 1000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const currentValue = startValue + (targetValue - startValue) * progress;

            if (element.id.includes('Gpa')) {
                element.textContent = currentValue.toFixed(2);
            } else {
                element.textContent = Math.floor(currentValue);
            }

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    updateSmartSuggestions() {
        const suggestionsContainer = document.getElementById('smartSuggestions');
        const noSuggestionsText = document.getElementById('noSuggestionsText');

        if (!this.currentResults) {
            return;
        }

        const suggestions = this.generateSmartSuggestions();

        if (suggestions.length > 0) {
            noSuggestionsText.style.display = 'none';
            suggestionsContainer.innerHTML = suggestions.map(suggestion => `
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 mb-3">
                    <div class="flex items-start gap-3">
                        <i class="fas ${suggestion.icon} text-blue-600 mt-1"></i>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-1">${suggestion.title}</h4>
                            <p class="text-sm text-gray-600">${suggestion.description}</p>
                        </div>
                    </div>
                </div>
            `).join('');
        }
    }

    generateSmartSuggestions() {
        const suggestions = [];
        const gpa = this.currentResults.cumulative_gpa;
        const totalHours = this.currentResults.total_hours;

        if (this.currentLanguage === 'ar') {
            if (gpa < 2.0) {
                suggestions.push({
                    icon: 'fa-exclamation-triangle',
                    title: 'تحسين المعدل مطلوب',
                    description: 'معدلك أقل من 2.0. ركز على المواد ذات الساعات الأكثر واحرص على الحصول على درجات عالية.'
                });
            } else if (gpa >= 3.75) {
                suggestions.push({
                    icon: 'fa-trophy',
                    title: 'أداء ممتاز!',
                    description: 'معدلك ممتاز! حافظ على هذا المستوى المتميز.'
                });
            } else if (gpa >= 3.0) {
                suggestions.push({
                    icon: 'fa-arrow-up',
                    title: 'يمكنك الوصول للامتياز',
                    description: 'معدلك جيد جداً. بقليل من الجهد يمكنك الوصول للامتياز.'
                });
            }

            if (totalHours < 30) {
                suggestions.push({
                    icon: 'fa-info-circle',
                    title: 'بداية الرحلة الجامعية',
                    description: 'أنت في بداية رحلتك الجامعية. ركز على بناء أساس قوي.'
                });
            } else if (totalHours >= 90) {
                suggestions.push({
                    icon: 'fa-graduation-cap',
                    title: 'قريب من التخرج',
                    description: 'أنت قريب من التخرج! حافظ على معدلك في الفصول الأخيرة.'
                });
            }
        } else {
            if (gpa < 2.0) {
                suggestions.push({
                    icon: 'fa-exclamation-triangle',
                    title: 'GPA Improvement Needed',
                    description: 'Your GPA is below 2.0. Focus on courses with more credit hours and aim for higher grades.'
                });
            } else if (gpa >= 3.75) {
                suggestions.push({
                    icon: 'fa-trophy',
                    title: 'Excellent Performance!',
                    description: 'Your GPA is excellent! Keep up this outstanding level.'
                });
            }
        }

        return suggestions;
    }

    async toggleLanguage() {
        try {
            const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';

            const response = await fetch('functions_new.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'change_language',
                    language: newLanguage
                })
            });

            const result = await response.json();

            if (result.success) {
                location.reload();
            }

        } catch (error) {
            console.error('Error changing language:', error);
        }
    }

    async changeUniversity(universityId) {
        try {
            const response = await fetch('universities.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'select_university',
                    university_id: universityId
                })
            });

            const result = await response.json();

            if (result.success) {
                location.reload();
            }

        } catch (error) {
            console.error('Error changing university:', error);
        }
    }

    async changeGradingSystem(system) {
        try {
            const response = await fetch('functions_new.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'change_grading_system',
                    system: system
                })
            });

            const result = await response.json();

            if (result.success) {
                location.reload();
            }

        } catch (error) {
            console.error('Error changing grading system:', error);
        }
    }

    setupCalculationType() {
        const calculationType = document.getElementById('calculationType');
        if (calculationType) {
            this.togglePreviousGpaSection(calculationType.value);
        }
    }

    togglePreviousGpaSection(type) {
        const section = document.getElementById('previousGpaSection');
        if (section) {
            if (type === 'cumulative') {
                section.classList.remove('hidden');
                section.classList.add('slide-in');
            } else {
                section.classList.add('hidden');
            }
        }
    }

    updateLanguage() {
        // Language update logic handled by PHP reload
        this.currentLanguage = window.currentLanguage || 'ar';
    }

    addInitialCourse() {
        const container = document.getElementById('coursesContainer');
        if (container && container.children.length === 0) {
            this.addCourse();
        }
    }

    checkForSharedGPA() {
        const urlParams = new URLSearchParams(window.location.search);
        const sharedId = urlParams.get('shared');
        if (sharedId) {
            this.loadSharedGPA(sharedId);
        }
    }

    async loadSharedGPA(sharedId) {
        try {
            const response = await fetch('functions_new.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'get_shared_data',
                    link_id: sharedId
                })
            });

            const result = await response.json();

            if (result.success && result.data) {
                this.clearCourses();

                // Load courses
                if (result.data.courses) {
                    result.data.courses.forEach(course => {
                        this.addCourse({
                            name: course.course_name,
                            hours: course.credit_hours,
                            grade: course.grade
                        });
                    });
                }

                // Set previous GPA data if available
                if (result.data.previous_gpa > 0) {
                    document.getElementById('calculationType').value = 'cumulative';
                    this.togglePreviousGpaSection('cumulative');
                    document.getElementById('previousGpa').value = result.data.previous_gpa;
                    document.getElementById('previousHours').value = result.data.previous_hours;
                }

                this.showAlert('تم تحميل البيانات المشاركة بنجاح', 'success');
            } else {
                this.showAlert('الرابط غير صحيح أو منتهي الصلاحية', 'error');
            }

        } catch (error) {
            console.error('Error loading shared data:', error);
            this.showAlert('خطأ في تحميل البيانات المشاركة', 'error');
        }
    }

    clearCourses() {
        const container = document.getElementById('coursesContainer');
        if (container) {
            container.innerHTML = '';
        }
    }

    openShareModal() {
        if (!this.currentResults) {
            this.showAlert('يرجى حساب المعدل أولاً', 'error');
            return;
        }

        document.getElementById('shareModal').classList.remove('hidden');
    }

    async handleShareForm() {
        const name = document.getElementById('studentName').value.trim();
        const phone = document.getElementById('studentPhone').value.trim();

        if (!name || !phone) {
            this.showAlert('يرجى ملء جميع الحقول', 'error');
            return;
        }

        if (!this.currentResults) {
            this.showAlert('يرجى حساب المعدل أولاً', 'error');
            return;
        }

        try {
            // Show loading
            this.showLoadingOverlay(true);

            // Collect current data
            const coursesData = this.collectCoursesData();
            const calculationType = document.getElementById('calculationType').value;
            const previousGpa = parseFloat(document.getElementById('previousGpa')?.value || 0);
            const previousHours = parseInt(document.getElementById('previousHours')?.value || 0);

            // Save student data to server
            const response = await fetch('functions_new.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'save_student_data',
                    name: name,
                    phone: phone,
                    gpa: this.currentResults.cumulative_gpa,
                    courses: JSON.stringify(coursesData),
                    calculation_type: calculationType,
                    previous_gpa: previousGpa,
                    previous_hours: previousHours
                })
            });

            const result = await response.json();

            if (result.success) {
                // Generate URL
                const shareUrl = `${window.location.origin}${window.location.pathname}?shared=${result.link_id}`;

                // Show generated link
                document.getElementById('shareUrl').value = shareUrl;
                document.getElementById('generatedLink').classList.remove('hidden');

                this.showAlert('تم إنشاء الرابط وحفظ البيانات بنجاح', 'success');
            } else {
                throw new Error(result.error || 'فشل في حفظ البيانات');
            }

        } catch (error) {
            console.error('Error in share form:', error);
            this.showAlert('حدث خطأ في حفظ البيانات: ' + error.message, 'error');
        } finally {
            this.showLoadingOverlay(false);
        }
    }

    sendChatMessage() {
        const input = document.getElementById('chatInput');
        const container = document.getElementById('chatContainer');

        if (!input.value.trim()) return;

        const message = input.value.trim();
        input.value = '';

        // Add user message
        this.addChatMessage(message, 'user');

        // Simulate AI response
        setTimeout(() => {
            const response = this.generateAIResponse(message);
            this.addChatMessage(response, 'ai');
        }, 1000);
    }

    addChatMessage(message, sender) {
        const container = document.getElementById('chatContainer');

        // Remove welcome message if exists
        const welcomeMessage = container.querySelector('#welcomeMessage');
        if (welcomeMessage) {
            welcomeMessage.parentElement.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `mb-4 ${sender === 'user' ? 'text-right' : 'text-left'}`;

        messageDiv.innerHTML = `
            <div class="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                sender === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-800'
            }">
                ${message}
            </div>
        `;

        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
    }

    generateAIResponse(message) {
        const responses = {
            ar: [
                'يمكنني مساعدتك في حساب معدلك التراكمي. أضف مواد دراسية واضغط على احسب المعدل.',
                'لتحسين معدلك، ركز على المواد ذات الساعات الأكثر واحرص على الحصول على درجات عالية.',
                'المعدل التراكمي يحسب بضرب نقاط كل مادة في عدد ساعاتها ثم القسمة على إجمالي الساعات.',
                'إذا كان معدلك أقل من 2.0، فأنت بحاجة لتحسين أدائك الأكاديمي.',
                'يمكنك حفظ بياناتك ومشاركتها مع الآخرين باستخدام الأزرار المتاحة.',
                'نظام التقدير في الجامعة العربية المفتوحة يتراوح من A (4.0) إلى F (0.0).',
                'للحصول على تقدير ممتاز تحتاج معدل 3.75 أو أكثر.'
            ],
            en: [
                'I can help you calculate your GPA. Add courses and click Calculate GPA.',
                'To improve your GPA, focus on courses with more credit hours and aim for high grades.',
                'GPA is calculated by multiplying grade points by credit hours, then dividing by total hours.',
                'If your GPA is below 2.0, you need to improve your academic performance.',
                'You can save your data and share it with others using the available buttons.',
                'The grading system ranges from A (4.0) to F (0.0).',
                'To get an excellent grade, you need a GPA of 3.75 or higher.'
            ]
        };

        const langResponses = responses[this.currentLanguage] || responses.ar;

        // Simple keyword matching for more relevant responses
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('معدل') || lowerMessage.includes('gpa')) {
            return langResponses[0];
        } else if (lowerMessage.includes('تحسين') || lowerMessage.includes('improve')) {
            return langResponses[1];
        } else if (lowerMessage.includes('كيف') || lowerMessage.includes('how')) {
            return langResponses[2];
        } else {
            return langResponses[Math.floor(Math.random() * langResponses.length)];
        }
    }

    async saveCourses() {
        try {
            const coursesData = this.collectCoursesData();

            if (coursesData.length === 0) {
                this.showAlert('لا توجد مواد لحفظها', 'error');
                return;
            }

            const response = await fetch('functions_new.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'save_courses',
                    courses: JSON.stringify(coursesData)
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('تم حفظ البيانات بنجاح', 'success');
            } else {
                throw new Error('فشل في حفظ البيانات');
            }

        } catch (error) {
            console.error('Error saving courses:', error);
            this.showAlert('حدث خطأ في حفظ البيانات', 'error');
        }
    }

    async loadCourses() {
        try {
            const response = await fetch('functions_new.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'load_courses'
                })
            });

            const result = await response.json();

            if (result.courses && result.courses.length > 0) {
                this.clearCourses();
                result.courses.forEach(course => {
                    this.addCourse(course);
                });
                this.showAlert('تم تحميل البيانات بنجاح', 'success');
            } else {
                this.showAlert('لا توجد بيانات محفوظة', 'info');
            }

        } catch (error) {
            console.error('Error loading courses:', error);
            this.showAlert('حدث خطأ في تحميل البيانات', 'error');
        }
    }

    loadSavedData() {
        // Load from localStorage for compatibility
        const savedData = localStorage.getItem('gpaCalculatorData');
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                if (data.courses && data.courses.length > 0) {
                    this.clearCourses();
                    data.courses.forEach(course => {
                        this.addCourse(course);
                    });
                }
            } catch (error) {
                console.error('Error loading saved data:', error);
            }
        }
    }

    printResults() {
        if (!this.currentResults) {
            this.showAlert('يرجى حساب المعدل أولاً', 'error');
            return;
        }

        const printWindow = window.open('', '_blank');
        const coursesData = this.collectCoursesData();

        const printContent = `
            <!DOCTYPE html>
            <html dir="${this.currentLanguage === 'ar' ? 'rtl' : 'ltr'}">
            <head>
                <meta charset="UTF-8">
                <title>نتائج المعدل التراكمي</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .results { margin: 20px 0; }
                    .courses-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .courses-table th, .courses-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    .courses-table th { background-color: #f2f2f2; }
                    .gpa-box { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${this.currentLanguage === 'ar' ? 'نتائج المعدل التراكمي' : 'GPA Results'}</h1>
                    <h2>${this.currentLanguage === 'ar' ? 'الجامعة العربية المفتوحة' : 'Arab Open University'}</h2>
                    <p>${this.currentLanguage === 'ar' ? 'تاريخ الطباعة:' : 'Print Date:'} ${new Date().toLocaleDateString()}</p>
                </div>

                <div class="gpa-box">
                    <h3>${this.currentLanguage === 'ar' ? 'النتائج' : 'Results'}</h3>
                    <p><strong>${this.currentLanguage === 'ar' ? 'المعدل الفصلي:' : 'Semester GPA:'}</strong> ${this.currentResults.semester_gpa}</p>
                    <p><strong>${this.currentLanguage === 'ar' ? 'المعدل التراكمي:' : 'Cumulative GPA:'}</strong> ${this.currentResults.cumulative_gpa}</p>
                    <p><strong>${this.currentLanguage === 'ar' ? 'إجمالي الساعات:' : 'Total Hours:'}</strong> ${this.currentResults.total_hours}</p>
                </div>

                <table class="courses-table">
                    <thead>
                        <tr>
                            <th>${this.currentLanguage === 'ar' ? 'اسم المادة' : 'Course Name'}</th>
                            <th>${this.currentLanguage === 'ar' ? 'الساعات' : 'Hours'}</th>
                            <th>${this.currentLanguage === 'ar' ? 'التقدير' : 'Grade'}</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${coursesData.map(course => `
                            <tr>
                                <td>${course.name}</td>
                                <td>${course.hours}</td>
                                <td>${course.grade}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </body>
            </html>
        `;

        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
    }

    showLoadingOverlay(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }
    }

    showAlert(message, type = 'info') {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `fixed top-4 ${this.currentLanguage === 'ar' ? 'left-4' : 'right-4'} z-50 p-4 rounded-lg shadow-lg text-white max-w-sm ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            type === 'info' ? 'bg-blue-500' :
            'bg-gray-500'
        }`;
        alert.innerHTML = `
            <div class="flex items-center gap-2">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-triangle' :
                    type === 'info' ? 'fa-info-circle' :
                    'fa-bell'
                }"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-auto text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(alert);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.gpaCalculator = new EnhancedGPACalculator();
});

// Global functions for modal handling
function closeShareModal() {
    document.getElementById('shareModal').classList.add('hidden');
    document.getElementById('generatedLink').classList.add('hidden');
    document.getElementById('shareForm').reset();
}

function copyLink() {
    const shareUrl = document.getElementById('shareUrl');
    shareUrl.select();
    document.execCommand('copy');

    if (window.gpaCalculator) {
        window.gpaCalculator.showAlert('تم نسخ الرابط', 'success');
    }
}