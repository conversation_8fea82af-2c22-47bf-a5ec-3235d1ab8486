<?php
// Admin protection script to be included in admin pages

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

// Get user role
$userRole = $_SESSION['admin_role'] ?? 'admin';

// Define which pages each role can access
$rolePermissions = [
    'admin' => [
        'admin_dashboard.php',
        'admin_students.php', 
        'admin_universities.php',
        'admin_courses.php',
        'admin_notifications.php',
        'admin_grading_scales.php',
        'admin_gpa_scales.php',
        'admin_settings.php'
    ],
    'publisher' => [
        'admin_courses.php'
    ]
];

// Get current page
$currentPage = basename($_SERVER['PHP_SELF']);

// Check if user has permission to access current page
if (!isset($rolePermissions[$userRole]) || !in_array($currentPage, $rolePermissions[$userRole])) {
    // Redirect to appropriate page based on role
    if ($userRole === 'publisher') {
        header('Location: admin_courses.php');
    } else {
        header('Location: admin_dashboard.php');
    }
    exit;
}
?>
