<?php
/**
 * Smart Database Fix
 * إصلاح ذكي لقاعدة البيانات
 */

session_start();

// Simple authentication
if (!isset($_SESSION['fix_auth']) && !isset($_POST['password'])) {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إصلاح ذكي لقاعدة البيانات</title>
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-100">
        <div class="min-h-screen flex items-center justify-center">
            <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
                <h1 class="text-2xl font-bold text-center mb-6">إصلاح ذكي لقاعدة البيانات</h1>
                <form method="POST">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                        <input type="password" name="password" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700">
                        بدء الإصلاح الذكي
                    </button>
                </form>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Check password
if (isset($_POST['password'])) {
    if ($_POST['password'] === 'admin123') {
        $_SESSION['fix_auth'] = true;
    } else {
        echo "<script>alert('كلمة مرور خاطئة'); window.location.href = 'smart_fix.php';</script>";
        exit;
    }
}

$updates = [];
$errors = [];

try {
    // Direct database connection
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $updates[] = "✅ تم الاتصال بقاعدة البيانات";

    // 1. Disable foreign key checks temporarily
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    $updates[] = "✅ تم تعطيل فحص المفاتيح الخارجية مؤقتاً";

    // 2. Check current database structure
    $updates[] = "<h3>1. فحص هيكل قاعدة البيانات الحالية</h3>";
    
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $updates[] = "📋 الجداول الموجودة: " . implode(', ', $tables);

    // 3. Handle universities table first
    $updates[] = "<h3>2. معالجة جدول الجامعات</h3>";
    
    // Check if universities table exists
    if (in_array('universities', $tables)) {
        // Check current structure
        $columns = $pdo->query("SHOW COLUMNS FROM universities")->fetchAll(PDO::FETCH_ASSOC);
        $columnNames = array_column($columns, 'Field');
        
        $updates[] = "📋 أعمدة جدول الجامعات: " . implode(', ', $columnNames);
        
        // Drop grading_system column if it exists with constraints
        if (in_array('grading_system', $columnNames)) {
            try {
                $pdo->exec("ALTER TABLE universities DROP COLUMN grading_system");
                $updates[] = "✅ تم حذف عمود grading_system القديم";
            } catch (Exception $e) {
                $updates[] = "⚠️ لم يتم حذف عمود grading_system: " . $e->getMessage();
            }
        }
    } else {
        // Create universities table
        $sql = "CREATE TABLE universities (
            id VARCHAR(50) PRIMARY KEY,
            name_ar VARCHAR(255) NOT NULL,
            name_en VARCHAR(255) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);
        $updates[] = "✅ تم إنشاء جدول universities";
    }

    // 4. Create/recreate grading system tables
    $updates[] = "<h3>3. إنشاء جداول نظام التقدير</h3>";
    
    // Drop existing grading tables
    $pdo->exec("DROP TABLE IF EXISTS grading_scales");
    $pdo->exec("DROP TABLE IF EXISTS grading_systems");
    $updates[] = "✅ تم حذف الجداول القديمة";

    // Create grading_systems table
    $sql = "CREATE TABLE grading_systems (
        id VARCHAR(50) PRIMARY KEY,
        name_ar VARCHAR(255) NOT NULL,
        name_en VARCHAR(255) NOT NULL,
        description_ar TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    $updates[] = "✅ تم إنشاء جدول grading_systems";

    // Create grading_scales table
    $sql = "CREATE TABLE grading_scales (
        id INT AUTO_INCREMENT PRIMARY KEY,
        grading_system_id VARCHAR(50) NOT NULL,
        grade VARCHAR(10) NOT NULL,
        points DECIMAL(3,2) NOT NULL,
        description_ar VARCHAR(255) NOT NULL,
        min_percentage DECIMAL(5,2) NOT NULL,
        max_percentage DECIMAL(5,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_grade_system (grading_system_id, grade)
    )";
    $pdo->exec($sql);
    $updates[] = "✅ تم إنشاء جدول grading_scales";

    // 5. Insert grading systems
    $updates[] = "<h3>4. إضافة أنظمة التقدير</h3>";
    
    $gradingSystems = [
        ['aou', 'نظام الجامعة العربية المفتوحة', 'Arab Open University System', 'نظام التقدير المعتمد في الجامعة العربية المفتوحة'],
        ['standard', 'النظام الأكاديمي المعياري', 'Standard Academic System', 'النظام الأكاديمي المعياري المستخدم في معظم الجامعات السعودية'],
        ['simple', 'النظام المبسط', 'Simple System', 'نظام تقدير مبسط']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO grading_systems (id, name_ar, name_en, description_ar) VALUES (?, ?, ?, ?)");
    
    foreach ($gradingSystems as $system) {
        $stmt->execute($system);
        $updates[] = "✅ تم إضافة نظام: {$system[1]}";
    }

    // 6. Insert grading scales
    $updates[] = "<h3>5. إضافة سلالم الدرجات</h3>";
    
    $allGrades = [
        'aou' => [
            ['A', 4.00, 'ممتاز', 90, 100],
            ['B+', 3.50, 'جيد جداً مرتفع', 85, 89],
            ['B', 3.00, 'جيد جداً', 80, 84],
            ['C+', 2.50, 'جيد مرتفع', 75, 79],
            ['C', 2.00, 'جيد', 70, 74],
            ['D+', 1.50, 'مقبول مرتفع', 65, 69],
            ['D', 1.00, 'مقبول', 60, 64],
            ['F', 0.00, 'راسب', 0, 59]
        ],
        'standard' => [
            ['A+', 4.00, 'ممتاز مرتفع', 95, 100],
            ['A', 4.00, 'ممتاز', 90, 94],
            ['B+', 3.50, 'جيد جداً مرتفع', 85, 89],
            ['B', 3.00, 'جيد جداً', 80, 84],
            ['C+', 2.50, 'جيد مرتفع', 75, 79],
            ['C', 2.00, 'جيد', 70, 74],
            ['D+', 1.50, 'مقبول مرتفع', 65, 69],
            ['D', 1.00, 'مقبول', 60, 64],
            ['F', 0.00, 'راسب', 0, 59]
        ],
        'simple' => [
            ['A', 4.00, 'ممتاز', 90, 100],
            ['B', 3.00, 'جيد جداً', 80, 89],
            ['C', 2.00, 'جيد', 70, 79],
            ['D', 1.00, 'مقبول', 60, 69],
            ['F', 0.00, 'راسب', 0, 59]
        ]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO grading_scales (grading_system_id, grade, points, description_ar, min_percentage, max_percentage) VALUES (?, ?, ?, ?, ?, ?)");
    
    foreach ($allGrades as $systemId => $grades) {
        foreach ($grades as $grade) {
            $stmt->execute([$systemId, $grade[0], $grade[1], $grade[2], $grade[3], $grade[4]]);
        }
        $updates[] = "✅ تم إضافة درجات نظام $systemId (" . count($grades) . " درجة)";
    }

    // 7. Add grading_system column to universities (without constraints)
    $updates[] = "<h3>6. إضافة عمود نظام التقدير للجامعات</h3>";
    
    $pdo->exec("ALTER TABLE universities ADD COLUMN grading_system VARCHAR(50) DEFAULT 'aou'");
    $updates[] = "✅ تم إضافة عمود grading_system";

    // 8. Insert/update universities
    $updates[] = "<h3>7. تحديث بيانات الجامعات</h3>";
    
    $universities = [
        ['aou', 'الجامعة العربية المفتوحة', 'Arab Open University', 'aou'],
        ['ksu', 'جامعة الملك سعود', 'King Saud University', 'standard'],
        ['kau', 'جامعة الملك عبدالعزيز', 'King Abdulaziz University', 'standard'],
        ['simple_uni', 'جامعة بنظام مبسط', 'Simple University', 'simple']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO universities (id, name_ar, name_en, grading_system, is_active) VALUES (?, ?, ?, ?, 1) ON DUPLICATE KEY UPDATE grading_system = VALUES(grading_system), name_ar = VALUES(name_ar), name_en = VALUES(name_en)");
    
    foreach ($universities as $uni) {
        $stmt->execute($uni);
        $updates[] = "✅ تم تحديث جامعة: {$uni[1]} - نظام التقدير: {$uni[3]}";
    }

    // 9. Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    $updates[] = "✅ تم تفعيل فحص المفاتيح الخارجية";

    // 10. Final verification
    $updates[] = "<h3>8. التحقق النهائي</h3>";
    
    $systemsCount = $pdo->query("SELECT COUNT(*) FROM grading_systems")->fetchColumn();
    $scalesCount = $pdo->query("SELECT COUNT(*) FROM grading_scales")->fetchColumn();
    $universitiesCount = $pdo->query("SELECT COUNT(*) FROM universities WHERE is_active = 1")->fetchColumn();
    
    $updates[] = "✅ عدد أنظمة التقدير: $systemsCount";
    $updates[] = "✅ عدد الدرجات: $scalesCount";
    $updates[] = "✅ عدد الجامعات النشطة: $universitiesCount";
    
    // Test query
    $testQuery = $pdo->query("
        SELECT u.name_ar as university, gs.name_ar as grading_system, COUNT(gsc.id) as grades_count 
        FROM universities u 
        LEFT JOIN grading_systems gs ON u.grading_system = gs.id 
        LEFT JOIN grading_scales gsc ON gs.id = gsc.grading_system_id 
        WHERE u.is_active = 1 
        GROUP BY u.id, gs.id
    ")->fetchAll();
    
    $updates[] = "<h4>اختبار الربط بين الجامعات وأنظمة التقدير:</h4>";
    foreach ($testQuery as $row) {
        $updates[] = "🔗 {$row['university']} ← {$row['grading_system']} ({$row['grades_count']} درجة)";
    }
    
    $updates[] = "🎉 تم إصلاح نظام التقدير بنجاح!";

} catch (Exception $e) {
    $errors[] = "❌ خطأ: " . $e->getMessage();
    $errors[] = "تفاصيل: " . $e->getFile() . " في السطر " . $e->getLine();
    
    // Try to re-enable foreign key checks even if there was an error
    try {
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    } catch (Exception $e2) {
        // Ignore
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح ذكي لقاعدة البيانات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h1 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-magic ml-2"></i>
                    إصلاح ذكي لقاعدة البيانات
                </h1>
                
                <?php if (!empty($updates)): ?>
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-green-600">
                        <i class="fas fa-check-circle ml-2"></i>
                        نتائج الإصلاح الذكي
                    </h2>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 max-h-96 overflow-y-auto">
                        <?php foreach ($updates as $update): ?>
                            <div class="mb-2 text-green-800"><?php echo $update; ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4 text-red-600">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        الأخطاء
                    </h2>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <?php foreach ($errors as $error): ?>
                            <div class="mb-2 text-red-800"><?php echo htmlspecialchars($error); ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="text-center mt-8">
                    <a href="index.php" class="inline-block bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors mr-4">
                        <i class="fas fa-home ml-2"></i>
                        الصفحة الرئيسية
                    </a>
                    
                    <a href="admin_universities.php" class="inline-block bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors mr-4">
                        <i class="fas fa-cog ml-2"></i>
                        إدارة الجامعات
                    </a>
                    
                    <a href="admin_students.php" class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-users ml-2"></i>
                        إدارة الطلاب
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
