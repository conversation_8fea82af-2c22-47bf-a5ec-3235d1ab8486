-- تحديث قاعدة البيانات الموجودة gpa_calculator
-- Update existing gpa_calculator database

USE gpa_calculator;

-- إضافة جدول المديرين الجديد
CREATE TABLE IF NOT EXISTS admins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    avatar VARCHAR(255) NULL,
    phone VARCHAR(20) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- تحديث جدول الطلاب الموجود
ALTER TABLE students 
ADD COLUMN IF NOT EXISTS email VARCHAR(100) NULL AFTER phone,
ADD COLUMN IF NOT EXISTS student_id VARCHAR(50) NULL AFTER email,
ADD COLUMN IF NOT EXISTS grading_system VARCHAR(50) NOT NULL DEFAULT 'aou' AFTER university,
ADD COLUMN IF NOT EXISTS semester_gpa DECIMAL(3,2) NULL AFTER grading_system,
ADD COLUMN IF NOT EXISTS cumulative_gpa DECIMAL(3,2) NULL AFTER semester_gpa,
ADD COLUMN IF NOT EXISTS total_hours INT DEFAULT 0 AFTER cumulative_gpa,
ADD COLUMN IF NOT EXISTS previous_gpa DECIMAL(3,2) NULL AFTER total_hours,
ADD COLUMN IF NOT EXISTS previous_hours INT DEFAULT 0 AFTER previous_gpa,
ADD COLUMN IF NOT EXISTS ip_address VARCHAR(45) NULL AFTER classification,
ADD COLUMN IF NOT EXISTS user_agent TEXT NULL AFTER ip_address,
ADD COLUMN IF NOT EXISTS share_link VARCHAR(100) UNIQUE NULL AFTER user_agent,
ADD COLUMN IF NOT EXISTS link_expires_at TIMESTAMP NULL AFTER share_link,
ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE AFTER link_expires_at,
ADD COLUMN IF NOT EXISTS notes TEXT NULL AFTER is_verified,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at;

-- تحديث نوع عمود gpa إلى cumulative_gpa إذا كان موجوداً
ALTER TABLE students CHANGE COLUMN gpa cumulative_gpa DECIMAL(3,2) NULL;

-- إضافة فهارس جديدة لجدول الطلاب
ALTER TABLE students 
ADD INDEX IF NOT EXISTS idx_university (university),
ADD INDEX IF NOT EXISTS idx_created_at (created_at),
ADD INDEX IF NOT EXISTS idx_share_link (share_link),
ADD INDEX IF NOT EXISTS idx_gpa (cumulative_gpa),
ADD INDEX IF NOT EXISTS idx_university_date (university, created_at);

-- إنشاء جدول المواد الدراسية الجديد
CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_name VARCHAR(100) NOT NULL,
    course_code VARCHAR(20) NULL,
    credit_hours INT NOT NULL,
    grade VARCHAR(5) NOT NULL,
    grade_points DECIMAL(3,2) NOT NULL,
    semester VARCHAR(20) NULL,
    year VARCHAR(10) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    INDEX idx_student_id (student_id),
    INDEX idx_grade (grade),
    INDEX idx_grade_points (grade_points)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول سجل العمليات
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NULL,
    student_id INT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT NULL,
    old_data JSON NULL,
    new_data JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE SET NULL,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE SET NULL,
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_admin_id (admin_id),
    INDEX idx_admin_action (admin_id, action),
    INDEX idx_student_id (student_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الإعدادات
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NULL,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    description TEXT NULL,
    category VARCHAR(50) DEFAULT 'general',
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES admins(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_category_public (category, is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الإحصائيات اليومية
CREATE TABLE IF NOT EXISTS daily_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE UNIQUE NOT NULL,
    total_calculations INT DEFAULT 0,
    unique_users INT DEFAULT 0,
    avg_gpa DECIMAL(3,2) NULL,
    total_students INT DEFAULT 0,
    new_students INT DEFAULT 0,
    universities_data JSON NULL,
    grades_distribution JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول التقارير المحفوظة
CREATE TABLE IF NOT EXISTS saved_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    report_type VARCHAR(50) NOT NULL,
    filters JSON NULL,
    created_by INT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_type (report_type),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_admin_read (admin_id, is_read),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات المديرين الافتراضية
INSERT IGNORE INTO admins (username, email, password, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام الرئيسي', 'super_admin'),
('moderator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مشرف النظام', 'moderator'),
('viewer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مراقب النظام', 'admin');

-- إدراج الإعدادات الافتراضية
INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description, category, is_public) VALUES 
('site_name', 'حاسبة المعدل التراكمي - الجامعة العربية المفتوحة', 'text', 'اسم الموقع', 'general', 1),
('site_description', 'نظام متطور لحساب المعدل التراكمي للطلاب', 'text', 'وصف الموقع', 'general', 1),
('admin_email', '<EMAIL>', 'text', 'البريد الإلكتروني للمدير', 'general', 0),
('default_language', 'ar', 'text', 'اللغة الافتراضية', 'general', 1),
('default_university', 'aou', 'text', 'الجامعة الافتراضية', 'general', 1),
('max_courses_per_calculation', '20', 'number', 'الحد الأقصى للمواد في الحساب الواحد', 'limits', 1),
('link_expiry_days', '30', 'number', 'مدة انتهاء صلاحية الروابط بالأيام', 'security', 1),
('enable_registration', '1', 'boolean', 'تفعيل التسجيل للطلاب', 'features', 1),
('enable_sharing', '1', 'boolean', 'تفعيل مشاركة النتائج', 'features', 1),
('maintenance_mode', '0', 'boolean', 'وضع الصيانة', 'system', 0),
('max_login_attempts', '5', 'number', 'الحد الأقصى لمحاولات تسجيل الدخول', 'security', 0),
('lockout_duration', '30', 'number', 'مدة الحظر بالدقائق', 'security', 0),
('session_timeout', '120', 'number', 'انتهاء صلاحية الجلسة بالدقائق', 'security', 0),
('backup_frequency', 'daily', 'text', 'تكرار النسخ الاحتياطي', 'system', 0),
('email_notifications', '1', 'boolean', 'تفعيل الإشعارات بالبريد الإلكتروني', 'notifications', 0);

-- إنشاء views للتقارير السريعة
CREATE OR REPLACE VIEW admin_dashboard_stats AS
SELECT 
    (SELECT COUNT(*) FROM students) as total_students,
    (SELECT COUNT(*) FROM students WHERE DATE(created_at) = CURDATE()) as today_students,
    (SELECT COUNT(*) FROM students WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) as week_students,
    (SELECT COUNT(*) FROM students WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as month_students,
    (SELECT ROUND(AVG(cumulative_gpa), 2) FROM students WHERE cumulative_gpa IS NOT NULL) as avg_gpa,
    (SELECT COUNT(DISTINCT university) FROM students) as total_universities,
    (SELECT COUNT(*) FROM courses) as total_courses,
    (SELECT COUNT(*) FROM activity_logs WHERE DATE(created_at) = CURDATE()) as today_activities;

CREATE OR REPLACE VIEW university_stats AS
SELECT 
    university,
    COUNT(*) as student_count,
    ROUND(AVG(cumulative_gpa), 2) as avg_gpa,
    MAX(cumulative_gpa) as max_gpa,
    MIN(cumulative_gpa) as min_gpa,
    SUM(CASE WHEN cumulative_gpa >= 3.75 THEN 1 ELSE 0 END) as excellent_count,
    SUM(CASE WHEN cumulative_gpa >= 3.25 AND cumulative_gpa < 3.75 THEN 1 ELSE 0 END) as very_good_high_count,
    SUM(CASE WHEN cumulative_gpa >= 2.75 AND cumulative_gpa < 3.25 THEN 1 ELSE 0 END) as very_good_count,
    SUM(CASE WHEN cumulative_gpa >= 2.25 AND cumulative_gpa < 2.75 THEN 1 ELSE 0 END) as good_high_count,
    SUM(CASE WHEN cumulative_gpa >= 2.0 AND cumulative_gpa < 2.25 THEN 1 ELSE 0 END) as good_count,
    SUM(CASE WHEN cumulative_gpa < 2.0 THEN 1 ELSE 0 END) as below_good_count,
    DATE(MAX(created_at)) as last_activity
FROM students 
WHERE cumulative_gpa IS NOT NULL
GROUP BY university
ORDER BY student_count DESC;

CREATE OR REPLACE VIEW recent_activities AS
SELECT 
    al.id,
    al.action,
    al.description,
    al.created_at,
    a.full_name as admin_name,
    s.name as student_name,
    al.ip_address
FROM activity_logs al
LEFT JOIN admins a ON al.admin_id = a.id
LEFT JOIN students s ON al.student_id = s.id
ORDER BY al.created_at DESC
LIMIT 50;

-- إدراج بيانات طلاب تجريبية محدثة
INSERT IGNORE INTO students (name, phone, university, cumulative_gpa, total_hours, classification, ip_address, created_at) VALUES
('أحمد محمد علي', '0501234567', 'aou', 3.85, 45, 'ممتاز', '*************', NOW() - INTERVAL 5 DAY),
('فاطمة أحمد سالم', '0507654321', 'aou', 3.45, 60, 'جيد جداً مرتفع', '*************', NOW() - INTERVAL 4 DAY),
('محمد سالم عبدالله', '0509876543', 'ksu', 2.95, 75, 'جيد جداً', '*************', NOW() - INTERVAL 3 DAY),
('نورا عبدالله أحمد', '0502468135', 'aou', 3.95, 30, 'ممتاز', '*************', NOW() - INTERVAL 2 DAY),
('خالد أحمد محمد', '0501357924', 'kau', 2.25, 90, 'جيد مرتفع', '*************', NOW() - INTERVAL 1 DAY),
('سارة محمد علي', '0505551234', 'aou', 3.65, 55, 'جيد جداً مرتفع', '*************', NOW()),
('عبدالرحمن سعد', '0506661234', 'imamu', 3.15, 80, 'جيد جداً', '*************', NOW()),
('مريم عبدالعزيز', '0507771234', 'kfupm', 3.88, 40, 'ممتاز', '*************', NOW());

-- إدراج مواد تجريبية للطلاب
INSERT IGNORE INTO courses (student_id, course_name, credit_hours, grade, grade_points) VALUES
(1, 'الرياضيات المتقدمة', 3, 'A', 4.0),
(1, 'الفيزياء العامة', 4, 'B+', 3.5),
(1, 'الكيمياء العامة', 3, 'A', 4.0),
(2, 'الأدب العربي', 3, 'B+', 3.5),
(2, 'التاريخ الإسلامي', 3, 'B', 3.0),
(2, 'الجغرافيا', 2, 'A', 4.0),
(3, 'الاقتصاد الجزئي', 3, 'B', 3.0),
(3, 'الإحصاء التطبيقي', 3, 'C+', 2.5),
(3, 'المحاسبة المالية', 4, 'B', 3.0);

-- تسجيل نشاط إعداد النظام
INSERT INTO activity_logs (action, description, ip_address, created_at) VALUES 
('system_setup', 'تم تحديث قاعدة البيانات وإضافة الجداول الجديدة', '127.0.0.1', NOW());

-- كلمة المرور الافتراضية للجميع: admin123
