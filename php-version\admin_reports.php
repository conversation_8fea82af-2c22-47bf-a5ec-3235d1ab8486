<?php
/**
 * Admin Reports Page
 * صفحة التقارير الإدارية
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$currentUser = [
    'id' => $_SESSION['admin_id'],
    'username' => $_SESSION['admin_username'] ?? 'admin',
    'role' => $_SESSION['admin_role'] ?? 'admin',
    'full_name' => $_SESSION['admin_name'] ?? 'مدير النظام'
];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get statistics
    $totalStudents = $pdo->query("SELECT COUNT(*) FROM students")->fetchColumn();
    $todayStudents = $pdo->query("SELECT COUNT(*) FROM students WHERE DATE(created_at) = CURDATE()")->fetchColumn();
    $avgGPA = $pdo->query("SELECT ROUND(AVG(cumulative_gpa), 2) FROM students WHERE cumulative_gpa IS NOT NULL")->fetchColumn();
    
    // Get university statistics
    $universityStats = $pdo->query("
        SELECT university, 
               COUNT(*) as student_count,
               ROUND(AVG(cumulative_gpa), 2) as avg_gpa,
               MAX(cumulative_gpa) as max_gpa,
               MIN(cumulative_gpa) as min_gpa
        FROM students 
        WHERE cumulative_gpa IS NOT NULL 
        GROUP BY university 
        ORDER BY student_count DESC
    ")->fetchAll();
    
    // Get grade distribution
    $gradeDistribution = $pdo->query("
        SELECT 
            CASE 
                WHEN cumulative_gpa >= 3.75 THEN 'ممتاز'
                WHEN cumulative_gpa >= 3.25 THEN 'جيد جداً مرتفع'
                WHEN cumulative_gpa >= 2.75 THEN 'جيد جداً'
                WHEN cumulative_gpa >= 2.25 THEN 'جيد مرتفع'
                WHEN cumulative_gpa >= 2.0 THEN 'جيد'
                ELSE 'مقبول'
            END as grade_category,
            COUNT(*) as count
        FROM students 
        WHERE cumulative_gpa IS NOT NULL 
        GROUP BY grade_category
        ORDER BY MIN(cumulative_gpa) DESC
    ")->fetchAll();
    
    // Get monthly registration stats
    $monthlyStats = $pdo->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as registrations
        FROM students 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month DESC
        LIMIT 12
    ")->fetchAll();
    
} catch (PDOException $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

// Handle export requests
if (isset($_GET['export'])) {
    $exportType = $_GET['export'];
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="report_' . $exportType . '_' . date('Y-m-d') . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    switch ($exportType) {
        case 'students':
            fputcsv($output, ['الاسم', 'الهاتف', 'الجامعة', 'المعدل التراكمي', 'إجمالي الساعات', 'التقدير', 'تاريخ التسجيل']);
            $students = $pdo->query("SELECT name, phone, university, cumulative_gpa, total_hours, classification, created_at FROM students ORDER BY created_at DESC")->fetchAll();
            foreach ($students as $student) {
                fputcsv($output, [
                    $student['name'],
                    $student['phone'],
                    $student['university'],
                    $student['cumulative_gpa'],
                    $student['total_hours'],
                    $student['classification'],
                    $student['created_at']
                ]);
            }
            break;
            
        case 'universities':
            fputcsv($output, ['الجامعة', 'عدد الطلاب', 'متوسط المعدل', 'أعلى معدل', 'أقل معدل']);
            foreach ($universityStats as $stat) {
                fputcsv($output, [
                    $stat['university'],
                    $stat['student_count'],
                    $stat['avg_gpa'],
                    $stat['max_gpa'],
                    $stat['min_gpa']
                ]);
            }
            break;
    }
    
    fclose($output);
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .stat-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg">
            <div class="p-6 border-b">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-graduation-cap text-white"></i>
                    </div>
                    <div class="mr-3">
                        <h2 class="text-lg font-semibold text-gray-800">لوحة الإدارة</h2>
                        <p class="text-sm text-gray-600">التقارير والإحصائيات</p>
                    </div>
                </div>
            </div>
            
            <nav class="mt-6">
                <a href="admin_dashboard.php" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-tachometer-alt ml-3"></i>
                    الرئيسية
                </a>
                
                <a href="admin_students.php" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-users ml-3"></i>
                    إدارة الطلاب
                </a>
                
                <a href="admin_reports.php" class="flex items-center px-6 py-3 text-blue-600 bg-blue-50 border-l-4 border-blue-600">
                    <i class="fas fa-chart-bar ml-3"></i>
                    التقارير
                </a>
                
                <a href="admin_settings.php" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-300">
                    <i class="fas fa-cog ml-3"></i>
                    الإعدادات
                </a>
            </nav>
            
            <div class="absolute bottom-0 w-64 p-6 border-t">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-gray-600"></i>
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-gray-800"><?php echo htmlspecialchars($currentUser['full_name']); ?></p>
                        <p class="text-xs text-gray-600"><?php echo htmlspecialchars($currentUser['role']); ?></p>
                    </div>
                    <a href="admin_logout.php" class="text-red-600 hover:text-red-800" title="تسجيل خروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">التقارير والإحصائيات</h1>
                        <p class="text-gray-600">تقارير شاملة عن أداء النظام والطلاب</p>
                    </div>
                    
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="relative">
                            <button onclick="toggleExportMenu()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-300">
                                <i class="fas fa-download ml-2"></i>
                                تصدير التقارير
                            </button>
                            
                            <div id="exportMenu" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
                                <a href="?export=students" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-t-lg">
                                    <i class="fas fa-users ml-2"></i>
                                    تقرير الطلاب
                                </a>
                                <a href="?export=universities" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-b-lg">
                                    <i class="fas fa-university ml-2"></i>
                                    تقرير الجامعات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Statistics Cards -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="stat-card bg-white p-6 rounded-lg shadow-lg">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-blue-600 text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <h3 class="text-lg font-semibold text-gray-800">إجمالي الطلاب</h3>
                                <p class="text-3xl font-bold text-blue-600"><?php echo number_format($totalStudents ?? 0); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card bg-white p-6 rounded-lg shadow-lg">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-user-plus text-green-600 text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <h3 class="text-lg font-semibold text-gray-800">طلاب اليوم</h3>
                                <p class="text-3xl font-bold text-green-600"><?php echo number_format($todayStudents ?? 0); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card bg-white p-6 rounded-lg shadow-lg">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                            </div>
                            <div class="mr-4">
                                <h3 class="text-lg font-semibold text-gray-800">متوسط المعدل</h3>
                                <p class="text-3xl font-bold text-purple-600"><?php echo $avgGPA ?? '0.00'; ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Grade Distribution Chart -->
                    <div class="bg-white p-6 rounded-lg shadow-lg">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">توزيع التقديرات</h3>
                        <div class="chart-container">
                            <canvas id="gradeChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- University Statistics Chart -->
                    <div class="bg-white p-6 rounded-lg shadow-lg">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">إحصائيات الجامعات</h3>
                        <div class="chart-container">
                            <canvas id="universityChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- University Statistics Table -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                    <div class="p-6 border-b bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-800">تفاصيل إحصائيات الجامعات</h3>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الجامعة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد الطلاب</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">متوسط المعدل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">أعلى معدل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">أقل معدل</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($universityStats)): ?>
                                    <?php foreach ($universityStats as $stat): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($stat['university']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($stat['student_count']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo $stat['avg_gpa']; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                                                <?php echo $stat['max_gpa']; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                                                <?php echo $stat['min_gpa']; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">لا توجد بيانات</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Monthly Registration Chart -->
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">إحصائيات التسجيل الشهرية</h3>
                    <div class="chart-container">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Grade Distribution Chart
        const gradeCtx = document.getElementById('gradeChart').getContext('2d');
        const gradeChart = new Chart(gradeCtx, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode(array_column($gradeDistribution, 'grade_category')); ?>,
                datasets: [{
                    data: <?php echo json_encode(array_column($gradeDistribution, 'count')); ?>,
                    backgroundColor: [
                        '#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444', '#6B7280'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // University Statistics Chart
        const universityCtx = document.getElementById('universityChart').getContext('2d');
        const universityChart = new Chart(universityCtx, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode(array_column($universityStats, 'university')); ?>,
                datasets: [{
                    label: 'عدد الطلاب',
                    data: <?php echo json_encode(array_column($universityStats, 'student_count')); ?>,
                    backgroundColor: '#3B82F6',
                    borderColor: '#1D4ED8',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Monthly Registration Chart
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_reverse(array_column($monthlyStats, 'month'))); ?>,
                datasets: [{
                    label: 'التسجيلات الشهرية',
                    data: <?php echo json_encode(array_reverse(array_column($monthlyStats, 'registrations'))); ?>,
                    borderColor: '#10B981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Export menu toggle
        function toggleExportMenu() {
            const menu = document.getElementById('exportMenu');
            menu.classList.toggle('hidden');
        }

        // Close export menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('exportMenu');
            const button = event.target.closest('button');
            
            if (!button || !button.onclick || button.onclick.toString().indexOf('toggleExportMenu') === -1) {
                menu.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
