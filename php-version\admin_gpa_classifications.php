<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['admin_id']) || $_SESSION['admin_role'] !== 'admin') {
    header('Location: simple_admin_login.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

$message = '';
$error = '';

// Create table if not exists
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS university_gpa_classifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            university_id VARCHAR(50) NOT NULL,
            min_gpa DECIMAL(3,2) NOT NULL,
            max_gpa DECIMAL(3,2) NOT NULL,
            classification_ar VARCHAR(100) NOT NULL,
            classification_en VARCHAR(100) NOT NULL,
            color_code VARCHAR(7) DEFAULT '#3B82F6',
            display_order INT DEFAULT 0,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_university_id (university_id),
            INDEX idx_gpa_range (min_gpa, max_gpa),
            FOREIGN KEY (university_id) REFERENCES universities(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
} catch (PDOException $e) {
    $error = 'خطأ في إنشاء الجدول: ' . $e->getMessage();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_classification':
                    $universityId = $_POST['university_id'] ?? '';
                    $minGpa = (float)($_POST['min_gpa'] ?? 0);
                    $maxGpa = (float)($_POST['max_gpa'] ?? 4);
                    $classificationAr = trim($_POST['classification_ar'] ?? '');
                    $classificationEn = trim($_POST['classification_en'] ?? '');
                    $colorCode = $_POST['color_code'] ?? '#3B82F6';
                    $displayOrder = (int)($_POST['display_order'] ?? 0);

                    if (empty($universityId) || empty($classificationAr) || empty($classificationEn)) {
                        $error = 'يرجى ملء جميع الحقول المطلوبة';
                    } elseif ($minGpa < 0 || $maxGpa > 4 || $minGpa >= $maxGpa) {
                        $error = 'نطاق المعدل غير صحيح';
                    } else {
                        // Check for overlapping ranges
                        $stmt = $pdo->prepare("
                            SELECT COUNT(*) FROM university_gpa_classifications 
                            WHERE university_id = ? AND (
                                (min_gpa <= ? AND max_gpa > ?) OR 
                                (min_gpa < ? AND max_gpa >= ?) OR
                                (min_gpa >= ? AND max_gpa <= ?)
                            )
                        ");
                        $stmt->execute([$universityId, $minGpa, $minGpa, $maxGpa, $maxGpa, $minGpa, $maxGpa]);
                        
                        if ($stmt->fetchColumn() > 0) {
                            $error = 'يوجد تداخل في نطاقات المعدل لهذه الجامعة';
                        } else {
                            $stmt = $pdo->prepare("
                                INSERT INTO university_gpa_classifications (university_id, min_gpa, max_gpa, classification_ar, classification_en, color_code, display_order)
                                VALUES (?, ?, ?, ?, ?, ?, ?)
                            ");
                            $stmt->execute([$universityId, $minGpa, $maxGpa, $classificationAr, $classificationEn, $colorCode, $displayOrder]);
                            $message = 'تم إضافة التصنيف بنجاح';
                        }
                    }
                    break;

                case 'edit_classification':
                    $id = (int)($_POST['classification_id'] ?? 0);
                    $universityId = $_POST['university_id'] ?? '';
                    $minGpa = (float)($_POST['min_gpa'] ?? 0);
                    $maxGpa = (float)($_POST['max_gpa'] ?? 4);
                    $classificationAr = trim($_POST['classification_ar'] ?? '');
                    $classificationEn = trim($_POST['classification_en'] ?? '');
                    $colorCode = $_POST['color_code'] ?? '#3B82F6';
                    $displayOrder = (int)($_POST['display_order'] ?? 0);

                    if ($id <= 0 || empty($universityId) || empty($classificationAr) || empty($classificationEn)) {
                        $error = 'يرجى ملء جميع الحقول المطلوبة';
                    } elseif ($minGpa < 0 || $maxGpa > 4 || $minGpa >= $maxGpa) {
                        $error = 'نطاق المعدل غير صحيح';
                    } else {
                        // Check for overlapping ranges (excluding current record)
                        $stmt = $pdo->prepare("
                            SELECT COUNT(*) FROM university_gpa_classifications 
                            WHERE university_id = ? AND id != ? AND (
                                (min_gpa <= ? AND max_gpa > ?) OR 
                                (min_gpa < ? AND max_gpa >= ?) OR
                                (min_gpa >= ? AND max_gpa <= ?)
                            )
                        ");
                        $stmt->execute([$universityId, $id, $minGpa, $minGpa, $maxGpa, $maxGpa, $minGpa, $maxGpa]);
                        
                        if ($stmt->fetchColumn() > 0) {
                            $error = 'يوجد تداخل في نطاقات المعدل لهذه الجامعة';
                        } else {
                            $stmt = $pdo->prepare("
                                UPDATE university_gpa_classifications 
                                SET university_id = ?, min_gpa = ?, max_gpa = ?, classification_ar = ?, classification_en = ?, 
                                    color_code = ?, display_order = ?
                                WHERE id = ?
                            ");
                            $stmt->execute([$universityId, $minGpa, $maxGpa, $classificationAr, $classificationEn, $colorCode, $displayOrder, $id]);
                            $message = 'تم تحديث التصنيف بنجاح';
                        }
                    }
                    break;

                case 'delete_classification':
                    $id = (int)($_POST['classification_id'] ?? 0);
                    if ($id <= 0) {
                        $error = 'معرف التصنيف غير صحيح';
                    } else {
                        $stmt = $pdo->prepare("DELETE FROM university_gpa_classifications WHERE id = ?");
                        $stmt->execute([$id]);
                        $message = 'تم حذف التصنيف بنجاح';
                    }
                    break;

                case 'add_default_classifications':
                    $universityId = $_POST['university_id'] ?? '';
                    
                    if (empty($universityId)) {
                        $error = 'يرجى اختيار الجامعة';
                        break;
                    }

                    // Default classifications for Saudi universities
                    $defaultClassifications = [
                        [4.00, 3.67, 'ممتاز', 'Excellent', '#10B981', 1],
                        [3.66, 3.00, 'جيد جداً', 'Very Good', '#3B82F6', 2],
                        [2.99, 2.33, 'جيد', 'Good', '#F59E0B', 3],
                        [2.32, 2.00, 'مقبول', 'Fair', '#F97316', 4],
                        [1.99, 0.00, 'راسب', 'Fail', '#EF4444', 5]
                    ];

                    // Clear existing classifications for this university
                    $stmt = $pdo->prepare("DELETE FROM university_gpa_classifications WHERE university_id = ?");
                    $stmt->execute([$universityId]);

                    $stmt = $pdo->prepare("
                        INSERT INTO university_gpa_classifications (university_id, min_gpa, max_gpa, classification_ar, classification_en, color_code, display_order)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");

                    foreach ($defaultClassifications as $classification) {
                        $stmt->execute([
                            $universityId,
                            $classification[1], // min_gpa
                            $classification[0], // max_gpa
                            $classification[2], // classification_ar
                            $classification[3], // classification_en
                            $classification[4], // color_code
                            $classification[5]  // display_order
                        ]);
                    }

                    $message = 'تم إضافة التصنيفات الافتراضية بنجاح';
                    break;

                case 'copy_classifications':
                    $sourceUniversityId = $_POST['source_university_id'] ?? '';
                    $targetUniversityId = $_POST['target_university_id'] ?? '';
                    
                    if (empty($sourceUniversityId) || empty($targetUniversityId)) {
                        $error = 'يرجى اختيار الجامعة المصدر والهدف';
                        break;
                    }

                    if ($sourceUniversityId === $targetUniversityId) {
                        $error = 'لا يمكن نسخ التصنيفات لنفس الجامعة';
                        break;
                    }

                    // Get classifications from source university
                    $stmt = $pdo->prepare("SELECT * FROM university_gpa_classifications WHERE university_id = ? ORDER BY display_order");
                    $stmt->execute([$sourceUniversityId]);
                    $sourceClassifications = $stmt->fetchAll();

                    if (empty($sourceClassifications)) {
                        $error = 'لا توجد تصنيفات في الجامعة المصدر';
                        break;
                    }

                    // Clear existing classifications for target university
                    $stmt = $pdo->prepare("DELETE FROM university_gpa_classifications WHERE university_id = ?");
                    $stmt->execute([$targetUniversityId]);

                    // Copy classifications to target university
                    $stmt = $pdo->prepare("
                        INSERT INTO university_gpa_classifications (university_id, min_gpa, max_gpa, classification_ar, classification_en, color_code, display_order)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");

                    foreach ($sourceClassifications as $classification) {
                        $stmt->execute([
                            $targetUniversityId,
                            $classification['min_gpa'],
                            $classification['max_gpa'],
                            $classification['classification_ar'],
                            $classification['classification_en'],
                            $classification['color_code'],
                            $classification['display_order']
                        ]);
                    }

                    $message = 'تم نسخ التصنيفات بنجاح';
                    break;
            }
        }
    } catch (PDOException $e) {
        $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    }
}

// Get data
try {
    // Get universities
    $universities = $pdo->query("SELECT id, name_ar FROM universities WHERE id != 'disabled' ORDER BY name_ar")->fetchAll();

    // Get classifications with university names
    $classifications = $pdo->query("
        SELECT ugc.*, u.name_ar as university_name 
        FROM university_gpa_classifications ugc 
        LEFT JOIN universities u ON ugc.university_id = u.id 
        ORDER BY u.name_ar, ugc.display_order, ugc.max_gpa DESC
    ")->fetchAll();

} catch (PDOException $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
    $universities = [];
    $classifications = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة تصنيف المعدل التراكمي - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-chart-bar text-purple-600 ml-3"></i>
                            إدارة تصنيف المعدل التراكمي
                        </h1>
                        <p class="text-gray-600 mt-1">إدارة وتخصيص تصنيفات المعدل لكل جامعة بشكل منفصل</p>
                    </div>
                    <button onclick="showAddModal()" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة تصنيف جديد
                    </button>
                </div>
            </header>

            <!-- Content -->
            <div class="p-6">
                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        <i class="fas fa-check-circle ml-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <i class="fas fa-exclamation-circle ml-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                    <h2 class="text-lg font-bold text-gray-800 mb-4">إجراءات سريعة</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Add Default Classifications -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">إضافة تصنيفات افتراضية</h3>
                            <form method="POST" class="space-y-3">
                                <input type="hidden" name="action" value="add_default_classifications">
                                <select name="university_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="">اختر الجامعة</option>
                                    <?php foreach ($universities as $university): ?>
                                        <option value="<?php echo htmlspecialchars($university['id']); ?>">
                                            <?php echo htmlspecialchars($university['name_ar']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="submit" class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-plus ml-2"></i>
                                    إضافة التصنيفات الافتراضية
                                </button>
                            </form>
                        </div>

                        <!-- Copy Classifications -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">نسخ التصنيفات</h3>
                            <form method="POST" class="space-y-3">
                                <input type="hidden" name="action" value="copy_classifications">
                                <select name="source_university_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="">من الجامعة</option>
                                    <?php foreach ($universities as $university): ?>
                                        <option value="<?php echo htmlspecialchars($university['id']); ?>">
                                            <?php echo htmlspecialchars($university['name_ar']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <select name="target_university_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="">إلى الجامعة</option>
                                    <?php foreach ($universities as $university): ?>
                                        <option value="<?php echo htmlspecialchars($university['id']); ?>">
                                            <?php echo htmlspecialchars($university['name_ar']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="submit" class="w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition-colors">
                                    <i class="fas fa-copy ml-2"></i>
                                    نسخ التصنيفات
                                </button>
                            </form>
                        </div>

                        <!-- Statistics -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">إحصائيات</h3>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span>إجمالي الجامعات:</span>
                                    <span class="font-semibold"><?php echo count($universities); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span>إجمالي التصنيفات:</span>
                                    <span class="font-semibold"><?php echo count($classifications); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span>متوسط التصنيفات لكل جامعة:</span>
                                    <span class="font-semibold"><?php echo count($universities) > 0 ? round(count($classifications) / count($universities), 1) : 0; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Classifications Table -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-bold text-gray-800">تصنيفات المعدل المسجلة</h2>
                        <p class="text-gray-600 text-sm mt-1">جميع تصنيفات المعدل المسجلة لكل جامعة</p>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الجامعة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">نطاق المعدل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التصنيف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اللون</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">ترتيب العرض</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($classifications)): ?>
                                    <tr>
                                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                            <i class="fas fa-chart-bar text-4xl mb-4 text-gray-300"></i>
                                            <p class="text-lg font-medium">لا توجد تصنيفات مسجلة</p>
                                            <p class="text-sm">ابدأ بإضافة التصنيفات الافتراضية أو إضافة تصنيف جديد</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($classifications as $classification): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($classification['university_name'] ?? 'غير محدد'); ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo number_format($classification['min_gpa'], 2); ?> - <?php echo number_format($classification['max_gpa'], 2); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white"
                                                      style="background-color: <?php echo htmlspecialchars($classification['color_code']); ?>">
                                                    <?php echo htmlspecialchars($classification['classification_ar']); ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="w-6 h-6 rounded-full border border-gray-300"
                                                     style="background-color: <?php echo htmlspecialchars($classification['color_code']); ?>"></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo $classification['display_order']; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button onclick="editClassification(<?php echo htmlspecialchars(json_encode($classification)); ?>)"
                                                        class="text-blue-600 hover:text-blue-900 ml-3">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button onclick="deleteClassification(<?php echo $classification['id']; ?>)"
                                                        class="text-red-600 hover:text-red-900">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Classification Modal -->
    <div id="classificationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 id="modalTitle" class="text-lg font-bold text-gray-900">إضافة تصنيف جديد</h3>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form id="classificationForm" method="POST" class="space-y-4">
                        <input type="hidden" id="classificationId" name="classification_id">
                        <input type="hidden" id="formAction" name="action" value="add_classification">

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الجامعة</label>
                            <select id="universityId" name="university_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                                <option value="">اختر الجامعة</option>
                                <?php foreach ($universities as $university): ?>
                                    <option value="<?php echo htmlspecialchars($university['id']); ?>">
                                        <?php echo htmlspecialchars($university['name_ar']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الحد الأدنى للمعدل</label>
                                <input type="number" id="minGpa" name="min_gpa" required step="0.01" min="0" max="4"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                                       placeholder="2.00">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الحد الأعلى للمعدل</label>
                                <input type="number" id="maxGpa" name="max_gpa" required step="0.01" min="0" max="4"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                                       placeholder="4.00">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">التصنيف بالعربية</label>
                            <input type="text" id="classificationAr" name="classification_ar" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                                   placeholder="ممتاز">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">التصنيف بالإنجليزية</label>
                            <input type="text" id="classificationEn" name="classification_en" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                                   placeholder="Excellent">
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">لون التصنيف</label>
                                <input type="color" id="colorCode" name="color_code"
                                       class="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                                       value="#3B82F6">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">ترتيب العرض</label>
                                <input type="number" id="displayOrder" name="display_order" min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                                       placeholder="1">
                            </div>
                        </div>

                        <div class="flex gap-4 pt-4">
                            <button type="submit" class="flex-1 bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition-colors">
                                <i class="fas fa-save ml-2"></i>
                                حفظ
                            </button>
                            <button type="button" onclick="closeModal()" class="flex-1 bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 transition-colors">
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center ml-3">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">تأكيد الحذف</h3>
                    </div>

                    <p class="text-gray-600 mb-6">هل أنت متأكد من حذف هذا التصنيف؟ لا يمكن التراجع عن هذا الإجراء.</p>

                    <form id="deleteForm" method="POST" class="flex gap-4">
                        <input type="hidden" name="action" value="delete_classification">
                        <input type="hidden" id="deleteClassificationId" name="classification_id">

                        <button type="submit" class="flex-1 bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash ml-2"></i>
                            حذف
                        </button>
                        <button type="button" onclick="closeDeleteModal()" class="flex-1 bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 transition-colors">
                            إلغاء
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة تصنيف جديد';
            document.getElementById('formAction').value = 'add_classification';
            document.getElementById('classificationForm').reset();
            document.getElementById('classificationId').value = '';
            document.getElementById('colorCode').value = '#3B82F6';
            document.getElementById('classificationModal').classList.remove('hidden');
        }

        function editClassification(classification) {
            document.getElementById('modalTitle').textContent = 'تعديل التصنيف';
            document.getElementById('formAction').value = 'edit_classification';
            document.getElementById('classificationId').value = classification.id;
            document.getElementById('universityId').value = classification.university_id;
            document.getElementById('minGpa').value = classification.min_gpa;
            document.getElementById('maxGpa').value = classification.max_gpa;
            document.getElementById('classificationAr').value = classification.classification_ar;
            document.getElementById('classificationEn').value = classification.classification_en;
            document.getElementById('colorCode').value = classification.color_code;
            document.getElementById('displayOrder').value = classification.display_order;
            document.getElementById('classificationModal').classList.remove('hidden');
        }

        function deleteClassification(classificationId) {
            document.getElementById('deleteClassificationId').value = classificationId;
            document.getElementById('deleteModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('classificationModal').classList.add('hidden');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.add('hidden');
        }

        // Close modals when clicking outside
        document.getElementById('classificationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });

        // Validate GPA range
        document.getElementById('minGpa').addEventListener('input', validateGpaRange);
        document.getElementById('maxGpa').addEventListener('input', validateGpaRange);

        function validateGpaRange() {
            const minGpa = parseFloat(document.getElementById('minGpa').value);
            const maxGpa = parseFloat(document.getElementById('maxGpa').value);

            if (minGpa && maxGpa && minGpa >= maxGpa) {
                document.getElementById('maxGpa').setCustomValidity('الحد الأعلى يجب أن يكون أكبر من الحد الأدنى');
            } else {
                document.getElementById('maxGpa').setCustomValidity('');
            }
        }
    </script>
</body>
</html>
