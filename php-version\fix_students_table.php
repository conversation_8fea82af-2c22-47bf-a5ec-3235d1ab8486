<?php
/**
 * Fix Students Table Foreign Key Issues
 * إصلاح مشاكل المفاتيح الخارجية في جدول الطلاب
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$fixes = [];
$errors = [];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $fixes[] = "✅ اتصال قاعدة البيانات ناجح";
    
    // Disable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    $fixes[] = "✅ تم تعطيل فحص المفاتيح الخارجية مؤقتاً";
    
    // Check current students table structure
    $stmt = $pdo->query("DESCRIBE students");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $fixes[] = "✅ تم فحص هيكل جدول الطلاب الحالي";
    
    // Drop problematic foreign key constraints
    $constraintsToRemove = [
        "ALTER TABLE students DROP FOREIGN KEY students_ibfk_1",
        "ALTER TABLE students DROP FOREIGN KEY students_ibfk_2",
        "ALTER TABLE students DROP FOREIGN KEY students_ibfk_3"
    ];
    
    foreach ($constraintsToRemove as $sql) {
        try {
            $pdo->exec($sql);
            $fixes[] = "✅ تم حذف قيد مفتاح خارجي من جدول الطلاب";
        } catch (PDOException $e) {
            // Constraint might not exist
            $fixes[] = "ℹ️ قيد مفتاح خارجي غير موجود (طبيعي)";
        }
    }
    
    // Create backup of students table
    $pdo->exec("DROP TABLE IF EXISTS students_backup");
    $pdo->exec("CREATE TABLE students_backup AS SELECT * FROM students");
    $fixes[] = "✅ تم إنشاء نسخة احتياطية من جدول الطلاب";
    
    // Drop and recreate students table without foreign key constraints
    $pdo->exec("DROP TABLE students");
    $pdo->exec("
        CREATE TABLE students (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            email VARCHAR(100) NULL,
            student_id VARCHAR(50) NULL,
            university VARCHAR(50) NOT NULL DEFAULT 'aou',
            grading_system VARCHAR(50) NOT NULL DEFAULT 'aou',
            semester_gpa DECIMAL(3,2) NULL,
            cumulative_gpa DECIMAL(3,2) NULL,
            total_hours INT DEFAULT 0,
            previous_gpa DECIMAL(3,2) NULL,
            previous_hours INT DEFAULT 0,
            classification VARCHAR(50) NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            share_link VARCHAR(100) UNIQUE NULL,
            link_expires_at TIMESTAMP NULL,
            is_verified BOOLEAN DEFAULT FALSE,
            notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_university (university),
            INDEX idx_created_at (created_at),
            INDEX idx_share_link (share_link),
            INDEX idx_gpa (cumulative_gpa),
            INDEX idx_phone (phone)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $fixes[] = "✅ تم إعادة إنشاء جدول الطلاب بدون قيود خارجية";
    
    // Restore data from backup
    $pdo->exec("INSERT INTO students SELECT * FROM students_backup");
    $fixes[] = "✅ تم استعادة بيانات الطلاب";
    
    // Drop backup table
    $pdo->exec("DROP TABLE students_backup");
    $fixes[] = "✅ تم حذف النسخة الاحتياطية";
    
    // Create universities table for future use
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS universities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(10) UNIQUE NOT NULL,
            name_ar VARCHAR(100) NOT NULL,
            name_en VARCHAR(100) NOT NULL,
            grading_system JSON NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_code (code),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $fixes[] = "✅ تم إنشاء جدول الجامعات للاستخدام المستقبلي";
    
    // Insert default universities
    $defaultUniversities = [
        ['aou', 'الجامعة العربية المفتوحة', 'Arab Open University', '{"A": 4.0, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D": 1.0, "F": 0.0}'],
        ['ksu', 'جامعة الملك سعود', 'King Saud University', '{"A+": 4.0, "A": 3.75, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D+": 1.5, "D": 1.0, "F": 0.0}'],
        ['kau', 'جامعة الملك عبدالعزيز', 'King Abdulaziz University', '{"A+": 4.0, "A": 3.75, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D+": 1.5, "D": 1.0, "F": 0.0}'],
        ['kfupm', 'جامعة الملك فهد للبترول والمعادن', 'King Fahd University of Petroleum and Minerals', '{"A": 4.0, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D+": 1.5, "D": 1.0, "F": 0.0}'],
        ['imamu', 'جامعة الإمام محمد بن سعود الإسلامية', 'Imam Mohammad Ibn Saud Islamic University', '{"A+": 4.0, "A": 3.75, "B+": 3.5, "B": 3.0, "C+": 2.5, "C": 2.0, "D+": 1.5, "D": 1.0, "F": 0.0}']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO universities (code, name_ar, name_en, grading_system) VALUES (?, ?, ?, ?)");
    $insertedUniversities = 0;
    foreach ($defaultUniversities as $uni) {
        if ($stmt->execute($uni)) {
            $insertedUniversities++;
        }
    }
    $fixes[] = "✅ تم إدراج $insertedUniversities جامعة افتراضية";
    
    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    $fixes[] = "✅ تم إعادة تفعيل فحص المفاتيح الخارجية";
    
    // Test student insertion
    $testData = [
        'طالب اختبار ' . date('H:i:s'),
        '050' . rand(1000000, 9999999),
        'aou',
        'aou',
        3.75,
        3.75,
        15,
        0,
        0,
        'ممتاز',
        '127.0.0.1',
        'Test Browser',
        uniqid() . '_' . time(),
        date('Y-m-d H:i:s', strtotime('+30 days'))
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO students (name, phone, university, grading_system, semester_gpa, cumulative_gpa, 
                            total_hours, previous_gpa, previous_hours, classification, ip_address, 
                            user_agent, share_link, link_expires_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    if ($stmt->execute($testData)) {
        $fixes[] = "✅ تم اختبار إدراج طالب جديد بنجاح";
    } else {
        $errors[] = "❌ فشل في اختبار إدراج طالب جديد";
    }
    
    // Log this fix
    try {
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (action, description, ip_address, created_at) 
            VALUES ('students_table_fix', 'تم إصلاح جدول الطلاب وإزالة قيود المفاتيح الخارجية', ?, NOW())
        ");
        $stmt->execute([$_SERVER['REMOTE_ADDR'] ?? '127.0.0.1']);
        $fixes[] = "✅ تم تسجيل عملية الإصلاح";
    } catch (Exception $e) {
        $errors[] = "تحذير: فشل في تسجيل عملية الإصلاح: " . $e->getMessage();
    }
    
} catch (PDOException $e) {
    $errors[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح جدول الطلاب</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-user-graduate text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">إصلاح جدول الطلاب</h1>
                <p class="text-gray-600">حل مشاكل المفاتيح الخارجية في جدول الطلاب</p>
            </div>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Fixes -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-green-700 mb-4">
                    <i class="fas fa-check-circle ml-2"></i>
                    الإصلاحات المكتملة (<?php echo count($fixes); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php foreach ($fixes as $index => $fix): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg text-sm" 
                             style="animation: slideIn 0.5s ease-out <?php echo $index * 0.1; ?>s both;">
                            <?php echo htmlspecialchars($fix); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Errors -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-red-700 mb-4">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    المشاكل المتبقية (<?php echo count($errors); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($errors)): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-thumbs-up ml-2"></i>
                                <span>تم إصلاح جميع المشاكل بنجاح!</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($errors as $error): ?>
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg text-sm">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- What was fixed -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">ما تم إصلاحه</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-gray-700 mb-2">جدول الطلاب (students)</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ إزالة جميع قيود المفاتيح الخارجية المشكلة</li>
                        <li>✅ إعادة إنشاء الجدول بهيكل محسن</li>
                        <li>✅ استعادة جميع البيانات الموجودة</li>
                        <li>✅ إضافة فهارس للأداء</li>
                        <li>✅ اختبار إدراج بيانات جديدة</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold text-gray-700 mb-2">جدول الجامعات (universities)</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>✅ إنشاء جدول جديد للجامعات</li>
                        <li>✅ إدراج الجامعات الافتراضية</li>
                        <li>✅ تعريف أنظمة التقدير لكل جامعة</li>
                        <li>✅ إعداد الجدول للاستخدام المستقبلي</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
            <?php if (empty($errors)): ?>
                <a href="test_student_save.php" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center">
                    <i class="fas fa-test-tube ml-2"></i>
                    اختبار حفظ الطلاب
                </a>
                
                <a href="admin_universities.php" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center">
                    <i class="fas fa-university ml-2"></i>
                    إدارة الجامعات
                </a>
                
                <a href="index.php" class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center">
                    <i class="fas fa-home ml-2"></i>
                    الصفحة الرئيسية
                </a>
            <?php else: ?>
                <button onclick="location.reload()" class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة المحاولة
                </button>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="mt-8 text-center text-sm text-gray-500">
            <p>تم إصلاح جدول الطلاب في: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p class="mt-1">الآن يمكن حفظ بيانات الطلاب بدون مشاكل!</p>
        </div>
    </div>

    <style>
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>

    <script>
        // Show success message
        <?php if (empty($errors)): ?>
        setTimeout(() => {
            alert('تم إصلاح جدول الطلاب بنجاح!\nيمكنك الآن حفظ بيانات الطلاب بدون مشاكل.');
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
