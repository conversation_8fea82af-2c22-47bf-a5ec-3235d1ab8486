<?php
/**
 * Fix All System Issues
 * إصلاح جميع مشاكل النظام
 */

session_start();

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$fixes = [];
$errors = [];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $fixes[] = "✅ اتصال قاعدة البيانات ناجح";
    
    // Fix 1: Ensure all required settings exist
    $defaultSettings = [
        'site_name' => 'حاسبة المعدل التراكمي - الجامعة العربية المفتوحة',
        'site_description' => 'نظام متطور لحساب المعدل التراكمي للطلاب',
        'admin_email' => '<EMAIL>',
        'default_language' => 'ar',
        'default_university' => 'aou',
        'max_courses_per_calculation' => '20',
        'link_expiry_days' => '30',
        'enable_registration' => '1',
        'enable_sharing' => '1',
        'maintenance_mode' => '0',
        'max_login_attempts' => '5',
        'lockout_duration' => '30',
        'session_timeout' => '120',
        'backup_frequency' => 'daily',
        'email_notifications' => '1'
    ];
    
    $insertedSettings = 0;
    foreach ($defaultSettings as $key => $value) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO settings (setting_key, setting_value, setting_type, description, category, is_public) 
                VALUES (?, ?, 'text', ?, 'general', 1) 
                ON DUPLICATE KEY UPDATE 
                setting_value = COALESCE(NULLIF(setting_value, ''), VALUES(setting_value))
            ");
            $stmt->execute([$key, $value, "إعداد $key"]);
            $insertedSettings++;
        } catch (PDOException $e) {
            $errors[] = "خطأ في إدراج الإعداد $key: " . $e->getMessage();
        }
    }
    $fixes[] = "✅ تم التحقق من $insertedSettings إعداد افتراضي";
    
    // Fix 2: Ensure admin passwords are correct
    $correctPasswordHash = password_hash('admin123', PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE username = 'admin'");
    if ($stmt->execute([$correctPasswordHash])) {
        $fixes[] = "✅ تم إصلاح كلمة مرور المدير الرئيسي";
    }
    
    // Fix 3: Initialize session variables if not set
    if (!isset($_SESSION['language'])) {
        $_SESSION['language'] = 'ar';
        $fixes[] = "✅ تم تعيين اللغة الافتراضية";
    }
    
    if (!isset($_SESSION['grading_system'])) {
        $_SESSION['grading_system'] = 'aou';
        $fixes[] = "✅ تم تعيين نظام التقدير الافتراضي";
    }
    
    if (!isset($_SESSION['university'])) {
        $_SESSION['university'] = 'aou';
        $fixes[] = "✅ تم تعيين الجامعة الافتراضية";
    }
    
    // Fix 4: Create data directory if not exists
    $dataDir = __DIR__ . '/data/';
    if (!is_dir($dataDir)) {
        if (mkdir($dataDir, 0755, true)) {
            $fixes[] = "✅ تم إنشاء مجلد البيانات";
        } else {
            $errors[] = "❌ فشل في إنشاء مجلد البيانات";
        }
    } else {
        $fixes[] = "✅ مجلد البيانات موجود";
    }
    
    // Fix 5: Test settings save/load functionality
    $testKey = 'system_test_' . time();
    $testValue = 'working_' . rand(1000, 9999);
    
    $stmt = $pdo->prepare("
        INSERT INTO settings (setting_key, setting_value, updated_by) 
        VALUES (?, ?, 1) 
        ON DUPLICATE KEY UPDATE 
        setting_value = VALUES(setting_value)
    ");
    
    if ($stmt->execute([$testKey, $testValue])) {
        // Verify it was saved
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$testKey]);
        $savedValue = $stmt->fetchColumn();
        
        if ($savedValue === $testValue) {
            $fixes[] = "✅ نظام حفظ الإعدادات يعمل بشكل صحيح";
            
            // Clean up test setting
            $pdo->prepare("DELETE FROM settings WHERE setting_key = ?")->execute([$testKey]);
        } else {
            $errors[] = "❌ مشكلة في نظام حفظ الإعدادات";
        }
    } else {
        $errors[] = "❌ فشل في اختبار حفظ الإعدادات";
    }
    
    // Fix 6: Check and fix table structure
    $requiredTables = ['admins', 'students', 'courses', 'activity_logs', 'settings'];
    foreach ($requiredTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $fixes[] = "✅ جدول '$table' موجود";
        } else {
            $errors[] = "❌ جدول '$table' غير موجود";
        }
    }
    
    // Fix 7: Test grading system functionality
    require_once 'functions_simple.php';
    
    $testCourses = [
        ['name' => 'اختبار', 'hours' => 3, 'grade' => 'A'],
        ['name' => 'اختبار 2', 'hours' => 2, 'grade' => 'B']
    ];
    
    try {
        $result = calculateGPA($testCourses);
        if (isset($result['gpa']) && $result['gpa'] > 0) {
            $fixes[] = "✅ نظام حساب المعدل يعمل بشكل صحيح";
        } else {
            $errors[] = "❌ مشكلة في نظام حساب المعدل";
        }
    } catch (Exception $e) {
        $errors[] = "❌ خطأ في نظام حساب المعدل: " . $e->getMessage();
    }
    
    // Fix 8: Log this fix session
    try {
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (action, description, ip_address, created_at) 
            VALUES ('system_fix', 'تم تشغيل سكريبت إصلاح النظام', ?, NOW())
        ");
        $stmt->execute([$_SERVER['REMOTE_ADDR'] ?? '127.0.0.1']);
        $fixes[] = "✅ تم تسجيل جلسة الإصلاح";
    } catch (Exception $e) {
        $errors[] = "❌ فشل في تسجيل جلسة الإصلاح: " . $e->getMessage();
    }
    
} catch (PDOException $e) {
    $errors[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل النظام</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .fix-item {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-tools text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">إصلاح مشاكل النظام</h1>
                <p class="text-gray-600">تشخيص وإصلاح جميع المشاكل في النظام</p>
            </div>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Fixes -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-green-700 mb-4 flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    الإصلاحات المكتملة (<?php echo count($fixes); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($fixes)): ?>
                        <div class="text-gray-500 text-center py-8">لا توجد إصلاحات</div>
                    <?php else: ?>
                        <?php foreach ($fixes as $index => $fix): ?>
                            <div class="fix-item bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg" 
                                 style="animation-delay: <?php echo $index * 0.1; ?>s">
                                <?php echo htmlspecialchars($fix); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Errors -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-red-700 mb-4 flex items-center">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    المشاكل المتبقية (<?php echo count($errors); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($errors)): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-thumbs-up ml-2"></i>
                                <span>لا توجد مشاكل! النظام يعمل بشكل مثالي</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($errors as $index => $error): ?>
                            <div class="fix-item bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg"
                                 style="animation-delay: <?php echo $index * 0.1; ?>s">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">حالة النظام</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-blue-50 p-4 rounded-lg text-center">
                    <i class="fas fa-database text-blue-600 text-2xl mb-2"></i>
                    <h3 class="font-semibold text-blue-800">قاعدة البيانات</h3>
                    <p class="text-sm text-blue-600"><?php echo empty($errors) ? 'تعمل بشكل صحيح' : 'تحتاج إصلاح'; ?></p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg text-center">
                    <i class="fas fa-cog text-green-600 text-2xl mb-2"></i>
                    <h3 class="font-semibold text-green-800">الإعدادات</h3>
                    <p class="text-sm text-green-600">تم التحقق والإصلاح</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg text-center">
                    <i class="fas fa-users text-purple-600 text-2xl mb-2"></i>
                    <h3 class="font-semibold text-purple-800">المديرين</h3>
                    <p class="text-sm text-purple-600">كلمات المرور محدثة</p>
                </div>
                
                <div class="bg-yellow-50 p-4 rounded-lg text-center">
                    <i class="fas fa-calculator text-yellow-600 text-2xl mb-2"></i>
                    <h3 class="font-semibold text-yellow-800">حساب المعدل</h3>
                    <p class="text-sm text-yellow-600">يعمل بشكل صحيح</p>
                </div>
            </div>
        </div>

        <!-- Session Info -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">معلومات الجلسة</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                    <span class="font-medium text-gray-700">اللغة:</span>
                    <span class="text-gray-600"><?php echo $_SESSION['language'] ?? 'غير محدد'; ?></span>
                </div>
                <div>
                    <span class="font-medium text-gray-700">نظام التقدير:</span>
                    <span class="text-gray-600"><?php echo $_SESSION['grading_system'] ?? 'غير محدد'; ?></span>
                </div>
                <div>
                    <span class="font-medium text-gray-700">الجامعة:</span>
                    <span class="text-gray-600"><?php echo $_SESSION['university'] ?? 'غير محدد'; ?></span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
            <?php if (empty($errors)): ?>
                <a href="index.php" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center">
                    <i class="fas fa-home ml-2"></i>
                    الصفحة الرئيسية
                </a>
                
                <a href="simple_admin_login.php" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل دخول الإدارة
                </a>
                
                <a href="admin_settings.php" class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center">
                    <i class="fas fa-cog ml-2"></i>
                    الإعدادات
                </a>
            <?php else: ?>
                <button onclick="location.reload()" class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-redo ml-2"></i>
                    إعادة الإصلاح
                </button>
                
                <a href="update_existing_database.php" class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-center">
                    <i class="fas fa-database ml-2"></i>
                    إصلاح قاعدة البيانات
                </a>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="mt-8 text-center text-sm text-gray-500">
            <p>تم تشغيل سكريبت الإصلاح في: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p>إجمالي الإصلاحات: <?php echo count($fixes); ?> | المشاكل المتبقية: <?php echo count($errors); ?></p>
        </div>
    </div>

    <script>
        // Auto refresh if there are errors
        <?php if (!empty($errors)): ?>
        setTimeout(() => {
            if (confirm('هل تريد إعادة تشغيل سكريبت الإصلاح؟')) {
                location.reload();
            }
        }, 5000);
        <?php endif; ?>
    </script>
</body>
</html>
