<?php
/**
 * Database Configuration
 * GPA Calculator - Enhanced with MySQL
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'gpa_calculator');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// PDO options
$pdo_options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
];

// Global PDO instance
$pdo = null;

/**
 * Get database connection
 */
function getDB() {
    global $pdo, $pdo_options;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $pdo_options);
        } catch (PDOException $e) {
            // Log error and show user-friendly message
            error_log("Database connection failed: " . $e->getMessage());
            die("خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.");
        }
    }
    
    return $pdo;
}

/**
 * Execute a prepared statement
 */
function executeQuery($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Query execution failed: " . $e->getMessage());
        throw new Exception("خطأ في تنفيذ الاستعلام");
    }
}

/**
 * Get single row
 */
function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetch();
}

/**
 * Get multiple rows
 */
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetchAll();
}

/**
 * Insert and get last insert ID
 */
function insertAndGetId($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return getDB()->lastInsertId();
}

/**
 * Get universities from database
 */
function getUniversities() {
    $sql = "SELECT * FROM universities ORDER BY name_ar";
    return fetchAll($sql);
}

/**
 * Get grading systems from database
 */
function getGradingSystems() {
    $sql = "SELECT gs.*, 
                   GROUP_CONCAT(
                       CONCAT(g.grade, ':', g.points, ':', g.min_percentage, '-', g.max_percentage, ':', g.description_ar)
                       ORDER BY g.points DESC SEPARATOR '|'
                   ) as grades_data
            FROM grading_systems gs
            LEFT JOIN grades g ON gs.id = g.grading_system_id
            GROUP BY gs.id
            ORDER BY gs.name_ar";
    
    $systems = fetchAll($sql);
    $result = [];
    
    foreach ($systems as $system) {
        $grades = [];
        if ($system['grades_data']) {
            $gradesArray = explode('|', $system['grades_data']);
            foreach ($gradesArray as $gradeData) {
                $parts = explode(':', $gradeData);
                if (count($parts) >= 4) {
                    $grades[$parts[0]] = [
                        'points' => floatval($parts[1]),
                        'range' => $parts[2],
                        'description' => $parts[3]
                    ];
                }
            }
        }
        
        $result[$system['id']] = [
            'name' => $system['name_ar'],
            'name_en' => $system['name_en'],
            'grades' => $grades
        ];
    }
    
    return $result;
}

/**
 * Save student and calculation data
 */
function saveStudentCalculation($studentData, $calculationData, $coursesData) {
    $db = getDB();
    
    try {
        $db->beginTransaction();
        
        // Check if student exists
        $student = fetchOne(
            "SELECT id FROM students WHERE phone = ?",
            [$studentData['phone']]
        );
        
        if ($student) {
            $studentId = $student['id'];
            // Update student info
            executeQuery(
                "UPDATE students SET name = ?, university_id = ?, updated_at = NOW() WHERE id = ?",
                [$studentData['name'], $studentData['university_id'], $studentId]
            );
        } else {
            // Insert new student
            $studentId = insertAndGetId(
                "INSERT INTO students (name, phone, university_id) VALUES (?, ?, ?)",
                [$studentData['name'], $studentData['phone'], $studentData['university_id']]
            );
        }
        
        // Insert calculation
        $calculationId = insertAndGetId(
            "INSERT INTO gpa_calculations (
                student_id, calculation_type, semester_gpa, cumulative_gpa, 
                total_hours, total_points, classification_ar, classification_en,
                previous_gpa, previous_hours, grading_system_id, calculation_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $studentId,
                $calculationData['type'],
                $calculationData['semester_gpa'],
                $calculationData['cumulative_gpa'],
                $calculationData['total_hours'],
                $calculationData['total_points'],
                $calculationData['classification_ar'],
                $calculationData['classification_en'],
                $calculationData['previous_gpa'],
                $calculationData['previous_hours'],
                $calculationData['grading_system_id'],
                date('Y-m-d')
            ]
        );
        
        // Insert courses
        foreach ($coursesData as $index => $course) {
            executeQuery(
                "INSERT INTO courses (calculation_id, course_name, credit_hours, grade, grade_points, course_order) 
                 VALUES (?, ?, ?, ?, ?, ?)",
                [
                    $calculationId,
                    $course['name'],
                    $course['hours'],
                    $course['grade'],
                    $course['grade_points'],
                    $index + 1
                ]
            );
        }
        
        $db->commit();
        return $calculationId;
        
    } catch (Exception $e) {
        $db->rollBack();
        error_log("Save calculation failed: " . $e->getMessage());
        throw new Exception("فشل في حفظ البيانات");
    }
}

/**
 * Create shared link
 */
function createSharedLink($calculationId, $expiryDays = 30) {
    $linkId = bin2hex(random_bytes(16));
    $expiryDate = date('Y-m-d H:i:s', strtotime("+{$expiryDays} days"));
    
    executeQuery(
        "INSERT INTO shared_links (id, calculation_id, expires_at) VALUES (?, ?, ?)",
        [$linkId, $calculationId, $expiryDate]
    );
    
    return $linkId;
}

/**
 * Get shared calculation data
 */
function getSharedCalculation($linkId) {
    $sql = "SELECT gc.*, s.name as student_name, s.phone, u.name_ar as university_name,
                   sl.access_count, sl.max_access, sl.expires_at
            FROM shared_links sl
            JOIN gpa_calculations gc ON sl.calculation_id = gc.id
            JOIN students s ON gc.student_id = s.id
            JOIN universities u ON s.university_id = u.id
            WHERE sl.id = ? AND sl.is_active = 1 
            AND (sl.expires_at IS NULL OR sl.expires_at > NOW())
            AND sl.access_count < sl.max_access";
    
    $data = fetchOne($sql, [$linkId]);
    
    if ($data) {
        // Increment access count
        executeQuery(
            "UPDATE shared_links SET access_count = access_count + 1 WHERE id = ?",
            [$linkId]
        );
        
        // Get courses
        $courses = fetchAll(
            "SELECT * FROM courses WHERE calculation_id = ? ORDER BY course_order",
            [$data['id']]
        );
        
        $data['courses'] = $courses;
    }
    
    return $data;
}

/**
 * Get admin statistics
 */
function getAdminStatistics() {
    $stats = [];
    
    // Total users
    $stats['total_users'] = fetchOne("SELECT COUNT(*) as count FROM students")['count'];
    
    // Total calculations
    $stats['total_calculations'] = fetchOne("SELECT COUNT(*) as count FROM gpa_calculations")['count'];
    
    // Average GPA
    $avgData = fetchOne("SELECT AVG(semester_gpa) as avg_semester, AVG(cumulative_gpa) as avg_cumulative FROM gpa_calculations");
    $stats['avg_gpa'] = round($avgData['avg_semester'] ?? 0, 2);
    
    // Today's calculations
    $stats['today_calculations'] = fetchOne(
        "SELECT COUNT(*) as count FROM gpa_calculations WHERE DATE(created_at) = CURDATE()"
    )['count'];
    
    // Grade distribution
    $gradeDistribution = fetchAll(
        "SELECT classification_ar, COUNT(*) as count 
         FROM gpa_calculations 
         WHERE classification_ar IS NOT NULL 
         GROUP BY classification_ar"
    );
    
    $stats['grade_distribution'] = [];
    foreach ($gradeDistribution as $grade) {
        $stats['grade_distribution'][$grade['classification_ar']] = $grade['count'];
    }
    
    // University distribution
    $universityDistribution = fetchAll(
        "SELECT u.name_ar, COUNT(DISTINCT s.id) as count
         FROM universities u
         LEFT JOIN students s ON u.id = s.university_id
         GROUP BY u.id, u.name_ar"
    );
    
    $stats['university_distribution'] = [];
    foreach ($universityDistribution as $uni) {
        $stats['university_distribution'][$uni['name_ar']] = $uni['count'];
    }
    
    return $stats;
}

/**
 * Get students data for admin
 */
function getStudentsData($limit = 100, $offset = 0) {
    $sql = "SELECT s.name, s.phone, u.name_ar as university_name,
                   gc.semester_gpa, gc.cumulative_gpa, gc.classification_ar,
                   gc.calculation_date, gc.created_at,
                   COUNT(c.id) as courses_count
            FROM students s
            JOIN universities u ON s.university_id = u.id
            LEFT JOIN gpa_calculations gc ON s.id = gc.student_id
            LEFT JOIN courses c ON gc.id = c.calculation_id
            GROUP BY s.id, gc.id
            ORDER BY gc.created_at DESC
            LIMIT ? OFFSET ?";
    
    return fetchAll($sql, [$limit, $offset]);
}

/**
 * Initialize database if needed
 */
function initializeDatabase() {
    try {
        // Check if database exists
        $db = getDB();
        $tables = fetchAll("SHOW TABLES");
        
        if (empty($tables)) {
            // Database is empty, run initialization
            $sql = file_get_contents(__DIR__ . '/database.sql');
            if ($sql) {
                $db->exec($sql);
                return true;
            }
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Database initialization failed: " . $e->getMessage());
        return false;
    }
}

// Initialize database on first load
if (!isset($_SESSION['db_initialized'])) {
    if (initializeDatabase()) {
        $_SESSION['db_initialized'] = true;
    }
}
?>
