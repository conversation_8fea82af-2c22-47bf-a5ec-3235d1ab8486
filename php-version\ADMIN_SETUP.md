# 🔧 دليل إعداد لوحة الإدارة - حاسبة المعدل التراكمي

## 📋 نظرة عامة

تم تطوير نظام إدارة متقدم لحاسبة المعدل التراكمي يتضمن:
- **نظام مصادقة آمن** مع أدوار متعددة
- **قاعدة بيانات MySQL** لتخزين البيانات
- **لوحة إدارة شاملة** مع إحصائيات متقدمة
- **نظام صلاحيات متدرج** (Super Admin, Admin, Moderator)
- **تسجيل جميع الأنشطة** والعمليات

## 🚀 خطوات الإعداد

### 1. متطلبات النظام

- **خادم ويب**: Apache/Nginx
- **PHP 7.4+** مع الإضافات التالية:
  - PDO MySQL
  - JSON
  - Session
- **MySQL 5.7+** أو **MariaDB 10.2+**
- **صلاحيات كتابة** على مجلدات المشروع

### 2. إعداد قاعدة البيانات

#### الطريقة الأولى: الإعداد التلقائي (مُوصى به)

1. **افتح متصفح الويب** واذهب إلى:
   ```
   http://localhost/gpa/php-version/setup_database.php
   ```

2. **اتبع التعليمات** على الشاشة
3. **تحقق من النتائج** والرسائل
4. **انقر على "تسجيل دخول الإدارة"** عند اكتمال الإعداد

#### الطريقة الثانية: الإعداد اليدوي

1. **أنشئ قاعدة بيانات جديدة**:
   ```sql
   CREATE DATABASE gpa_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **استورد ملف SQL**:
   ```bash
   mysql -u root -p gpa_admin < admin_database.sql
   ```

3. **تحديث إعدادات الاتصال** في `config/database.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'gpa_admin');
   define('DB_USER', 'root');
   define('DB_PASS', 'your_password');
   ```

### 3. إعداد الصلاحيات

تأكد من أن المجلدات التالية لديها صلاحيات كتابة:
```bash
chmod 755 config/
chmod 644 config/*.php
chmod 755 data/
chmod 755 exports/
```

### 4. اختبار النظام

1. **افتح صفحة تسجيل الدخول**:
   ```
   http://localhost/gpa/php-version/admin_login.php
   ```

2. **استخدم البيانات الافتراضية**:
   - **المستخدم**: `admin`
   - **كلمة المرور**: `admin123`

3. **تحقق من لوحة الإدارة**:
   ```
   http://localhost/gpa/php-version/admin_dashboard.php
   ```

## 👥 أدوار المستخدمين

### Super Admin (المدير الرئيسي)
- **جميع الصلاحيات** بدون قيود
- إدارة المديرين الآخرين
- تعديل إعدادات النظام
- الوصول لجميع البيانات والتقارير
- إجراء نسخ احتياطي واستعادة

### Admin (مدير)
- إدارة بيانات الطلاب
- عرض التقارير والإحصائيات
- إدارة الجامعات وأنظمة التقدير
- تصدير البيانات
- عرض سجل الأنشطة

### Moderator (مشرف)
- عرض لوحة الإدارة
- إدارة بيانات الطلاب (محدود)
- عرض التقارير الأساسية
- لا يمكنه حذف البيانات

## 🔐 الحسابات الافتراضية

| المستخدم | كلمة المرور | الدور | البريد الإلكتروني |
|----------|-------------|-------|------------------|
| admin | admin123 | Super Admin | <EMAIL> |
| moderator | admin123 | Moderator | <EMAIL> |
| viewer | admin123 | Admin | <EMAIL> |

⚠️ **تحذير**: يجب تغيير كلمات المرور الافتراضية فوراً بعد الإعداد!

## 📊 ميزات لوحة الإدارة

### الصفحة الرئيسية
- **إحصائيات سريعة**: عدد الطلاب، متوسط المعدل، الأنشطة
- **رسوم بيانية**: توزيع التقديرات، إحصائيات الجامعات
- **الأنشطة الأخيرة**: سجل العمليات الحديثة

### إدارة الطلاب
- عرض قائمة الطلاب مع البحث والفلترة
- تفاصيل كل طالب ومواده
- تعديل وحذف البيانات
- تصدير البيانات

### التقارير
- تقارير شاملة حسب الجامعة
- إحصائيات التقديرات
- تقارير زمنية (يومية، شهرية)
- تصدير التقارير بصيغ مختلفة

### الإعدادات
- إعدادات عامة للنظام
- إدارة الجامعات وأنظمة التقدير
- إعدادات الأمان
- إعدادات الإشعارات

## 🔧 التخصيص والتطوير

### إضافة مدير جديد

```php
// في قاعدة البيانات
INSERT INTO admins (username, email, password, full_name, role) VALUES 
('new_admin', '<EMAIL>', '$2y$10$...', 'اسم المدير', 'admin');
```

### تخصيص الصلاحيات

عدّل ملف `config/auth.php` في دالة `getRolePermissions()`:

```php
private function getRolePermissions($role) {
    $permissions = [
        'custom_role' => [
            'view_dashboard', 'custom_permission'
        ]
    ];
    return $permissions[$role] ?? [];
}
```

### إضافة إعدادات جديدة

```php
// إضافة إعداد جديد
setSetting('new_setting', 'default_value', $adminId);

// قراءة الإعداد
$value = getSetting('new_setting', 'fallback_value');
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```
الحل: تحقق من إعدادات قاعدة البيانات في config/database.php
```

#### 2. صفحة بيضاء أو خطأ 500
```
الحل: فعّل عرض الأخطاء في PHP وتحقق من سجل الأخطاء
```

#### 3. لا يمكن تسجيل الدخول
```
الحل: تحقق من كلمة المرور وحالة الحساب في قاعدة البيانات
```

#### 4. البيانات لا تظهر
```
الحل: تحقق من صلاحيات قاعدة البيانات وتشغيل setup_database.php
```

### تفعيل وضع التطوير

في `config/database.php`:
```php
// إضافة في بداية الملف للتطوير فقط
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

## 📁 هيكل الملفات

```
php-version/
├── config/
│   ├── database.php          # إعدادات قاعدة البيانات
│   └── auth.php             # نظام المصادقة
├── admin_login.php          # صفحة تسجيل الدخول
├── admin_dashboard.php      # لوحة الإدارة الرئيسية
├── admin_logout.php         # تسجيل الخروج
├── setup_database.php       # إعداد قاعدة البيانات
├── admin_database.sql       # ملف قاعدة البيانات
└── ADMIN_SETUP.md          # هذا الملف
```

## 🔒 الأمان

### إرشادات الأمان المهمة

1. **غيّر كلمات المرور الافتراضية** فوراً
2. **استخدم HTTPS** في الإنتاج
3. **قم بنسخ احتياطي منتظم** لقاعدة البيانات
4. **راقب سجل الأنشطة** بانتظام
5. **حدّث النظام** عند توفر تحديثات

### إعدادات الأمان الموصى بها

```php
// في config/auth.php
'max_login_attempts' => 5,
'lockout_duration' => 30,    // دقيقة
'session_timeout' => 120,    // دقيقة
'password_min_length' => 8,
```

## 📞 الدعم والمساعدة

### للحصول على المساعدة:

1. **تحقق من سجل الأخطاء** في PHP
2. **راجع قاعدة البيانات** للتأكد من البيانات
3. **اختبر الاتصال** بقاعدة البيانات
4. **تحقق من الصلاحيات** على الملفات والمجلدات

### معلومات إضافية:

- **الإصدار**: 1.0.0
- **التوافق**: PHP 7.4+, MySQL 5.7+
- **الترخيص**: مفتوح المصدر
- **التطوير**: الجامعة العربية المفتوحة

---

**تم إنشاء هذا النظام بـ ❤️ لخدمة الطلاب والجامعات العربية**
