<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

// Check user role and redirect publishers to their own dashboard
$userRole = $_SESSION['admin_role'] ?? 'admin';
if ($userRole === 'publisher') {
    header('Location: publisher_dashboard.php');
    exit;
}

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        switch ($_POST['action']) {
            case 'add':
                $stmt = $pdo->prepare("INSERT INTO course_catalog (course_code, course_title_ar, course_title_en, prerequisites, credit_hours, course_description_ar, course_description_en, course_objectives_ar, course_objectives_en, course_outcomes_ar, course_outcomes_en, university_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $_POST['course_code'],
                    $_POST['course_title_ar'],
                    $_POST['course_title_en'],
                    $_POST['prerequisites'],
                    $_POST['credit_hours'],
                    $_POST['course_description_ar'],
                    $_POST['course_description_en'],
                    $_POST['course_objectives_ar'],
                    $_POST['course_objectives_en'],
                    $_POST['course_outcomes_ar'],
                    $_POST['course_outcomes_en'],
                    $_POST['university_id']
                ]);
                echo json_encode(['success' => true, 'message' => 'تم إضافة المادة بنجاح']);
                break;
                
            case 'edit':
                $stmt = $pdo->prepare("UPDATE course_catalog SET course_code = ?, course_title_ar = ?, course_title_en = ?, prerequisites = ?, credit_hours = ?, course_description_ar = ?, course_description_en = ?, course_objectives_ar = ?, course_objectives_en = ?, course_outcomes_ar = ?, course_outcomes_en = ?, university_id = ? WHERE id = ?");
                $stmt->execute([
                    $_POST['course_code'],
                    $_POST['course_title_ar'],
                    $_POST['course_title_en'],
                    $_POST['prerequisites'],
                    $_POST['credit_hours'],
                    $_POST['course_description_ar'],
                    $_POST['course_description_en'],
                    $_POST['course_objectives_ar'],
                    $_POST['course_objectives_en'],
                    $_POST['course_outcomes_ar'],
                    $_POST['course_outcomes_en'],
                    $_POST['university_id'],
                    $_POST['id']
                ]);
                echo json_encode(['success' => true, 'message' => 'تم تحديث المادة بنجاح']);
                break;
                
            case 'delete':
                $stmt = $pdo->prepare("DELETE FROM course_catalog WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                echo json_encode(['success' => true, 'message' => 'تم حذف المادة بنجاح']);
                break;
                
            case 'toggle_status':
                $stmt = $pdo->prepare("UPDATE course_catalog SET is_active = !is_active WHERE id = ?");
                $stmt->execute([$_POST['id']]);
                echo json_encode(['success' => true, 'message' => 'تم تغيير حالة المادة بنجاح']);
                break;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ: ' . $e->getMessage()]);
    }
    exit();
}

// Get courses and universities for display
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get courses with university names
    $stmt = $pdo->query("
        SELECT cc.*, u.name_ar as university_name 
        FROM course_catalog cc 
        LEFT JOIN universities u ON cc.university_id = u.id 
        ORDER BY cc.course_code
    ");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get universities for dropdown
    $stmt = $pdo->query("SELECT id, name_ar FROM universities WHERE id != 'disabled' ORDER BY name_ar");
    $universities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المواد الدراسية - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <?php if ($userRole === 'publisher'): ?>
        <!-- Publisher Layout - No Sidebar -->
        <div class="min-h-screen">
            <!-- Publisher Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-3">
                            <i class="fas fa-graduation-cap text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-800">نشر المواد</h1>
                            <p class="text-gray-600">إدارة ونشر المواد الدراسية</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center ml-2">
                                <i class="fas fa-user text-gray-600"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-800"><?php echo htmlspecialchars($_SESSION['admin_name'] ?? $_SESSION['admin_username'] ?? 'ناشر المواد'); ?></p>
                                <p class="text-xs text-gray-600">ناشر المواد</p>
                            </div>
                        </div>
                        <a href="admin_logout.php" class="text-red-600 hover:text-red-800 px-3 py-2 rounded-lg hover:bg-red-50 transition-colors" title="تسجيل خروج">
                            <i class="fas fa-sign-out-alt ml-1"></i>
                            خروج
                        </a>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <div class="p-6">
    <?php else: ?>
        <!-- Admin Layout - With Sidebar -->
        <div class="flex h-screen">
            <?php include 'admin_sidebar.php'; ?>

            <!-- Main Content -->
            <div class="flex-1 overflow-auto">
                <!-- Header -->
                <header class="bg-white shadow-sm border-b p-6">
    <?php endif; ?>

    <?php if ($userRole !== 'publisher'): // Only show admin header for non-publishers ?>
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إدارة المواد الدراسية</h1>
                        <p class="text-gray-600">إضافة وتعديل وحذف المواد الدراسية</p>
                    </div>
                    <button onclick="openAddModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة مادة جديدة
                    </button>
                </div>
                </header>
    <?php endif; ?>

            <!-- Main Content -->
            <main class="p-6">
                <!-- Courses Table -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">كود المادة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم المادة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الساعات المعتمدة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المتطلبات السابقة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الجامعة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (isset($courses) && count($courses) > 0): ?>
                                    <?php foreach ($courses as $course): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($course['course_code']); ?>
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-900">
                                                <div class="font-medium"><?php echo htmlspecialchars($course['course_title_ar']); ?></div>
                                                <div class="text-gray-500 text-xs"><?php echo htmlspecialchars($course['course_title_en']); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo $course['credit_hours']; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo $course['prerequisites'] ? htmlspecialchars($course['prerequisites']) : '-'; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo htmlspecialchars($course['university_name'] ?? 'عام'); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $course['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                                    <?php echo $course['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button onclick="editCourse(<?php echo htmlspecialchars(json_encode($course)); ?>)" class="text-blue-600 hover:text-blue-900 ml-2">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button onclick="toggleCourseStatus(<?php echo $course['id']; ?>)" class="text-yellow-600 hover:text-yellow-900 ml-2">
                                                    <i class="fas fa-toggle-<?php echo $course['is_active'] ? 'on' : 'off'; ?>"></i>
                                                </button>
                                                <button onclick="deleteCourse(<?php echo $course['id']; ?>)" class="text-red-600 hover:text-red-900">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            لا توجد مواد دراسية مسجلة
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add/Edit Course Modal -->
    <div id="courseModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 id="modalTitle" class="text-lg font-medium text-gray-900">إضافة مادة جديدة</h3>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="courseForm" class="space-y-4">
                        <input type="hidden" id="courseId" name="id">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">كود المادة</label>
                                <input type="text" id="courseCode" name="course_code" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الساعات المعتمدة</label>
                                <input type="number" id="creditHours" name="credit_hours" min="1" max="12" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">اسم المادة (عربي)</label>
                                <input type="text" id="courseTitleAr" name="course_title_ar" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">اسم المادة (إنجليزي)</label>
                                <input type="text" id="courseTitleEn" name="course_title_en" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">المتطلبات السابقة</label>
                                <input type="text" id="prerequisites" name="prerequisites" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الجامعة</label>
                                <select id="universityId" name="university_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">عام (جميع الجامعات)</option>
                                    <?php foreach ($universities as $university): ?>
                                        <option value="<?php echo $university['id']; ?>"><?php echo htmlspecialchars($university['name_ar']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">وصف المادة (عربي)</label>
                                <textarea id="courseDescriptionAr" name="course_description_ar" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">وصف المادة (إنجليزي)</label>
                                <textarea id="courseDescriptionEn" name="course_description_en" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                            <button type="button" onclick="closeModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                                إلغاء
                            </button>
                            <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                                حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isEditMode = false;

        function openAddModal() {
            isEditMode = false;
            document.getElementById('modalTitle').textContent = 'إضافة مادة جديدة';
            document.getElementById('courseForm').reset();
            document.getElementById('courseId').value = '';
            document.getElementById('courseModal').classList.remove('hidden');
        }

        function editCourse(course) {
            isEditMode = true;
            document.getElementById('modalTitle').textContent = 'تعديل المادة';
            
            // Fill form with course data
            document.getElementById('courseId').value = course.id;
            document.getElementById('courseCode').value = course.course_code;
            document.getElementById('courseTitleAr').value = course.course_title_ar;
            document.getElementById('courseTitleEn').value = course.course_title_en;
            document.getElementById('prerequisites').value = course.prerequisites || '';
            document.getElementById('creditHours').value = course.credit_hours;
            document.getElementById('courseDescriptionAr').value = course.course_description_ar || '';
            document.getElementById('courseDescriptionEn').value = course.course_description_en || '';
            document.getElementById('universityId').value = course.university_id || '';
            
            document.getElementById('courseModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('courseModal').classList.add('hidden');
        }

        function deleteCourse(id) {
            if (confirm('هل أنت متأكد من حذف هذه المادة؟')) {
                fetch('admin_courses.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=delete&id=${id}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.message);
                    }
                });
            }
        }

        function toggleCourseStatus(id) {
            fetch('admin_courses.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=toggle_status&id=${id}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('خطأ: ' + data.message);
                }
            });
        }

        // Handle form submission
        document.getElementById('courseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', isEditMode ? 'edit' : 'add');
            
            fetch('admin_courses.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('خطأ: ' + data.message);
                }
            });
        });
    </script>

    <?php if ($userRole === 'publisher'): ?>
            </div> <!-- End publisher main content -->
        </div> <!-- End publisher layout -->
    <?php else: ?>
            </div> <!-- End admin main content -->
        </div> <!-- End admin layout -->
    <?php endif; ?>
</body>
</html>
