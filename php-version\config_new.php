<?php
/**
 * GPA Calculator Configuration
 * Arab Open University System - Enhanced with MySQL
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database configuration
require_once __DIR__ . '/db_config.php';

// Application Settings
define('APP_NAME', 'حاسبة المعدل التراكمي');
define('APP_VERSION', '2.0.0');
define('APP_AUTHOR', 'Arab Open University');
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_GRADING_SYSTEM', 'aou');

// File Paths
define('DATA_DIR', __DIR__ . '/data/');
define('ASSETS_DIR', __DIR__ . '/assets/');

// Create data directory if it doesn't exist
if (!is_dir(DATA_DIR)) {
    mkdir(DATA_DIR, 0755, true);
}

// Load data from database
try {
    // Get grading systems from database
    $all_grading_systems = getGradingSystems();
    
    // Get universities from database
    $universities_db = getUniversities();
    $universities = [];
    
    foreach ($universities_db as $uni) {
        $universities[$uni['id']] = [
            'name' => $uni['name_ar'],
            'name_en' => $uni['name_en'],
            'logo' => $uni['logo'],
            'country' => $uni['country_ar'],
            'country_en' => $uni['country_en'],
            'established' => $uni['established'],
            'students' => $uni['students'],
            'grading_system' => $uni['grading_system'],
            'website' => $uni['website'],
            'description' => $uni['description_ar'],
            'description_en' => $uni['description_en']
        ];
    }
    
} catch (Exception $e) {
    // Fallback to static data if database fails
    error_log("Database error in config: " . $e->getMessage());
    
    // Fallback grading systems
    $all_grading_systems = [
        'aou' => [
            'name' => 'الجامعة العربية المفتوحة',
            'name_en' => 'Arab Open University',
            'grades' => [
                'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'ممتاز'],
                'B+' => ['points' => 3.5, 'range' => '85-89', 'description' => 'جيد جداً مرتفع'],
                'B' => ['points' => 3.0, 'range' => '80-84', 'description' => 'جيد جداً'],
                'C+' => ['points' => 2.5, 'range' => '75-79', 'description' => 'جيد مرتفع'],
                'C' => ['points' => 2.0, 'range' => '70-74', 'description' => 'جيد'],
                'D' => ['points' => 1.5, 'range' => '60-69', 'description' => 'مقبول'],
                'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'راسب']
            ]
        ],
        'standard' => [
            'name' => 'النظام الأمريكي القياسي',
            'name_en' => 'Standard American System',
            'grades' => [
                'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'Excellent'],
                'B' => ['points' => 3.0, 'range' => '80-89', 'description' => 'Good'],
                'C' => ['points' => 2.0, 'range' => '70-79', 'description' => 'Average'],
                'D' => ['points' => 1.0, 'range' => '60-69', 'description' => 'Below Average'],
                'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'Fail']
            ]
        ],
        'simple' => [
            'name' => 'النظام المبسط',
            'name_en' => 'Simple System',
            'grades' => [
                'A' => ['points' => 4.0, 'range' => '90-100', 'description' => 'ممتاز'],
                'B' => ['points' => 3.0, 'range' => '80-89', 'description' => 'جيد'],
                'C' => ['points' => 2.0, 'range' => '70-79', 'description' => 'مقبول'],
                'D' => ['points' => 1.0, 'range' => '60-69', 'description' => 'ضعيف'],
                'F' => ['points' => 0.0, 'range' => '0-59', 'description' => 'راسب']
            ]
        ]
    ];
    
    // Fallback universities
    $universities = [
        'aou' => [
            'name' => 'الجامعة العربية المفتوحة',
            'name_en' => 'Arab Open University',
            'logo' => '🏛️',
            'country' => 'السعودية',
            'country_en' => 'Saudi Arabia',
            'established' => 2002,
            'students' => '40,000+',
            'grading_system' => 'aou',
            'website' => 'https://www.aou.edu.sa',
            'description' => 'مؤسسة تعليمية رائدة تأسست عام 2002 وتقدم برامج أكاديمية متميزة',
            'description_en' => 'Leading educational institution established in 2002 offering distinguished academic programs'
        ]
    ];
}

// Language Settings
$current_language = $_SESSION['language'] ?? DEFAULT_LANGUAGE;

// Set page direction based on language
$page_direction = $current_language === 'ar' ? 'rtl' : 'ltr';

// Language translations
$translations = [
    'ar' => [
        'app_title' => 'حاسبة المعدل التراكمي',
        'university_name' => 'الجامعة العربية المفتوحة',
        'calculate_gpa' => 'احسب المعدل',
        'add_course' => 'إضافة مادة',
        'course_name' => 'اسم المادة',
        'credit_hours' => 'عدد الساعات',
        'grade' => 'التقدير',
        'gpa_result' => 'المعدل التراكمي',
        'total_hours' => 'إجمالي الساعات',
        'classification' => 'التقدير العام',
        'grading_system' => 'نظام التقدير',
        'save_data' => 'حفظ البيانات',
        'load_data' => 'تحميل البيانات'
    ],
    'en' => [
        'app_title' => 'GPA Calculator',
        'university_name' => 'Arab Open University',
        'calculate_gpa' => 'Calculate GPA',
        'add_course' => 'Add Course',
        'course_name' => 'Course Name',
        'credit_hours' => 'Credit Hours',
        'grade' => 'Grade',
        'gpa_result' => 'GPA Result',
        'total_hours' => 'Total Hours',
        'classification' => 'Classification',
        'grading_system' => 'Grading System',
        'save_data' => 'Save Data',
        'load_data' => 'Load Data'
    ]
];

// Translation function
function t($key, $default = '') {
    global $translations, $current_language;
    return $translations[$current_language][$key] ?? $default;
}

// Set default grading system if not set
if (!isset($_SESSION['grading_system'])) {
    $_SESSION['grading_system'] = DEFAULT_GRADING_SYSTEM;
}

// Set default university if not set
if (!isset($_SESSION['selected_university'])) {
    $_SESSION['selected_university'] = 'aou';
}

// Get current grading system
$current_grading_system = $all_grading_systems[$_SESSION['grading_system']] ?? $all_grading_systems['aou'];

// Make grading systems available globally
$grading_systems = $all_grading_systems;

// Get current university
$current_university = $_SESSION['selected_university'] ?? 'aou';
$selected_university = $universities[$current_university] ?? $universities['aou'];
?>
