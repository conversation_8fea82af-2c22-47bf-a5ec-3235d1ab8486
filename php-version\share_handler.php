<?php
/**
 * Share Handler for GPA Calculator
 * Handles sharing and saving student data
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'functions_simple.php';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'save_student_data':
            $name = trim($_POST['name'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $gpa = floatval($_POST['gpa'] ?? 0);
            $courses = json_decode($_POST['courses'] ?? '[]', true);
            $calculation_type = $_POST['calculation_type'] ?? 'semester';
            $previous_gpa = floatval($_POST['previous_gpa'] ?? 0);
            $previous_hours = intval($_POST['previous_hours'] ?? 0);
            
            if (empty($name) || empty($phone)) {
                echo json_encode(['success' => false, 'error' => 'يرجى ملء جميع الحقول المطلوبة']);
                exit;
            }
            
            // Generate unique link ID
            $link_id = uniqid() . '_' . time();
            
            // Prepare student data
            $student_data = [
                'name' => $name,
                'phone' => $phone,
                'gpa' => $gpa,
                'courses' => $courses,
                'calculation_type' => $calculation_type,
                'previous_gpa' => $previous_gpa,
                'previous_hours' => $previous_hours,
                'classification' => getGPAClassification($gpa),
                'university' => $_SESSION['university'] ?? 'aou',
                'grading_system' => $_SESSION['grading_system'] ?? 'aou',
                'created_at' => date('Y-m-d H:i:s'),
                'expires_at' => date('Y-m-d H:i:s', strtotime('+30 days'))
            ];
            
            // Save to file
            $data_dir = __DIR__ . '/data/';
            if (!is_dir($data_dir)) {
                mkdir($data_dir, 0755, true);
            }
            
            $file_path = $data_dir . $link_id . '.json';
            if (file_put_contents($file_path, json_encode($student_data, JSON_PRETTY_PRINT))) {
                echo json_encode([
                    'success' => true,
                    'link_id' => $link_id,
                    'message' => 'تم حفظ البيانات بنجاح'
                ]);
            } else {
                echo json_encode(['success' => false, 'error' => 'فشل في حفظ البيانات']);
            }
            exit;
            
        case 'get_shared_data':
            $link_id = $_POST['link_id'] ?? '';
            
            if (empty($link_id)) {
                echo json_encode(['success' => false, 'error' => 'معرف الرابط مطلوب']);
                exit;
            }
            
            $file_path = __DIR__ . '/data/' . $link_id . '.json';
            
            if (!file_exists($file_path)) {
                echo json_encode(['success' => false, 'error' => 'الرابط غير موجود أو منتهي الصلاحية']);
                exit;
            }
            
            $data = json_decode(file_get_contents($file_path), true);
            
            if (!$data) {
                echo json_encode(['success' => false, 'error' => 'بيانات غير صحيحة']);
                exit;
            }
            
            // Check if expired
            if (isset($data['expires_at']) && strtotime($data['expires_at']) < time()) {
                unlink($file_path); // Delete expired file
                echo json_encode(['success' => false, 'error' => 'الرابط منتهي الصلاحية']);
                exit;
            }
            
            echo json_encode([
                'success' => true,
                'data' => $data
            ]);
            exit;
            
        case 'get_admin_stats':
            $stats = getAdminStatistics();
            echo json_encode($stats);
            exit;
            
        case 'get_students_data':
            $students = getStudentsData();
            echo json_encode($students);
            exit;
            
        case 'delete_student':
            $link_id = $_POST['link_id'] ?? '';
            
            if (empty($link_id)) {
                echo json_encode(['success' => false, 'error' => 'معرف الرابط مطلوب']);
                exit;
            }
            
            $file_path = __DIR__ . '/data/' . $link_id . '.json';
            
            if (file_exists($file_path)) {
                if (unlink($file_path)) {
                    echo json_encode(['success' => true, 'message' => 'تم حذف البيانات بنجاح']);
                } else {
                    echo json_encode(['success' => false, 'error' => 'فشل في حذف البيانات']);
                }
            } else {
                echo json_encode(['success' => false, 'error' => 'البيانات غير موجودة']);
            }
            exit;
            
        case 'export_data':
            $type = $_POST['type'] ?? 'students';
            
            try {
                $data_dir = __DIR__ . '/data/';
                $files = glob($data_dir . '*.json');
                $export_data = [];
                
                foreach ($files as $file) {
                    $content = json_decode(file_get_contents($file), true);
                    if ($content) {
                        $export_data[] = $content;
                    }
                }
                
                if (empty($export_data)) {
                    echo json_encode(['success' => false, 'error' => 'لا توجد بيانات للتصدير']);
                    exit;
                }
                
                // Create CSV content
                $csv_content = '';
                $headers = ['الاسم', 'الهاتف', 'المعدل', 'التقدير', 'الجامعة', 'نوع الحساب', 'تاريخ الإنشاء'];
                $csv_content .= implode(',', $headers) . "\n";
                
                foreach ($export_data as $row) {
                    $csv_row = [
                        '"' . ($row['name'] ?? '') . '"',
                        '"' . ($row['phone'] ?? '') . '"',
                        '"' . ($row['gpa'] ?? 0) . '"',
                        '"' . ($row['classification']['name'] ?? '') . '"',
                        '"' . ($row['university'] ?? '') . '"',
                        '"' . ($row['calculation_type'] ?? '') . '"',
                        '"' . ($row['created_at'] ?? '') . '"'
                    ];
                    $csv_content .= implode(',', $csv_row) . "\n";
                }
                
                // Save to exports directory
                $exports_dir = __DIR__ . '/exports/';
                if (!is_dir($exports_dir)) {
                    mkdir($exports_dir, 0755, true);
                }
                
                $filename = 'students_export_' . date('Y-m-d_H-i-s') . '.csv';
                $filepath = $exports_dir . $filename;
                
                if (file_put_contents($filepath, $csv_content)) {
                    echo json_encode([
                        'success' => true,
                        'filename' => $filename,
                        'download_url' => 'exports/' . $filename,
                        'message' => 'تم تصدير البيانات بنجاح'
                    ]);
                } else {
                    echo json_encode(['success' => false, 'error' => 'فشل في إنشاء ملف التصدير']);
                }
                
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => 'خطأ في تصدير البيانات: ' . $e->getMessage()]);
            }
            exit;
    }
    
    echo json_encode(['error' => 'Invalid action']);
    exit;
}

/**
 * Get admin statistics from files
 */
function getAdminStatistics() {
    $data_dir = __DIR__ . '/data/';
    $stats = [
        'total_users' => 0,
        'total_calculations' => 0,
        'avg_gpa' => 0,
        'today_calculations' => 0,
        'grade_distribution' => [],
        'university_distribution' => [],
        'monthly_stats' => []
    ];
    
    if (!is_dir($data_dir)) {
        return $stats;
    }
    
    $files = glob($data_dir . '*.json');
    $all_gpas = [];
    $today = date('Y-m-d');
    
    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data && isset($data['gpa'])) {
            $stats['total_users']++;
            $all_gpas[] = $data['gpa'];
            
            if (isset($data['created_at']) && strpos($data['created_at'], $today) === 0) {
                $stats['today_calculations']++;
            }
            
            // Grade distribution
            $classification = $data['classification']['name'] ?? 'غير محدد';
            $stats['grade_distribution'][$classification] = ($stats['grade_distribution'][$classification] ?? 0) + 1;
            
            // University distribution
            $university = $data['university'] ?? 'aou';
            $stats['university_distribution'][$university] = ($stats['university_distribution'][$university] ?? 0) + 1;
        }
    }
    
    $stats['total_calculations'] = count($all_gpas);
    $stats['avg_gpa'] = count($all_gpas) > 0 ? array_sum($all_gpas) / count($all_gpas) : 0;
    
    return $stats;
}

/**
 * Get students data from files
 */
function getStudentsData() {
    $data_dir = __DIR__ . '/data/';
    $students = [];
    
    if (!is_dir($data_dir)) {
        return $students;
    }
    
    $files = glob($data_dir . '*.json');
    
    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data) {
            $students[] = [
                'name' => $data['name'] ?? 'غير محدد',
                'phone' => $data['phone'] ?? 'غير محدد',
                'gpa' => $data['gpa'] ?? 0,
                'classification' => $data['classification']['name'] ?? 'غير محدد',
                'university' => $data['university'] ?? 'aou',
                'date' => $data['created_at'] ?? date('Y-m-d H:i:s'),
                'courses_count' => count($data['courses'] ?? []),
                'link_id' => basename($file, '.json')
            ];
        }
    }
    
    // Sort by date (newest first)
    usort($students, function($a, $b) {
        return strtotime($b['date']) - strtotime($a['date']);
    });
    
    return $students;
}
?>
