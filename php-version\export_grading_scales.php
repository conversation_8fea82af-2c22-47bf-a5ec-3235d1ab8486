<?php
/**
 * Export Grading Scales
 * تصدير سلالم الدرجات
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('HTTP/1.1 403 Forbidden');
    exit('غير مصرح لك بالوصول');
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get export format
    $format = $_GET['format'] ?? 'json';
    
    // Get all grading scales with system names
    $gradingScales = $pdo->query("
        SELECT gs.*, gsy.name_ar as system_name, gsy.name_en as system_name_en
        FROM grading_scales gs
        LEFT JOIN grading_systems gsy ON gs.grading_system_id = gsy.id
        ORDER BY gsy.name_ar, gs.points DESC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // Get grading systems
    $gradingSystems = $pdo->query("
        SELECT * FROM grading_systems ORDER BY name_ar
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    $exportData = [
        'export_info' => [
            'exported_at' => date('Y-m-d H:i:s'),
            'exported_by' => $_SESSION['admin_username'] ?? 'admin',
            'total_systems' => count($gradingSystems),
            'total_scales' => count($gradingScales)
        ],
        'grading_systems' => $gradingSystems,
        'grading_scales' => $gradingScales
    ];
    
    $filename = 'grading_scales_export_' . date('Y-m-d_H-i-s');
    
    switch ($format) {
        case 'json':
            header('Content-Type: application/json; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '.json"');
            echo json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            break;
            
        case 'csv':
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
            
            $output = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // CSV Headers
            fputcsv($output, [
                'نظام التقدير',
                'معرف النظام',
                'الدرجة',
                'النقاط',
                'الوصف',
                'أقل نسبة',
                'أعلى نسبة',
                'تاريخ الإنشاء'
            ]);
            
            // CSV Data
            foreach ($gradingScales as $scale) {
                fputcsv($output, [
                    $scale['system_name'] ?? $scale['grading_system_id'],
                    $scale['grading_system_id'],
                    $scale['grade'],
                    $scale['points'],
                    $scale['description_ar'],
                    $scale['min_percentage'],
                    $scale['max_percentage'],
                    $scale['created_at']
                ]);
            }
            
            fclose($output);
            break;
            
        case 'excel':
            // Simple Excel format (HTML table)
            header('Content-Type: application/vnd.ms-excel; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
            
            echo '<html dir="rtl">';
            echo '<head><meta charset="UTF-8"><title>تصدير سلالم الدرجات</title></head>';
            echo '<body>';
            echo '<h1>تصدير سلالم الدرجات</h1>';
            echo '<p>تاريخ التصدير: ' . date('Y-m-d H:i:s') . '</p>';
            echo '<p>إجمالي الأنظمة: ' . count($gradingSystems) . '</p>';
            echo '<p>إجمالي الدرجات: ' . count($gradingScales) . '</p>';
            
            echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
            echo '<tr style="background-color: #f0f0f0;">';
            echo '<th>نظام التقدير</th>';
            echo '<th>معرف النظام</th>';
            echo '<th>الدرجة</th>';
            echo '<th>النقاط</th>';
            echo '<th>الوصف</th>';
            echo '<th>أقل نسبة</th>';
            echo '<th>أعلى نسبة</th>';
            echo '<th>تاريخ الإنشاء</th>';
            echo '</tr>';
            
            foreach ($gradingScales as $scale) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($scale['system_name'] ?? $scale['grading_system_id']) . '</td>';
                echo '<td>' . htmlspecialchars($scale['grading_system_id']) . '</td>';
                echo '<td>' . htmlspecialchars($scale['grade']) . '</td>';
                echo '<td>' . htmlspecialchars($scale['points']) . '</td>';
                echo '<td>' . htmlspecialchars($scale['description_ar']) . '</td>';
                echo '<td>' . htmlspecialchars($scale['min_percentage']) . '</td>';
                echo '<td>' . htmlspecialchars($scale['max_percentage']) . '</td>';
                echo '<td>' . htmlspecialchars($scale['created_at']) . '</td>';
                echo '</tr>';
            }
            
            echo '</table>';
            echo '</body></html>';
            break;
            
        default:
            header('HTTP/1.1 400 Bad Request');
            exit('تنسيق غير مدعوم');
    }
    
} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    exit('خطأ في التصدير: ' . $e->getMessage());
}
?>
