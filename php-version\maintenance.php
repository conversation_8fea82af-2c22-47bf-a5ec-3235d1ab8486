<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وضع الصيانة - حاسبة المعدل التراكمي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .maintenance-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .pulse-slow {
            animation: pulse 3s infinite;
        }
    </style>
</head>
<body class="maintenance-bg min-h-screen flex items-center justify-center">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto text-center">
            <!-- Maintenance Icon -->
            <div class="floating mb-8">
                <div class="w-32 h-32 mx-auto bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <i class="fas fa-tools text-6xl text-white"></i>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="bg-white bg-opacity-10 backdrop-blur-md rounded-2xl p-8 shadow-2xl">
                <h1 class="text-4xl font-bold text-white mb-4">
                    🔧 وضع الصيانة
                </h1>
                
                <p class="text-xl text-white text-opacity-90 mb-6">
                    نعتذر، الموقع تحت الصيانة حالياً
                </p>
                
                <div class="bg-white bg-opacity-20 rounded-lg p-6 mb-6">
                    <p class="text-white text-lg leading-relaxed">
                        نحن نعمل على تحسين الموقع لتقديم تجربة أفضل لك.<br>
                        سيكون الموقع متاحاً قريباً، شكراً لصبرك.
                    </p>
                </div>
                
                <!-- Features List -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                    <div class="bg-white bg-opacity-10 rounded-lg p-4">
                        <i class="fas fa-calculator text-2xl text-white mb-2"></i>
                        <h3 class="text-white font-semibold">حساب المعدل</h3>
                        <p class="text-white text-opacity-80 text-sm">حساب دقيق للمعدل التراكمي</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 rounded-lg p-4">
                        <i class="fas fa-university text-2xl text-white mb-2"></i>
                        <h3 class="text-white font-semibold">عدة جامعات</h3>
                        <p class="text-white text-opacity-80 text-sm">دعم أنظمة تقدير متعددة</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 rounded-lg p-4">
                        <i class="fas fa-share-alt text-2xl text-white mb-2"></i>
                        <h3 class="text-white font-semibold">مشاركة النتائج</h3>
                        <p class="text-white text-opacity-80 text-sm">روابط مشاركة آمنة</p>
                    </div>
                    
                    <div class="bg-white bg-opacity-10 rounded-lg p-4">
                        <i class="fas fa-mobile-alt text-2xl text-white mb-2"></i>
                        <h3 class="text-white font-semibold">متوافق مع الجوال</h3>
                        <p class="text-white text-opacity-80 text-sm">يعمل على جميع الأجهزة</p>
                    </div>
                </div>
                
                <!-- Contact Info -->
                <div class="bg-white bg-opacity-20 rounded-lg p-4 mb-6">
                    <h3 class="text-white font-semibold mb-2">
                        <i class="fas fa-envelope ml-2"></i>
                        للاستفسارات
                    </h3>
                    <p class="text-white text-opacity-90">
                        يمكنك التواصل معنا عبر البريد الإلكتروني
                    </p>
                </div>
                
                <!-- Admin Login -->
                <div class="mt-8">
                    <a href="simple_admin_login.php" class="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-lg transition-all duration-300 backdrop-blur-sm">
                        <i class="fas fa-user-shield ml-2"></i>
                        دخول الإدارة
                    </a>
                </div>
                
                <!-- Loading Animation -->
                <div class="mt-8">
                    <div class="flex justify-center items-center space-x-2 space-x-reverse">
                        <div class="w-3 h-3 bg-white rounded-full pulse-slow"></div>
                        <div class="w-3 h-3 bg-white rounded-full pulse-slow" style="animation-delay: 0.5s;"></div>
                        <div class="w-3 h-3 bg-white rounded-full pulse-slow" style="animation-delay: 1s;"></div>
                    </div>
                    <p class="text-white text-opacity-70 mt-2 text-sm">جاري العمل على التحديثات...</p>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="mt-8 text-center">
                <p class="text-white text-opacity-60 text-sm">
                    © <?php echo date('Y'); ?> حاسبة المعدل التراكمي - جميع الحقوق محفوظة
                </p>
            </div>
        </div>
    </div>

    <script>
        // Auto refresh every 5 minutes to check if maintenance is over
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 minutes
        
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to feature cards
            const cards = document.querySelectorAll('.grid > div');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
