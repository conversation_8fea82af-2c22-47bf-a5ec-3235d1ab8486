<?php
session_start();

// Check if user is logged in and is a publisher
if (!isset($_SESSION['admin_id']) || $_SESSION['admin_role'] !== 'publisher') {
    header('Location: simple_admin_login.php');
    exit;
}

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

$success = '';
$error = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'add':
                $courseCode = trim($_POST['course_code'] ?? '');
                $courseTitleAr = trim($_POST['course_title_ar'] ?? '');
                $courseTitleEn = trim($_POST['course_title_en'] ?? '');
                $creditHours = intval($_POST['credit_hours'] ?? 0);
                $prerequisites = trim($_POST['prerequisites'] ?? '');
                $universityId = trim($_POST['university_id'] ?? '');
                $courseDescriptionAr = trim($_POST['course_description_ar'] ?? '');
                $courseDescriptionEn = trim($_POST['course_description_en'] ?? '');
                
                if (empty($courseCode) || empty($courseTitleAr) || empty($courseTitleEn) || $creditHours <= 0) {
                    $error = 'يرجى ملء جميع الحقول المطلوبة';
                } else {
                    $stmt = $pdo->prepare("
                        INSERT INTO courses (course_code, course_title_ar, course_title_en, credit_hours, prerequisites, university_id, course_description_ar, course_description_en, is_active, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, ?)
                    ");
                    $stmt->execute([$courseCode, $courseTitleAr, $courseTitleEn, $creditHours, $prerequisites, $universityId ?: null, $courseDescriptionAr, $courseDescriptionEn, $_SESSION['admin_id']]);
                    $success = 'تم إضافة المادة بنجاح';
                }
                break;
                
            case 'edit':
                $id = intval($_POST['id'] ?? 0);
                $courseCode = trim($_POST['course_code'] ?? '');
                $courseTitleAr = trim($_POST['course_title_ar'] ?? '');
                $courseTitleEn = trim($_POST['course_title_en'] ?? '');
                $creditHours = intval($_POST['credit_hours'] ?? 0);
                $prerequisites = trim($_POST['prerequisites'] ?? '');
                $universityId = trim($_POST['university_id'] ?? '');
                $courseDescriptionAr = trim($_POST['course_description_ar'] ?? '');
                $courseDescriptionEn = trim($_POST['course_description_en'] ?? '');
                
                if ($id <= 0 || empty($courseCode) || empty($courseTitleAr) || empty($courseTitleEn) || $creditHours <= 0) {
                    $error = 'يرجى ملء جميع الحقول المطلوبة';
                } else {
                    $stmt = $pdo->prepare("
                        UPDATE courses 
                        SET course_code = ?, course_title_ar = ?, course_title_en = ?, credit_hours = ?, prerequisites = ?, university_id = ?, course_description_ar = ?, course_description_en = ?
                        WHERE id = ? AND created_by = ?
                    ");
                    $stmt->execute([$courseCode, $courseTitleAr, $courseTitleEn, $creditHours, $prerequisites, $universityId ?: null, $courseDescriptionAr, $courseDescriptionEn, $id, $_SESSION['admin_id']]);
                    $success = 'تم تحديث المادة بنجاح';
                }
                break;
                
            case 'delete':
                $id = intval($_POST['id'] ?? 0);
                if ($id > 0) {
                    $stmt = $pdo->prepare("DELETE FROM courses WHERE id = ? AND created_by = ?");
                    $stmt->execute([$id, $_SESSION['admin_id']]);
                    $success = 'تم حذف المادة بنجاح';
                }
                break;
                
            case 'toggle_status':
                $id = intval($_POST['id'] ?? 0);
                if ($id > 0) {
                    $stmt = $pdo->prepare("UPDATE courses SET is_active = !is_active WHERE id = ? AND created_by = ?");
                    $stmt->execute([$id, $_SESSION['admin_id']]);
                    $success = 'تم تغيير حالة المادة بنجاح';
                }
                break;
        }
        
        // Return JSON response for AJAX requests
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode(['success' => !empty($success), 'message' => $success ?: $error]);
            exit;
        }
    }
    
    // Get universities for dropdown
    $universities = $pdo->query("SELECT id, name_ar FROM universities WHERE id != 'disabled' ORDER BY name_ar")->fetchAll();
    
    // Get courses created by this publisher only
    $stmt = $pdo->prepare("
        SELECT c.*, u.name_ar as university_name 
        FROM courses c 
        LEFT JOIN universities u ON c.university_id = u.id 
        WHERE c.created_by = ?
        ORDER BY c.created_at DESC
    ");
    $stmt->execute([$_SESSION['admin_id']]);
    $courses = $stmt->fetchAll();
    
    // Get statistics
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM courses WHERE created_by = ?");
    $stmt->execute([$_SESSION['admin_id']]);
    $totalCourses = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM courses WHERE created_by = ? AND is_active = 1");
    $stmt->execute([$_SESSION['admin_id']]);
    $activeCourses = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
    $courses = [];
    $universities = [];
    $totalCourses = 0;
    $activeCourses = 0;

    // Log the full error for debugging
    error_log("Publisher Dashboard Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة ناشر المواد - حاسبة المعدل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Simple Header -->
    <header class="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg">
        <div class="max-w-6xl mx-auto px-4 py-6">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center ml-4">
                        <i class="fas fa-book text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold">نشر المواد الدراسية</h1>
                        <p class="text-blue-100">مرحباً <?php echo htmlspecialchars($_SESSION['admin_name'] ?? $_SESSION['admin_username'] ?? 'ناشر المواد'); ?></p>
                    </div>
                </div>

                <div class="flex items-center space-x-3 space-x-reverse">
                    <a href="publisher_logout.php" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt ml-2"></i>
                        تسجيل خروج
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto px-4 py-8">
        <!-- Alerts -->
        <?php if ($success): ?>
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <span><?php echo htmlspecialchars($success); ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    <span><?php echo htmlspecialchars($error); ?></span>
                </div>
            </div>
        <?php endif; ?>

        <!-- Welcome Section -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <div class="text-center">
                <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-book text-white text-2xl"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">نشر المواد الدراسية</h2>
                <p class="text-gray-600 mb-6">أضف وأدر المواد الدراسية بسهولة</p>

                <!-- Quick Stats -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="text-2xl font-bold text-blue-600"><?php echo $totalCourses; ?></div>
                        <div class="text-sm text-gray-600">إجمالي المواد</div>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4">
                        <div class="text-2xl font-bold text-green-600"><?php echo $activeCourses; ?></div>
                        <div class="text-sm text-gray-600">المواد النشطة</div>
                    </div>
                </div>

                <!-- Add Course Button -->
                <button onclick="openAddModal()" class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة مادة جديدة
                </button>
            </div>
        </div>

        <!-- Courses Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">المواد المنشورة</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">كود المادة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم المادة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الساعات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الجامعة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (count($courses) > 0): ?>
                            <?php foreach ($courses as $course): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo htmlspecialchars($course['course_code']); ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        <div class="font-medium"><?php echo htmlspecialchars($course['course_title_ar']); ?></div>
                                        <div class="text-gray-500 text-xs"><?php echo htmlspecialchars($course['course_title_en']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $course['credit_hours']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($course['university_name'] ?? 'عام'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $course['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                            <?php echo $course['is_active'] ? 'نشط' : 'معطل'; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <button onclick="editCourse(<?php echo htmlspecialchars(json_encode($course)); ?>)" 
                                                    class="text-blue-600 hover:text-blue-900 p-2 rounded-lg hover:bg-blue-50" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button onclick="toggleCourseStatus(<?php echo $course['id']; ?>)" 
                                                    class="text-yellow-600 hover:text-yellow-900 p-2 rounded-lg hover:bg-yellow-50" title="تغيير الحالة">
                                                <i class="fas fa-toggle-<?php echo $course['is_active'] ? 'on' : 'off'; ?>"></i>
                                            </button>
                                            <button onclick="deleteCourse(<?php echo $course['id']; ?>)" 
                                                    class="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                    <div class="flex flex-col items-center">
                                        <i class="fas fa-book text-4xl text-gray-300 mb-4"></i>
                                        <p class="text-lg font-medium mb-2">لا توجد مواد منشورة</p>
                                        <p class="text-sm">ابدأ بإضافة أول مادة دراسية</p>
                                        <button onclick="openAddModal()" class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                                            إضافة مادة جديدة
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Add/Edit Course Modal -->
    <div id="courseModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 id="modalTitle" class="text-xl font-bold text-gray-900">إضافة مادة جديدة</h3>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 p-2">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <form id="courseForm" class="space-y-6">
                        <input type="hidden" id="courseId" name="id">

                        <!-- Basic Information -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">المعلومات الأساسية</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">كود المادة *</label>
                                    <input type="text" id="courseCode" name="course_code" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="مثل: CS101">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">الساعات المعتمدة *</label>
                                    <input type="number" id="creditHours" name="credit_hours" min="1" max="12" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>

                        <!-- Course Names -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">أسماء المادة</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم المادة (عربي) *</label>
                                    <input type="text" id="courseTitleAr" name="course_title_ar" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="مثل: مقدمة في علوم الحاسوب">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم المادة (إنجليزي) *</label>
                                    <input type="text" id="courseTitleEn" name="course_title_en" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="e.g: Introduction to Computer Science">
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">معلومات إضافية</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">المتطلبات السابقة</label>
                                    <input type="text" id="prerequisites" name="prerequisites"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="مثل: MATH101, CS100">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">الجامعة</label>
                                    <select id="universityId" name="university_id"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">عام (جميع الجامعات)</option>
                                        <?php foreach ($universities as $university): ?>
                                            <option value="<?php echo $university['id']; ?>"><?php echo htmlspecialchars($university['name_ar']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Course Descriptions -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">وصف المادة</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف المادة (عربي)</label>
                                    <textarea id="courseDescriptionAr" name="course_description_ar" rows="4"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                              placeholder="وصف تفصيلي للمادة باللغة العربية"></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف المادة (إنجليزي)</label>
                                    <textarea id="courseDescriptionEn" name="course_description_en" rows="4"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                              placeholder="Detailed course description in English"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex justify-end space-x-3 space-x-reverse pt-6 border-t">
                            <button type="button" onclick="closeModal()"
                                    class="px-6 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                إلغاء
                            </button>
                            <button type="submit"
                                    class="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-save ml-2"></i>
                                حفظ المادة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isEditMode = false;

        function openAddModal() {
            isEditMode = false;
            document.getElementById('modalTitle').textContent = 'إضافة مادة جديدة';
            document.getElementById('courseForm').reset();
            document.getElementById('courseId').value = '';
            document.getElementById('courseModal').classList.remove('hidden');
        }

        function editCourse(course) {
            isEditMode = true;
            document.getElementById('modalTitle').textContent = 'تعديل المادة';

            // Fill form with course data
            document.getElementById('courseId').value = course.id;
            document.getElementById('courseCode').value = course.course_code;
            document.getElementById('courseTitleAr').value = course.course_title_ar;
            document.getElementById('courseTitleEn').value = course.course_title_en;
            document.getElementById('prerequisites').value = course.prerequisites || '';
            document.getElementById('creditHours').value = course.credit_hours;
            document.getElementById('courseDescriptionAr').value = course.course_description_ar || '';
            document.getElementById('courseDescriptionEn').value = course.course_description_en || '';
            document.getElementById('universityId').value = course.university_id || '';

            document.getElementById('courseModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('courseModal').classList.add('hidden');
        }

        function deleteCourse(id) {
            if (confirm('هل أنت متأكد من حذف هذه المادة؟\n\nسيتم حذف المادة نهائياً ولا يمكن التراجع عن هذا الإجراء.')) {
                fetch('publisher_dashboard.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `action=delete&id=${id}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ أثناء حذف المادة');
                });
            }
        }

        function toggleCourseStatus(id) {
            fetch('publisher_dashboard.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `action=toggle_status&id=${id}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ أثناء تغيير حالة المادة');
            });
        }

        // Handle form submission
        document.getElementById('courseForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', isEditMode ? 'edit' : 'add');

            fetch('publisher_dashboard.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ أثناء حفظ المادة');
            });
        });

        // Close modal when clicking outside
        document.getElementById('courseModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
