<?php
/**
 * Test Link Expiry Feature
 * اختبار ميزة انتهاء صلاحية الروابط
 */

session_start();
require_once 'functions_simple.php';

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$results = [];
$errors = [];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $results[] = "✅ اتصال قاعدة البيانات ناجح";
    
    // Test link expiry settings
    $expirySettings = getLinkExpirySettings();
    $results[] = "📋 إعدادات انتهاء الروابط - مفعل: " . ($expirySettings['enabled'] ? 'نعم' : 'لا') . " | الأيام: " . $expirySettings['days'];
    
    // Test creating links with different expiry settings
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_create_link'])) {
        $enableExpiry = isset($_POST['enable_expiry']) ? '1' : '0';
        $expiryDays = intval($_POST['expiry_days'] ?? 30);
        
        // Update settings temporarily
        $stmt = $pdo->prepare("
            INSERT INTO settings (setting_key, setting_value, updated_by) 
            VALUES (?, ?, 1) 
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value)
        ");
        
        $stmt->execute(['enable_link_expiry', $enableExpiry]);
        $stmt->execute(['link_expiry_days', $expiryDays]);
        
        $results[] = "⚙️ تم تحديث الإعدادات مؤقتاً - تفعيل: $enableExpiry | أيام: $expiryDays";
        
        // Create test student with link
        $linkId = uniqid() . '_' . time();
        $linkExpiresAt = null;
        
        if ($enableExpiry === '1') {
            $linkExpiresAt = date('Y-m-d H:i:s', strtotime("+{$expiryDays} days"));
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO students (name, phone, university, grading_system, semester_gpa, cumulative_gpa, 
                                total_hours, classification, share_link, link_expires_at, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $testData = [
            'طالب اختبار انتهاء الرابط ' . date('H:i:s'),
            '050' . rand(1000000, 9999999),
            'aou',
            'aou',
            3.75,
            3.75,
            15,
            'ممتاز',
            $linkId,
            $linkExpiresAt
        ];
        
        if ($stmt->execute($testData)) {
            $results[] = "✅ تم إنشاء طالب اختبار بنجاح";
            $results[] = "🔗 رابط الطالب: $linkId";
            if ($linkExpiresAt) {
                $results[] = "⏰ ينتهي في: $linkExpiresAt";
                $results[] = "🔍 حالة الرابط: " . (isLinkExpired($linkExpiresAt) ? 'منتهي الصلاحية' : 'صالح');
            } else {
                $results[] = "♾️ الرابط دائم (لا ينتهي)";
            }
        } else {
            $errors[] = "❌ فشل في إنشاء طالب اختبار";
        }
    }
    
    // Get existing test links
    $stmt = $pdo->query("
        SELECT id, name, share_link, link_expires_at, created_at 
        FROM students 
        WHERE name LIKE '%اختبار انتهاء الرابط%' 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $testLinks = $stmt->fetchAll();
    
    // Test link expiry function
    if (!empty($testLinks)) {
        $results[] = "📊 تم العثور على " . count($testLinks) . " رابط اختبار";
        
        foreach ($testLinks as $link) {
            $isExpired = isLinkExpired($link['link_expires_at']);
            $status = $isExpired ? '❌ منتهي' : '✅ صالح';
            $expiryText = $link['link_expires_at'] ? $link['link_expires_at'] : 'دائم';
            $results[] = "🔗 {$link['share_link']} - $status - ينتهي: $expiryText";
        }
    }
    
} catch (PDOException $e) {
    $errors[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ميزة انتهاء صلاحية الروابط</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">
                <i class="fas fa-clock text-blue-600 ml-2"></i>
                اختبار ميزة انتهاء صلاحية الروابط
            </h1>
            <p class="text-gray-600">اختبار تفعيل وإلغاء تفعيل انتهاء صلاحية روابط المشاركة</p>
        </div>

        <!-- Test Form -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">إنشاء رابط اختبار</h2>
            
            <form method="POST" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="flex items-center mb-3">
                            <input type="checkbox" name="enable_expiry" value="1" checked
                                   onchange="toggleExpiryDays(this)"
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200">
                            <span class="mr-2 text-sm text-gray-700">تفعيل انتهاء صلاحية الرابط</span>
                        </label>
                    </div>
                    
                    <div id="expiry_days_container">
                        <label class="block text-sm font-medium text-gray-700 mb-2">عدد الأيام حتى انتهاء الصلاحية</label>
                        <input type="number" name="expiry_days" value="30" min="1" max="365"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <button type="submit" name="test_create_link" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء رابط اختبار
                </button>
            </form>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Success Results -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-green-700 mb-4">
                    <i class="fas fa-check-circle ml-2"></i>
                    النتائج الناجحة (<?php echo count($results); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($results)): ?>
                        <div class="text-gray-500 text-center py-4">لا توجد نتائج</div>
                    <?php else: ?>
                        <?php foreach ($results as $result): ?>
                            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded-lg text-sm">
                                <?php echo htmlspecialchars($result); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Errors -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-red-700 mb-4">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    الأخطاء (<?php echo count($errors); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($errors)): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                            <i class="fas fa-thumbs-up ml-2"></i>
                            لا توجد أخطاء!
                        </div>
                    <?php else: ?>
                        <?php foreach ($errors as $error): ?>
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded-lg text-sm">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Current Settings -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">الإعدادات الحالية</h2>
            
            <?php
            try {
                $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('enable_link_expiry', 'link_expiry_days')");
                $currentSettings = [];
                while ($row = $stmt->fetch()) {
                    $currentSettings[$row['setting_key']] = $row['setting_value'];
                }
                
                echo '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
                echo '<div class="bg-gray-100 p-4 rounded-lg">';
                echo '<h3 class="font-semibold text-gray-700">تفعيل انتهاء الروابط</h3>';
                echo '<p class="text-lg ' . (($currentSettings['enable_link_expiry'] ?? '1') === '1' ? 'text-green-600' : 'text-red-600') . '">';
                echo (($currentSettings['enable_link_expiry'] ?? '1') === '1') ? '✅ مفعل' : '❌ غير مفعل';
                echo '</p>';
                echo '</div>';
                
                echo '<div class="bg-gray-100 p-4 rounded-lg">';
                echo '<h3 class="font-semibold text-gray-700">مدة انتهاء الروابط</h3>';
                echo '<p class="text-lg text-blue-600">' . ($currentSettings['link_expiry_days'] ?? '30') . ' يوم</p>';
                echo '</div>';
                echo '</div>';
                
            } catch (Exception $e) {
                echo '<p class="text-red-600">خطأ في تحميل الإعدادات: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
            ?>
        </div>

        <!-- Test Links -->
        <?php if (!empty($testLinks)): ?>
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">روابط الاختبار</h2>
            
            <div class="overflow-x-auto">
                <table class="w-full border border-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الاسم</th>
                            <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">رابط المشاركة</th>
                            <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">تاريخ الانتهاء</th>
                            <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الحالة</th>
                            <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">تاريخ الإنشاء</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($testLinks as $link): ?>
                            <?php 
                            $isExpired = isLinkExpired($link['link_expires_at']);
                            $statusClass = $isExpired ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800';
                            $statusText = $isExpired ? 'منتهي الصلاحية' : 'صالح';
                            ?>
                            <tr class="border-t">
                                <td class="px-4 py-2 text-sm"><?php echo htmlspecialchars($link['name']); ?></td>
                                <td class="px-4 py-2 text-sm font-mono"><?php echo htmlspecialchars($link['share_link']); ?></td>
                                <td class="px-4 py-2 text-sm">
                                    <?php echo $link['link_expires_at'] ? date('Y-m-d H:i', strtotime($link['link_expires_at'])) : 'دائم'; ?>
                                </td>
                                <td class="px-4 py-2 text-sm">
                                    <span class="px-2 py-1 text-xs rounded-full <?php echo $statusClass; ?>">
                                        <?php echo $statusText; ?>
                                    </span>
                                </td>
                                <td class="px-4 py-2 text-sm text-gray-600">
                                    <?php echo date('Y-m-d H:i', strtotime($link['created_at'])); ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>

        <!-- Navigation -->
        <div class="mt-6 flex gap-4 justify-center">
            <a href="admin_settings.php" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-cog ml-2"></i>
                صفحة الإعدادات
            </a>
            
            <a href="index.php" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-home ml-2"></i>
                الصفحة الرئيسية
            </a>
            
            <button onclick="location.reload()" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-redo ml-2"></i>
                إعادة الاختبار
            </button>
        </div>
    </div>

    <script>
        function toggleExpiryDays(checkbox) {
            const container = document.getElementById('expiry_days_container');
            if (checkbox.checked) {
                container.classList.remove('opacity-50', 'pointer-events-none');
            } else {
                container.classList.add('opacity-50', 'pointer-events-none');
            }
        }
    </script>
</body>
</html>
