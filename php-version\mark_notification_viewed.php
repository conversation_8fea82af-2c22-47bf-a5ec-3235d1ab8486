<?php
session_start();

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['notification_id'])) {
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $notificationId = $_POST['notification_id'];
        $userSession = session_id();
        
        // Insert or update view record
        $stmt = $pdo->prepare("
            INSERT INTO notification_views (notification_id, user_session) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE viewed_at = CURRENT_TIMESTAMP
        ");
        $stmt->execute([$notificationId, $userSession]);
        
        echo json_encode(['success' => true]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'error' => 'Invalid request']);
}
?>
