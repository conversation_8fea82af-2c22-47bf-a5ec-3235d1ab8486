<?php
// Test if course catalog table exists and has data

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Testing Course Catalog Table</h2>";
    
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'course_catalog'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color: green;'>✓ Table 'course_catalog' exists</p>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE course_catalog");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check data count
        $stmt = $pdo->query("SELECT COUNT(*) FROM course_catalog");
        $count = $stmt->fetchColumn();
        
        echo "<p>Number of courses in table: <strong>$count</strong></p>";
        
        if ($count > 0) {
            // Show sample data
            $stmt = $pdo->query("SELECT * FROM course_catalog LIMIT 5");
            $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Sample Data:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Code</th><th>Title (AR)</th><th>Title (EN)</th><th>Credit Hours</th><th>University</th></tr>";
            foreach ($courses as $course) {
                echo "<tr>";
                echo "<td>{$course['id']}</td>";
                echo "<td>{$course['course_code']}</td>";
                echo "<td>{$course['course_title_ar']}</td>";
                echo "<td>{$course['course_title_en']}</td>";
                echo "<td>{$course['credit_hours']}</td>";
                echo "<td>{$course['university_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠ Table is empty. Running insert script...</p>";
            
            // Insert sample data
            $courses = [
                [
                    'course_code' => 'B207-A',
                    'course_title_ar' => 'تشكيل الفرص التجارية',
                    'course_title_en' => 'Shaping Business Opportunities',
                    'prerequisites' => 'BUS110',
                    'credit_hours' => 8,
                    'course_description_ar' => 'B207A هي مادة 8 ساعات معتمدة (30 نقطة)، المستوى 5 UK-OU مقدمة من خلال برنامج إدارة الأعمال في الجامعة العربية المفتوحة.',
                    'course_description_en' => 'B207A is an 8-credit (30 points), Level 5 UK-OU based course offered through the Business Program at the Arab Open University.',
                    'course_objectives_ar' => 'تم تصميم هذه الوحدة لتوفير التعلم المفاهيمي والعملي المتوسط للطلاب في إدارة العمليات والتسويق وإدارة الموارد البشرية.',
                    'course_objectives_en' => 'This module is designed to provide intermediate conceptual and practical learning to students in operations management, marketing and human resource management.',
                    'course_outcomes_ar' => 'تطوير تقدير نقدي للتفاعلات بين وظائف الأعمال المختلفة والتعقيد التكاملي الذي يشكل الابتكار التجاري.',
                    'course_outcomes_en' => 'Develop a critical appreciation of the interactions between various business functions and the integrative complexity that shapes business innovation.',
                    'university_id' => 'aou'
                ],
                [
                    'course_code' => 'BUS110',
                    'course_title_ar' => 'مقدمة في إدارة الأعمال',
                    'course_title_en' => 'Introduction to Business',
                    'prerequisites' => null,
                    'credit_hours' => 6,
                    'course_description_ar' => 'مقدمة شاملة في مبادئ إدارة الأعمال والمفاهيم الأساسية',
                    'course_description_en' => 'Comprehensive introduction to business management principles and fundamental concepts',
                    'course_objectives_ar' => 'تعريف الطلاب بأساسيات إدارة الأعمال',
                    'course_objectives_en' => 'Introduce students to business management fundamentals',
                    'course_outcomes_ar' => 'فهم المبادئ الأساسية لإدارة الأعمال',
                    'course_outcomes_en' => 'Understanding fundamental principles of business management',
                    'university_id' => 'aou'
                ],
                [
                    'course_code' => 'M150',
                    'course_title_ar' => 'الرياضيات التطبيقية',
                    'course_title_en' => 'Applied Mathematics',
                    'prerequisites' => null,
                    'credit_hours' => 6,
                    'course_description_ar' => 'مقدمة في الرياضيات التطبيقية للأعمال',
                    'course_description_en' => 'Introduction to applied mathematics for business',
                    'course_objectives_ar' => 'تطوير المهارات الرياضية للطلاب',
                    'course_objectives_en' => 'Develop mathematical skills for students',
                    'course_outcomes_ar' => 'إتقان المفاهيم الرياضية الأساسية',
                    'course_outcomes_en' => 'Master basic mathematical concepts',
                    'university_id' => 'aou'
                ]
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO course_catalog (
                    course_code, course_title_ar, course_title_en, prerequisites, 
                    credit_hours, course_description_ar, course_description_en, 
                    course_objectives_ar, course_objectives_en, course_outcomes_ar, 
                    course_outcomes_en, university_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($courses as $course) {
                $stmt->execute([
                    $course['course_code'],
                    $course['course_title_ar'],
                    $course['course_title_en'],
                    $course['prerequisites'],
                    $course['credit_hours'],
                    $course['course_description_ar'],
                    $course['course_description_en'],
                    $course['course_objectives_ar'],
                    $course['course_objectives_en'],
                    $course['course_outcomes_ar'],
                    $course['course_outcomes_en'],
                    $course['university_id']
                ]);
            }
            
            echo "<p style='color: green;'>✓ Sample data inserted successfully!</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Table 'course_catalog' does not exist</p>";
        echo "<p>Creating table...</p>";
        
        // Create table
        $sql = "CREATE TABLE course_catalog (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_code VARCHAR(20) NOT NULL,
            course_title_ar VARCHAR(255) NOT NULL,
            course_title_en VARCHAR(255) NOT NULL,
            prerequisites TEXT,
            credit_hours INT NOT NULL DEFAULT 3,
            course_description_ar TEXT,
            course_description_en TEXT,
            course_objectives_ar TEXT,
            course_objectives_en TEXT,
            course_outcomes_ar TEXT,
            course_outcomes_en TEXT,
            university_id VARCHAR(50),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_course_code (course_code, university_id),
            INDEX idx_course_code (course_code),
            INDEX idx_university (university_id),
            INDEX idx_active (is_active)
        )";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>✓ Table created successfully!</p>";
        echo "<p><a href='test_courses_table.php'>Refresh to see the table</a></p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>
