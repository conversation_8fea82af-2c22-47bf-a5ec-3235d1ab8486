<?php
/**
 * Admin Settings Page
 * صفحة إعدادات الإدارة
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

// Redirect publishers to their own dashboard
if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'publisher') {
    header('Location: publisher_dashboard.php');
    exit;
}

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

$currentUser = [
    'id' => $_SESSION['admin_id'],
    'username' => $_SESSION['admin_username'] ?? 'admin',
    'role' => $_SESSION['admin_role'] ?? 'admin',
    'full_name' => $_SESSION['admin_name'] ?? 'مدير النظام'
];

$success = '';
$error = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_settings':
                    $settings = $_POST['settings'] ?? [];

                    // Handle checkboxes (they won't be in POST if unchecked)
                    $checkboxSettings = ['enable_registration', 'enable_sharing', 'maintenance_mode', 'enable_link_expiry'];
                    foreach ($checkboxSettings as $checkbox) {
                        if (!isset($settings[$checkbox])) {
                            $settings[$checkbox] = '0';
                        }
                    }

                    $updatedCount = 0;
                    foreach ($settings as $key => $value) {
                        try {
                            $stmt = $pdo->prepare("
                                INSERT INTO settings (setting_key, setting_value, updated_by)
                                VALUES (?, ?, ?)
                                ON DUPLICATE KEY UPDATE
                                setting_value = VALUES(setting_value),
                                updated_by = VALUES(updated_by)
                            ");
                            $stmt->execute([$key, $value, $currentUser['id']]);
                            $updatedCount++;
                        } catch (PDOException $e) {
                            error_log("Error updating setting $key: " . $e->getMessage());
                        }
                    }

                    $success = "تم حفظ $updatedCount إعداد بنجاح";
                    break;

                case 'change_password':
                    $currentPassword = $_POST['current_password'] ?? '';
                    $newPassword = $_POST['new_password'] ?? '';
                    $confirmPassword = $_POST['confirm_password'] ?? '';

                    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                        $error = 'يرجى ملء جميع الحقول';
                    } elseif ($newPassword !== $confirmPassword) {
                        $error = 'كلمة المرور الجديدة غير متطابقة';
                    } elseif (strlen($newPassword) < 8) {
                        $error = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
                    } else {
                        // Get current user password
                        $stmt = $pdo->prepare("SELECT password FROM admins WHERE id = ?");
                        $stmt->execute([$currentUser['id']]);
                        $admin = $stmt->fetch();

                        if ($admin && password_verify($currentPassword, $admin['password'])) {
                            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                            $stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE id = ?");
                            $stmt->execute([$hashedPassword, $currentUser['id']]);

                            $success = 'تم تغيير كلمة المرور بنجاح';
                        } else {
                            $error = 'كلمة المرور الحالية غير صحيحة';
                        }
                    }
                    break;

                case 'add_admin':
                    $username = trim($_POST['username'] ?? '');
                    $email = trim($_POST['email'] ?? '');
                    $fullName = trim($_POST['full_name'] ?? '');
                    $role = $_POST['role'] ?? 'admin';
                    $adminPassword = $_POST['admin_password'] ?? '';

                    if (empty($username) || empty($email) || empty($fullName) || empty($adminPassword)) {
                        $error = 'يرجى ملء جميع الحقول';
                    } else {
                        // Check if username or email exists
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM admins WHERE username = ? OR email = ?");
                        $stmt->execute([$username, $email]);

                        if ($stmt->fetchColumn() > 0) {
                            $error = 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً';
                        } else {
                            $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
                            $stmt = $pdo->prepare("
                                INSERT INTO admins (username, email, password, full_name, role, is_active)
                                VALUES (?, ?, ?, ?, ?, 1)
                            ");
                            $stmt->execute([$username, $email, $hashedPassword, $fullName, $role]);

                            $success = 'تم إضافة المدير الجديد بنجاح';
                        }
                    }
                    break;

                case 'add_grading_system':
                    $systemId = trim($_POST['system_id'] ?? '');
                    $nameAr = trim($_POST['name_ar'] ?? '');
                    $nameEn = trim($_POST['name_en'] ?? '');
                    $descriptionAr = trim($_POST['description_ar'] ?? '');

                    if (empty($systemId) || empty($nameAr)) {
                        $error = 'يرجى ملء الحقول المطلوبة';
                    } else {
                        // Check if system ID exists
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM grading_systems WHERE id = ?");
                        $stmt->execute([$systemId]);

                        if ($stmt->fetchColumn() > 0) {
                            $error = 'معرف النظام موجود مسبقاً';
                        } else {
                            $stmt = $pdo->prepare("
                                INSERT INTO grading_systems (id, name_ar, name_en, description_ar, is_active)
                                VALUES (?, ?, ?, ?, 1)
                            ");
                            $stmt->execute([$systemId, $nameAr, $nameEn, $descriptionAr]);

                            $success = 'تم إضافة نظام التقدير بنجاح';
                        }
                    }
                    break;

                case 'edit_grading_system':
                    $systemId = trim($_POST['system_id'] ?? '');
                    $nameAr = trim($_POST['name_ar'] ?? '');
                    $nameEn = trim($_POST['name_en'] ?? '');
                    $descriptionAr = trim($_POST['description_ar'] ?? '');

                    if (empty($systemId) || empty($nameAr)) {
                        $error = 'يرجى ملء الحقول المطلوبة';
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE grading_systems
                            SET name_ar = ?, name_en = ?, description_ar = ?
                            WHERE id = ?
                        ");
                        $stmt->execute([$nameAr, $nameEn, $descriptionAr, $systemId]);

                        $success = 'تم تحديث نظام التقدير بنجاح';
                    }
                    break;

                case 'delete_grading_system':
                    $systemId = trim($_POST['system_id'] ?? '');

                    if (empty($systemId)) {
                        $error = 'معرف النظام مطلوب';
                    } else {
                        // Check if system is used by universities
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM universities WHERE grading_system = ?");
                        $stmt->execute([$systemId]);

                        if ($stmt->fetchColumn() > 0) {
                            $error = 'لا يمكن حذف النظام لأنه مستخدم من قبل جامعات';
                        } else {
                            // Delete grading scales first
                            $stmt = $pdo->prepare("DELETE FROM grading_scales WHERE grading_system_id = ?");
                            $stmt->execute([$systemId]);

                            // Delete grading system
                            $stmt = $pdo->prepare("DELETE FROM grading_systems WHERE id = ?");
                            $stmt->execute([$systemId]);

                            $success = 'تم حذف نظام التقدير بنجاح';
                        }
                    }
                    break;
            }
        }
    }

    // Get current settings
    $settings = [];
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    // Get all admins
    $admins = $pdo->query("SELECT id, username, email, full_name, role, is_active, last_login, created_at FROM admins ORDER BY created_at DESC")->fetchAll();

    // Get all grading systems
    $gradingSystems = $pdo->query("SELECT * FROM grading_systems ORDER BY name_ar")->fetchAll();

    // Get grading systems with scales count
    $gradingSystemsWithCounts = [];
    foreach ($gradingSystems as $system) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM grading_scales WHERE grading_system_id = ?");
        $stmt->execute([$system['id']]);
        $scalesCount = $stmt->fetchColumn();

        $stmt = $pdo->prepare("SELECT COUNT(*) FROM universities WHERE grading_system = ?");
        $stmt->execute([$system['id']]);
        $universitiesCount = $stmt->fetchColumn();

        $system['scales_count'] = $scalesCount;
        $system['universities_count'] = $universitiesCount;
        $gradingSystemsWithCounts[] = $system;
    }

} catch (PDOException $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

// Default settings if not found
$defaultSettings = [
    'site_name' => 'حاسبة المعدل التراكمي - الجامعة العربية المفتوحة',
    'default_language' => 'ar',
    'default_university' => 'aou',
    'max_courses_per_calculation' => '20',
    'link_expiry_days' => '30',
    'enable_link_expiry' => '1',
    'enable_registration' => '1',
    'enable_sharing' => '1',
    'maintenance_mode' => '0',
    'max_login_attempts' => '5',
    'lockout_duration' => '30',
    'session_timeout' => '120'
];

foreach ($defaultSettings as $key => $value) {
    if (!isset($settings[$key])) {
        $settings[$key] = $value;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .tab-button.active {
            background-color: #3B82F6;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إعدادات النظام</h1>
                        <p class="text-gray-600">إدارة إعدادات النظام والمديرين</p>
                    </div>
                </div>
            </header>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="mx-6 mt-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle ml-2"></i>
                        <span><?php echo htmlspecialchars($success); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mx-6 mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        <span><?php echo htmlspecialchars($error); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Tabs -->
            <div class="p-6">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <!-- Tab Navigation -->
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 space-x-reverse px-6">
                            <button onclick="showTab('general')" class="tab-button active py-4 px-2 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                <i class="fas fa-cog ml-2"></i>
                                الإعدادات العامة
                            </button>
                            <button onclick="showTab('security')" class="tab-button py-4 px-2 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                <i class="fas fa-shield-alt ml-2"></i>
                                الأمان
                            </button>
                            <button onclick="showTab('admins')" class="tab-button py-4 px-2 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                <i class="fas fa-users-cog ml-2"></i>
                                إدارة المديرين
                            </button>
                            <button onclick="showTab('grading')" class="tab-button py-4 px-2 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                <i class="fas fa-graduation-cap ml-2"></i>
                                أنظمة التقدير
                            </button>
                            <button onclick="showTab('password')" class="tab-button py-4 px-2 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                <i class="fas fa-key ml-2"></i>
                                تغيير كلمة المرور
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <!-- General Settings Tab -->
                        <div id="general" class="tab-content active">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_settings">

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم الموقع</label>
                                        <input type="text" name="settings[site_name]" value="<?php echo htmlspecialchars($settings['site_name']); ?>"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">اللغة الافتراضية</label>
                                        <select name="settings[default_language]" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                            <option value="ar" <?php echo $settings['default_language'] === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                            <option value="en" <?php echo $settings['default_language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الجامعة الافتراضية</label>
                                        <select name="settings[default_university]" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                            <option value="aou" <?php echo $settings['default_university'] === 'aou' ? 'selected' : ''; ?>>الجامعة العربية المفتوحة</option>
                                            <option value="ksu" <?php echo $settings['default_university'] === 'ksu' ? 'selected' : ''; ?>>جامعة الملك سعود</option>
                                            <option value="kau" <?php echo $settings['default_university'] === 'kau' ? 'selected' : ''; ?>>جامعة الملك عبدالعزيز</option>
                                            <option value="kfupm" <?php echo $settings['default_university'] === 'kfupm' ? 'selected' : ''; ?>>جامعة الملك فهد للبترول والمعادن</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الحد الأقصى للمواد</label>
                                        <input type="number" name="settings[max_courses_per_calculation]" value="<?php echo htmlspecialchars($settings['max_courses_per_calculation']); ?>"
                                               min="1" max="50" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div>
                                        <label class="flex items-center mb-3">
                                            <input type="checkbox" name="settings[enable_link_expiry]" value="1"
                                                   <?php echo $settings['enable_link_expiry'] ? 'checked' : ''; ?>
                                                   onchange="toggleLinkExpiry(this)"
                                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200">
                                            <span class="mr-2 text-sm text-gray-700">تفعيل انتهاء صلاحية الروابط</span>
                                        </label>

                                        <div id="link_expiry_settings" class="<?php echo $settings['enable_link_expiry'] ? '' : 'opacity-50 pointer-events-none'; ?>">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">مدة انتهاء الروابط (أيام)</label>
                                            <input type="number" name="settings[link_expiry_days]" value="<?php echo htmlspecialchars($settings['link_expiry_days']); ?>"
                                                   min="1" max="365" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                            <p class="text-xs text-gray-500 mt-1">
                                                <?php if ($settings['enable_link_expiry']): ?>
                                                    الروابط ستنتهي صلاحيتها بعد <?php echo $settings['link_expiry_days']; ?> يوم
                                                <?php else: ?>
                                                    الروابط لن تنتهي صلاحيتها (دائمة)
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>

                                    <div>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="settings[enable_registration]" value="1"
                                                   <?php echo $settings['enable_registration'] ? 'checked' : ''; ?>
                                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200">
                                            <span class="mr-2 text-sm text-gray-700">تفعيل تسجيل الطلاب</span>
                                        </label>
                                    </div>

                                    <div>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="settings[enable_sharing]" value="1"
                                                   <?php echo $settings['enable_sharing'] ? 'checked' : ''; ?>
                                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200">
                                            <span class="mr-2 text-sm text-gray-700">تفعيل مشاركة النتائج</span>
                                        </label>
                                    </div>

                                    <div>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="settings[maintenance_mode]" value="1"
                                                   <?php echo $settings['maintenance_mode'] ? 'checked' : ''; ?>
                                                   class="rounded border-gray-300 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200">
                                            <span class="mr-2 text-sm text-gray-700">وضع الصيانة</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="mt-6">
                                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-save ml-2"></i>
                                        حفظ الإعدادات
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Security Settings Tab -->
                        <div id="security" class="tab-content">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_settings">

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الحد الأقصى لمحاولات تسجيل الدخول</label>
                                        <input type="number" name="settings[max_login_attempts]" value="<?php echo htmlspecialchars($settings['max_login_attempts']); ?>"
                                               min="1" max="20" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">مدة الحظر (دقائق)</label>
                                        <input type="number" name="settings[lockout_duration]" value="<?php echo htmlspecialchars($settings['lockout_duration']); ?>"
                                               min="1" max="1440" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">انتهاء صلاحية الجلسة (دقائق)</label>
                                        <input type="number" name="settings[session_timeout]" value="<?php echo htmlspecialchars($settings['session_timeout']); ?>"
                                               min="5" max="1440" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>

                                <div class="mt-6">
                                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-save ml-2"></i>
                                        حفظ إعدادات الأمان
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Grading Systems Tab -->
                        <div id="grading" class="tab-content">
                            <!-- Add New Grading System -->
                            <div class="mb-8 p-6 bg-gray-50 rounded-lg">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">إضافة نظام تقدير جديد</h3>

                                <form method="POST" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <input type="hidden" name="action" value="add_grading_system">

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">معرف النظام *</label>
                                        <input type="text" name="system_id" required placeholder="مثل: kfupm"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                        <p class="text-xs text-gray-500 mt-1">معرف فريد باللغة الإنجليزية</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم العربي *</label>
                                        <input type="text" name="name_ar" required placeholder="مثل: جامعة الملك فهد للبترول والمعادن"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الإنجليزي</label>
                                        <input type="text" name="name_en" placeholder="King Fahd University"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                        <input type="text" name="description_ar" placeholder="وصف نظام التقدير"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div class="md:col-span-2">
                                        <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                            <i class="fas fa-plus ml-2"></i>
                                            إضافة نظام التقدير
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Existing Grading Systems -->
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">أنظمة التقدير الموجودة</h3>

                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <?php foreach ($gradingSystemsWithCounts as $system): ?>
                                        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                                            <div class="flex justify-between items-start mb-4">
                                                <div>
                                                    <h4 class="font-semibold text-gray-800 text-lg"><?php echo htmlspecialchars($system['name_ar']); ?></h4>
                                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($system['name_en'] ?? ''); ?></p>
                                                    <p class="text-xs text-gray-500 mt-1">معرف: <?php echo htmlspecialchars($system['id']); ?></p>
                                                </div>
                                                <div class="flex space-x-2 space-x-reverse">
                                                    <button onclick="editGradingSystem('<?php echo htmlspecialchars($system['id']); ?>', '<?php echo htmlspecialchars($system['name_ar']); ?>', '<?php echo htmlspecialchars($system['name_en'] ?? ''); ?>', '<?php echo htmlspecialchars($system['description_ar'] ?? ''); ?>')"
                                                            class="text-blue-600 hover:text-blue-800 p-1" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button onclick="deleteGradingSystem('<?php echo htmlspecialchars($system['id']); ?>', '<?php echo htmlspecialchars($system['name_ar']); ?>')"
                                                            class="text-red-600 hover:text-red-800 p-1" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <?php if ($system['description_ar']): ?>
                                                <p class="text-sm text-gray-600 mb-3"><?php echo htmlspecialchars($system['description_ar']); ?></p>
                                            <?php endif; ?>

                                            <div class="grid grid-cols-2 gap-4 text-sm">
                                                <div class="bg-blue-50 p-3 rounded">
                                                    <div class="font-medium text-blue-800">عدد الدرجات</div>
                                                    <div class="text-2xl font-bold text-blue-600"><?php echo $system['scales_count']; ?></div>
                                                </div>
                                                <div class="bg-green-50 p-3 rounded">
                                                    <div class="font-medium text-green-800">الجامعات المستخدمة</div>
                                                    <div class="text-2xl font-bold text-green-600"><?php echo $system['universities_count']; ?></div>
                                                </div>
                                            </div>

                                            <div class="mt-4 flex space-x-2 space-x-reverse">
                                                <a href="admin_grading_scales.php" class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                                                    <i class="fas fa-cog ml-1"></i>
                                                    إدارة الدرجات
                                                </a>
                                                <?php if ($system['scales_count'] == 0): ?>
                                                    <button onclick="addDefaultGrades('<?php echo htmlspecialchars($system['id']); ?>')"
                                                            class="inline-flex items-center px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors">
                                                        <i class="fas fa-plus ml-1"></i>
                                                        إضافة درجات افتراضية
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <?php if (empty($gradingSystemsWithCounts)): ?>
                                    <div class="text-center py-8 text-gray-500">
                                        <i class="fas fa-graduation-cap text-4xl mb-4"></i>
                                        <p>لا توجد أنظمة تقدير حالياً</p>
                                        <p class="text-sm">قم بإضافة نظام تقدير جديد أعلاه</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Quick Links -->
                            <div class="p-4 bg-blue-50 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">روابط سريعة</h4>
                                <div class="flex flex-wrap gap-3">
                                    <a href="admin_grading_scales.php" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-graduation-cap ml-2"></i>
                                        إدارة سلم الدرجات
                                    </a>
                                    <a href="admin_universities.php" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                        <i class="fas fa-university ml-2"></i>
                                        إدارة الجامعات
                                    </a>
                                    <a href="test_sync.php" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                        <i class="fas fa-sync-alt ml-2"></i>
                                        اختبار المزامنة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Admins Management Tab -->
                        <div id="admins" class="tab-content">
                            <!-- Add New Admin Form -->
                            <div class="mb-8 p-6 bg-gray-50 rounded-lg">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">إضافة مدير جديد</h3>

                                <form method="POST">
                                    <input type="hidden" name="action" value="add_admin">

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                                            <input type="text" name="username" required
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                            <input type="email" name="email" required
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                                            <input type="text" name="full_name" required
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">الدور</label>
                                            <select name="role" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                                <option value="admin">مدير</option>
                                                <option value="moderator">مشرف</option>
                                                <?php if ($currentUser['role'] === 'super_admin'): ?>
                                                    <option value="super_admin">مدير رئيسي</option>
                                                <?php endif; ?>
                                            </select>
                                        </div>

                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                                            <input type="password" name="admin_password" required minlength="8"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                            <i class="fas fa-user-plus ml-2"></i>
                                            إضافة مدير
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Admins List -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">قائمة المديرين</h3>

                                <div class="overflow-x-auto">
                                    <table class="w-full border border-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الاسم</th>
                                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">اسم المستخدم</th>
                                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">البريد الإلكتروني</th>
                                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الدور</th>
                                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الحالة</th>
                                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">آخر دخول</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($admins as $admin): ?>
                                                <tr class="border-t">
                                                    <td class="px-4 py-2 text-sm"><?php echo htmlspecialchars($admin['full_name']); ?></td>
                                                    <td class="px-4 py-2 text-sm"><?php echo htmlspecialchars($admin['username']); ?></td>
                                                    <td class="px-4 py-2 text-sm"><?php echo htmlspecialchars($admin['email']); ?></td>
                                                    <td class="px-4 py-2 text-sm">
                                                        <span class="px-2 py-1 text-xs rounded-full
                                                            <?php
                                                            echo $admin['role'] === 'super_admin' ? 'bg-red-100 text-red-800' :
                                                                ($admin['role'] === 'admin' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800');
                                                            ?>">
                                                            <?php
                                                            echo $admin['role'] === 'super_admin' ? 'مدير رئيسي' :
                                                                ($admin['role'] === 'admin' ? 'مدير' : 'مشرف');
                                                            ?>
                                                        </span>
                                                    </td>
                                                    <td class="px-4 py-2 text-sm">
                                                        <span class="px-2 py-1 text-xs rounded-full <?php echo $admin['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                                            <?php echo $admin['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                        </span>
                                                    </td>
                                                    <td class="px-4 py-2 text-sm text-gray-600">
                                                        <?php echo $admin['last_login'] ? date('Y-m-d H:i', strtotime($admin['last_login'])) : 'لم يسجل دخول'; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Change Password Tab -->
                        <div id="password" class="tab-content">
                            <div class="max-w-md">
                                <form method="POST">
                                    <input type="hidden" name="action" value="change_password">

                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الحالية</label>
                                            <input type="password" name="current_password" required
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة</label>
                                            <input type="password" name="new_password" required minlength="8"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور الجديدة</label>
                                            <input type="password" name="confirm_password" required minlength="8"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>

                                    <div class="mt-6">
                                        <button type="submit" class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                            <i class="fas fa-key ml-2"></i>
                                            تغيير كلمة المرور
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        function toggleLinkExpiry(checkbox) {
            const linkExpirySettings = document.getElementById('link_expiry_settings');
            const helpText = linkExpirySettings.querySelector('p');
            const daysInput = linkExpirySettings.querySelector('input[name="settings[link_expiry_days]"]');

            if (checkbox.checked) {
                linkExpirySettings.classList.remove('opacity-50', 'pointer-events-none');
                helpText.innerHTML = `الروابط ستنتهي صلاحيتها بعد ${daysInput.value} يوم`;
            } else {
                linkExpirySettings.classList.add('opacity-50', 'pointer-events-none');
                helpText.innerHTML = 'الروابط لن تنتهي صلاحيتها (دائمة)';
            }
        }

        // Update help text when days input changes
        document.addEventListener('DOMContentLoaded', function() {
            const daysInput = document.querySelector('input[name="settings[link_expiry_days]"]');
            const enableCheckbox = document.querySelector('input[name="settings[enable_link_expiry]"]');

            if (daysInput && enableCheckbox) {
                daysInput.addEventListener('input', function() {
                    if (enableCheckbox.checked) {
                        const helpText = document.querySelector('#link_expiry_settings p');
                        helpText.innerHTML = `الروابط ستنتهي صلاحيتها بعد ${this.value} يوم`;
                    }
                });
            }
        });

        // Form validation for password change
        document.querySelector('form[action*="change_password"]')?.addEventListener('submit', function(e) {
            const newPassword = this.querySelector('input[name="new_password"]').value;
            const confirmPassword = this.querySelector('input[name="confirm_password"]').value;

            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور الجديدة غير متطابقة');
                return false;
            }
        });

        // Grading System Management Functions
        function editGradingSystem(systemId, nameAr, nameEn, descriptionAr) {
            // Create modal if it doesn't exist
            let modal = document.getElementById('editGradingSystemModal');
            if (!modal) {
                modal = document.createElement('div');
                modal.id = 'editGradingSystemModal';
                modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50';
                modal.innerHTML = `
                    <div class="flex items-center justify-center min-h-screen p-4">
                        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                            <div class="p-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-semibold text-gray-800">تعديل نظام التقدير</h3>
                                    <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <form method="POST" id="editGradingSystemForm">
                                    <input type="hidden" name="action" value="edit_grading_system">
                                    <input type="hidden" name="system_id" id="editSystemId">

                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">الاسم العربي *</label>
                                            <input type="text" name="name_ar" id="editNameAr" required
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الإنجليزي</label>
                                            <input type="text" name="name_en" id="editNameEn"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                            <textarea name="description_ar" id="editDescriptionAr" rows="3"
                                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"></textarea>
                                        </div>
                                    </div>

                                    <div class="mt-6 flex justify-end space-x-3 space-x-reverse">
                                        <button type="button" onclick="closeEditModal()"
                                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                                            إلغاء
                                        </button>
                                        <button type="submit"
                                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                            <i class="fas fa-save ml-2"></i>
                                            حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);

                // Add click outside to close
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeEditModal();
                    }
                });
            }

            // Fill form with data
            document.getElementById('editSystemId').value = systemId;
            document.getElementById('editNameAr').value = nameAr;
            document.getElementById('editNameEn').value = nameEn;
            document.getElementById('editDescriptionAr').value = descriptionAr;

            // Show modal
            modal.classList.remove('hidden');
        }

        function closeEditModal() {
            const modal = document.getElementById('editGradingSystemModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        }

        function deleteGradingSystem(systemId, systemName) {
            if (confirm(`هل أنت متأكد من حذف نظام التقدير "${systemName}"؟\n\nسيتم حذف جميع الدرجات المرتبطة بهذا النظام.\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_grading_system">
                    <input type="hidden" name="system_id" value="${systemId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function addDefaultGrades(systemId) {
            if (confirm(`هل تريد إضافة الدرجات الافتراضية لهذا النظام؟\n\nسيتم توجيهك إلى صفحة إدارة سلم الدرجات.`)) {
                window.location.href = `admin_grading_scales.php?system=${systemId}&action=add_defaults`;
            }
        }
    </script>
</body>
</html>