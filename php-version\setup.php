<?php
/**
 * Database Setup Script
 * GPA Calculator - Enhanced with MySQL
 */

// Check if setup is already done
if (file_exists(__DIR__ . '/.setup_complete')) {
    header('Location: index_new.php');
    exit;
}

$error = '';
$success = '';
$step = $_GET['step'] ?? 1;

if ($_POST) {
    switch ($_POST['action']) {
        case 'test_connection':
            $host = $_POST['db_host'] ?? 'localhost';
            $user = $_POST['db_user'] ?? 'root';
            $pass = $_POST['db_pass'] ?? '';

            try {
                // Test connection with proper error handling
                $dsn = "mysql:host=$host;charset=utf8mb4";
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ];

                $pdo = new PDO($dsn, $user, $pass, $options);
                $success = 'تم الاتصال بقاعدة البيانات بنجاح!';
                $step = 2;
                
                // Save connection details in session
                session_start();
                $_SESSION['db_config'] = [
                    'host' => $host,
                    'user' => $user,
                    'pass' => $pass
                ];
                
            } catch (PDOException $e) {
                $error = 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 'create_database':
            session_start();
            $config = $_SESSION['db_config'];
            $db_name = $_POST['db_name'] ?? 'gpa_calculator';
            
            try {
                $pdo = new PDO("mysql:host={$config['host']}", $config['user'], $config['pass']);
                
                // Create database
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$db_name`");
                
                // Read and execute SQL file
                $sql = file_get_contents(__DIR__ . '/database.sql');
                $sql = str_replace('CREATE DATABASE IF NOT EXISTS gpa_calculator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;', '', $sql);
                $sql = str_replace('USE gpa_calculator;', '', $sql);
                
                // Execute SQL statements
                $statements = explode(';', $sql);
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        $pdo->exec($statement);
                    }
                }
                
                // Update config file
                $configContent = "<?php
define('DB_HOST', '{$config['host']}');
define('DB_NAME', '$db_name');
define('DB_USER', '{$config['user']}');
define('DB_PASS', '{$config['pass']}');
define('DB_CHARSET', 'utf8mb4');
?>";
                
                file_put_contents(__DIR__ . '/db_config_auto.php', $configContent);
                
                // Mark setup as complete
                file_put_contents(__DIR__ . '/.setup_complete', date('Y-m-d H:i:s'));
                
                $success = 'تم إعداد قاعدة البيانات بنجاح!';
                $step = 3;
                
            } catch (PDOException $e) {
                $error = 'فشل في إنشاء قاعدة البيانات: ' . $e->getMessage();
            }
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد حاسبة المعدل التراكمي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">

    <div class="container mx-auto px-4 py-8 max-w-2xl">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="h-20 w-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-3xl mx-auto mb-4">
                <i class="fas fa-cog"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">إعداد حاسبة المعدل التراكمي</h1>
            <p class="text-gray-600">الجامعة العربية المفتوحة - نسخة محسنة مع قاعدة البيانات</p>
        </div>

        <!-- Progress Steps -->
        <div class="flex justify-center mb-8">
            <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 rounded-full <?php echo $step >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'; ?>">
                    1
                </div>
                <div class="w-16 h-1 <?php echo $step >= 2 ? 'bg-blue-600' : 'bg-gray-300'; ?>"></div>
                <div class="flex items-center justify-center w-8 h-8 rounded-full <?php echo $step >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'; ?>">
                    2
                </div>
                <div class="w-16 h-1 <?php echo $step >= 3 ? 'bg-blue-600' : 'bg-gray-300'; ?>"></div>
                <div class="flex items-center justify-center w-8 h-8 rounded-full <?php echo $step >= 3 ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-600'; ?>">
                    3
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if ($error): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    <span><?php echo $error; ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <span><?php echo $success; ?></span>
                </div>
            </div>
        <?php endif; ?>

        <!-- Setup Steps -->
        <div class="bg-white rounded-2xl shadow-xl p-8">
            
            <?php if ($step == 1): ?>
            <!-- Step 1: Database Connection -->
            <div>
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-database text-blue-600 ml-3"></i>
                    الخطوة 1: إعدادات قاعدة البيانات
                </h2>
                
                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="test_connection">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">خادم قاعدة البيانات</label>
                        <input type="text" name="db_host" value="localhost" required
                               class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <p class="text-sm text-gray-500 mt-1">عادة ما يكون localhost</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                        <input type="text" name="db_user" value="root" required
                               class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <p class="text-sm text-gray-500 mt-1">اسم المستخدم لقاعدة البيانات</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                        <input type="password" name="db_pass" 
                               class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <p class="text-sm text-gray-500 mt-1">اتركها فارغة إذا لم تكن هناك كلمة مرور</p>
                    </div>
                    
                    <button type="submit" class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-plug ml-2"></i>
                        اختبار الاتصال
                    </button>
                </form>
            </div>
            
            <?php elseif ($step == 2): ?>
            <!-- Step 2: Create Database -->
            <div>
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-plus-circle text-green-600 ml-3"></i>
                    الخطوة 2: إنشاء قاعدة البيانات
                </h2>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <p class="text-green-800">
                        <i class="fas fa-check-circle ml-2"></i>
                        تم الاتصال بقاعدة البيانات بنجاح! الآن سيتم إنشاء قاعدة البيانات والجداول المطلوبة.
                    </p>
                </div>
                
                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="create_database">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم قاعدة البيانات</label>
                        <input type="text" name="db_name" value="gpa_calculator" required
                               class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <p class="text-sm text-gray-500 mt-1">سيتم إنشاء قاعدة البيانات إذا لم تكن موجودة</p>
                    </div>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="font-semibold text-blue-800 mb-2">ما سيتم إنشاؤه:</h3>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li><i class="fas fa-check ml-2"></i>قاعدة البيانات الرئيسية</li>
                            <li><i class="fas fa-check ml-2"></i>جداول الجامعات وأنظمة التقدير</li>
                            <li><i class="fas fa-check ml-2"></i>جداول الطلاب والحسابات</li>
                            <li><i class="fas fa-check ml-2"></i>جداول المواد والروابط المشاركة</li>
                            <li><i class="fas fa-check ml-2"></i>بيانات افتراضية للجامعات</li>
                            <li><i class="fas fa-check ml-2"></i>حساب إدارة افتراضي</li>
                        </ul>
                    </div>
                    
                    <button type="submit" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-database ml-2"></i>
                        إنشاء قاعدة البيانات
                    </button>
                </form>
            </div>
            
            <?php elseif ($step == 3): ?>
            <!-- Step 3: Setup Complete -->
            <div class="text-center">
                <div class="h-20 w-20 bg-green-600 rounded-full flex items-center justify-center text-white text-3xl mx-auto mb-6">
                    <i class="fas fa-check"></i>
                </div>
                
                <h2 class="text-2xl font-bold text-gray-800 mb-4">تم الإعداد بنجاح!</h2>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                    <p class="text-green-800 mb-4">
                        تم إعداد حاسبة المعدل التراكمي بنجاح مع قاعدة البيانات. يمكنك الآن استخدام التطبيق بجميع مميزاته المتقدمة.
                    </p>
                    
                    <div class="grid md:grid-cols-2 gap-4 text-sm">
                        <div class="bg-white p-3 rounded border">
                            <strong>لوحة الإدارة:</strong><br>
                            اسم المستخدم: admin<br>
                            كلمة المرور: admin123
                        </div>
                        <div class="bg-white p-3 rounded border">
                            <strong>المميزات المتاحة:</strong><br>
                            • حفظ البيانات في قاعدة البيانات<br>
                            • مشاركة النتائج<br>
                            • إحصائيات متقدمة
                        </div>
                    </div>
                </div>
                
                <div class="flex gap-4 justify-center">
                    <a href="index_new.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-calculator ml-2"></i>
                        بدء استخدام الحاسبة
                    </a>
                    
                    <a href="admin.php" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-cog ml-2"></i>
                        لوحة الإدارة
                    </a>
                </div>
            </div>
            <?php endif; ?>
            
        </div>
        
        <!-- Footer -->
        <div class="text-center mt-8 text-gray-600">
            <p>حاسبة المعدل التراكمي - الجامعة العربية المفتوحة</p>
            <p class="text-sm">الإصدار 2.0.0 - محسن مع قاعدة البيانات</p>
        </div>
    </div>

</body>
</html>
