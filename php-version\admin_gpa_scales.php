<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

// Redirect publishers to their own dashboard
if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'publisher') {
    header('Location: publisher_dashboard.php');
    exit;
}

// Database configuration
$host = 'localhost';
$dbname = 'gpa_calculator';
$username = 'root';
$password = '';

$success = '';
$error = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_scale':
                    $universityId = trim($_POST['university_id'] ?? '');
                    $minGpa = floatval($_POST['min_gpa'] ?? 0);
                    $maxGpa = floatval($_POST['max_gpa'] ?? 0);
                    $gradeAr = trim($_POST['grade_ar'] ?? '');
                    $gradeEn = trim($_POST['grade_en'] ?? '');
                    $descriptionAr = trim($_POST['description_ar'] ?? '');
                    $descriptionEn = trim($_POST['description_en'] ?? '');
                    $colorCode = trim($_POST['color_code'] ?? '#3B82F6');

                    if (empty($universityId) || empty($gradeAr) || empty($gradeEn) || $minGpa < 0 || $maxGpa < 0 || $minGpa >= $maxGpa) {
                        $error = 'يرجى ملء جميع الحقول المطلوبة والتأكد من صحة نطاق المعدل';
                    } else {
                        $stmt = $pdo->prepare("
                            INSERT INTO gpa_scales (university_id, min_gpa, max_gpa, grade_ar, grade_en, description_ar, description_en, color_code)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([$universityId, $minGpa, $maxGpa, $gradeAr, $gradeEn, $descriptionAr, $descriptionEn, $colorCode]);

                        $success = 'تم إضافة مقياس المعدل بنجاح';
                    }
                    break;

                case 'edit_scale':
                    $scaleId = intval($_POST['scale_id'] ?? 0);
                    $universityId = trim($_POST['university_id'] ?? '');
                    $minGpa = floatval($_POST['min_gpa'] ?? 0);
                    $maxGpa = floatval($_POST['max_gpa'] ?? 0);
                    $gradeAr = trim($_POST['grade_ar'] ?? '');
                    $gradeEn = trim($_POST['grade_en'] ?? '');
                    $descriptionAr = trim($_POST['description_ar'] ?? '');
                    $descriptionEn = trim($_POST['description_en'] ?? '');
                    $colorCode = trim($_POST['color_code'] ?? '#3B82F6');

                    if ($scaleId <= 0 || empty($universityId) || empty($gradeAr) || empty($gradeEn) || $minGpa < 0 || $maxGpa < 0 || $minGpa >= $maxGpa) {
                        $error = 'يرجى ملء جميع الحقول المطلوبة والتأكد من صحة نطاق المعدل';
                    } else {
                        $stmt = $pdo->prepare("
                            UPDATE gpa_scales
                            SET university_id = ?, min_gpa = ?, max_gpa = ?, grade_ar = ?, grade_en = ?, description_ar = ?, description_en = ?, color_code = ?
                            WHERE id = ?
                        ");
                        $stmt->execute([$universityId, $minGpa, $maxGpa, $gradeAr, $gradeEn, $descriptionAr, $descriptionEn, $colorCode, $scaleId]);

                        $success = 'تم تحديث مقياس المعدل بنجاح';
                    }
                    break;

                case 'delete_scale':
                    $scaleId = intval($_POST['scale_id'] ?? 0);
                    if ($scaleId > 0) {
                        $stmt = $pdo->prepare("DELETE FROM gpa_scales WHERE id = ?");
                        $stmt->execute([$scaleId]);
                        $success = 'تم حذف مقياس المعدل بنجاح';
                    }
                    break;

                case 'toggle_status':
                    $scaleId = intval($_POST['scale_id'] ?? 0);
                    if ($scaleId > 0) {
                        $stmt = $pdo->prepare("UPDATE gpa_scales SET is_active = !is_active WHERE id = ?");
                        $stmt->execute([$scaleId]);
                        $success = 'تم تغيير حالة مقياس المعدل بنجاح';
                    }
                    break;
            }
        }
    }

    // Get universities for dropdown
    $universities = $pdo->query("SELECT id, name_ar FROM universities WHERE id != 'disabled' ORDER BY name_ar")->fetchAll();
    
    // Get GPA scales with university names
    $gpaScales = $pdo->query("
        SELECT gs.*, u.name_ar as university_name
        FROM gpa_scales gs
        LEFT JOIN universities u ON gs.university_id = u.id
        ORDER BY u.name_ar, gs.scale_name
    ")->fetchAll();

    // Process the scales to extract individual grades from JSON
    $processedScales = [];
    foreach ($gpaScales as $scale) {
        $grades = json_decode($scale['grades'], true);
        if ($grades && is_array($grades)) {
            foreach ($grades as $gradeName => $gradeData) {
                $processedScales[] = [
                    'id' => $scale['id'],
                    'university_id' => $scale['university_id'],
                    'university_name' => $scale['university_name'],
                    'scale_name' => $scale['scale_name'],
                    'scale_type' => $scale['scale_type'],
                    'grade_ar' => $gradeName,
                    'grade_en' => $gradeName, // For now, use same name
                    'min_gpa' => $gradeData['min'] ?? 0,
                    'max_gpa' => $gradeData['max'] ?? 0,
                    'points' => $gradeData['points'] ?? 0,
                    'color_code' => '#3B82F6', // Default color
                    'is_active' => $scale['is_active']
                ];
            }
        }
    }

} catch (PDOException $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
    $gpaScales = [];
    $processedScales = [];
    $universities = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة مقاييس المعدل - لوحة الإدارة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">إدارة مقاييس المعدل</h1>
                        <p class="text-gray-600">إدارة وتعديل مقاييس المعدل التراكمي لكل جامعة</p>
                    </div>
                    
                    <button onclick="showAddModal()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة مقياس جديد
                    </button>
                </div>
            </header>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="mx-6 mt-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle ml-2"></i>
                        <span><?php echo htmlspecialchars($success); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mx-6 mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        <span><?php echo htmlspecialchars($error); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <!-- GPA Scales by University -->
            <div class="p-6">
                <?php
                // Group processed scales by university
                $groupedScales = [];
                foreach ($processedScales as $scale) {
                    $universityKey = $scale['university_id'] ?: 'general';
                    $groupedScales[$universityKey][] = $scale;
                }
                
                if (empty($groupedScales)): ?>
                    <div class="bg-white rounded-lg shadow-lg p-8 text-center">
                        <i class="fas fa-chart-line text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد مقاييس معدل</h3>
                        <p class="text-gray-500 mb-4">ابدأ بإضافة مقاييس المعدل للجامعات</p>
                        <button onclick="showAddModal()" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة أول مقياس
                        </button>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($groupedScales as $universityId => $scales): ?>
                            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                                <div class="bg-gradient-to-r from-purple-500 to-blue-600 text-white p-4">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h3 class="text-lg font-bold">
                                                <?php echo htmlspecialchars($scales[0]['university_name'] ?? $universityId); ?>
                                            </h3>
                                            <p class="text-purple-100 text-sm">
                                                مقياس المعدل التراكمي
                                            </p>
                                        </div>
                                        <button onclick="addScaleForUniversity('<?php echo $universityId; ?>')"
                                                class="text-purple-200 hover:text-purple-100 p-1" title="إضافة مقياس">
                                            <i class="fas fa-plus text-sm"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="p-4">
                                    <div class="space-y-2 max-h-64 overflow-y-auto">
                                        <?php foreach ($scales as $scale): ?>
                                            <div class="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50" 
                                                 style="border-left: 4px solid <?php echo htmlspecialchars($scale['color_code']); ?>">
                                                <div class="flex items-center space-x-3 space-x-reverse">
                                                    <div class="w-4 h-4 rounded-full" style="background-color: <?php echo htmlspecialchars($scale['color_code']); ?>"></div>
                                                    <div>
                                                        <div class="font-bold text-gray-800"><?php echo htmlspecialchars($scale['grade_ar']); ?></div>
                                                        <div class="text-xs text-gray-500"><?php echo $scale['min_gpa']; ?> - <?php echo $scale['max_gpa']; ?></div>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-1 space-x-reverse">
                                                    <button onclick="editScale(<?php echo htmlspecialchars(json_encode($scale)); ?>)"
                                                            class="text-blue-600 hover:text-blue-800 p-1" title="تعديل">
                                                        <i class="fas fa-edit text-sm"></i>
                                                    </button>
                                                    <button onclick="toggleScaleStatus(<?php echo $scale['id']; ?>)"
                                                            class="<?php echo $scale['is_active'] ? 'text-green-600 hover:text-green-800' : 'text-gray-400 hover:text-gray-600'; ?> p-1" title="تفعيل/إلغاء">
                                                        <i class="fas fa-toggle-<?php echo $scale['is_active'] ? 'on' : 'off'; ?> text-sm"></i>
                                                    </button>
                                                    <button onclick="deleteScale(<?php echo $scale['id']; ?>, '<?php echo htmlspecialchars($scale['grade_ar']); ?>')"
                                                            class="text-red-600 hover:text-red-800 p-1" title="حذف">
                                                        <i class="fas fa-trash text-sm"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add/Edit Scale Modal -->
    <div id="scaleModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6 border-b">
                    <div class="flex items-center justify-between">
                        <h3 id="modalTitle" class="text-lg font-semibold text-gray-800">إضافة مقياس جديد</h3>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                
                <form id="scaleForm" method="POST" class="p-6">
                    <input type="hidden" name="action" id="formAction" value="add_scale">
                    <input type="hidden" name="scale_id" id="scaleId" value="">
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الجامعة</label>
                            <select name="university_id" id="universityId" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">اختر الجامعة</option>
                                <?php foreach ($universities as $university): ?>
                                    <option value="<?php echo htmlspecialchars($university['id']); ?>">
                                        <?php echo htmlspecialchars($university['name_ar']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">أقل معدل</label>
                                <input type="number" name="min_gpa" id="minGpa" required step="0.01" min="0" max="4"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">أعلى معدل</label>
                                <input type="number" name="max_gpa" id="maxGpa" required step="0.01" min="0" max="4"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">التقدير (عربي)</label>
                                <input type="text" name="grade_ar" id="gradeAr" required maxlength="50"
                                       placeholder="مثل: ممتاز"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">التقدير (إنجليزي)</label>
                                <input type="text" name="grade_en" id="gradeEn" required maxlength="50"
                                       placeholder="e.g: Excellent"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">لون المقياس</label>
                            <input type="color" name="color_code" id="colorCode" value="#3B82F6"
                                   class="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الوصف (عربي)</label>
                                <textarea name="description_ar" id="descriptionAr" rows="2"
                                          placeholder="وصف التقدير باللغة العربية"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الوصف (إنجليزي)</label>
                                <textarea name="description_en" id="descriptionEn" rows="2"
                                          placeholder="Grade description in English"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3 space-x-reverse">
                        <button type="button" onclick="closeModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                            إلغاء
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-save ml-2"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة مقياس جديد';
            document.getElementById('formAction').value = 'add_scale';
            document.getElementById('scaleForm').reset();
            document.getElementById('scaleId').value = '';
            document.getElementById('colorCode').value = '#3B82F6';
            document.getElementById('scaleModal').classList.remove('hidden');
        }

        function addScaleForUniversity(universityId) {
            showAddModal();
            document.getElementById('universityId').value = universityId;
        }

        function editScale(scale) {
            document.getElementById('modalTitle').textContent = 'تعديل المقياس';
            document.getElementById('formAction').value = 'edit_scale';
            document.getElementById('scaleId').value = scale.id;
            document.getElementById('universityId').value = scale.university_id;
            document.getElementById('minGpa').value = scale.min_gpa;
            document.getElementById('maxGpa').value = scale.max_gpa;
            document.getElementById('gradeAr').value = scale.grade_ar;
            document.getElementById('gradeEn').value = scale.grade_en;
            document.getElementById('descriptionAr').value = scale.description_ar || '';
            document.getElementById('descriptionEn').value = scale.description_en || '';
            document.getElementById('colorCode').value = scale.color_code || '#3B82F6';
            document.getElementById('scaleModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('scaleModal').classList.add('hidden');
        }

        function deleteScale(id, grade) {
            if (confirm(`هل أنت متأكد من حذف مقياس "${grade}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_scale">
                    <input type="hidden" name="scale_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function toggleScaleStatus(id) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="toggle_status">
                <input type="hidden" name="scale_id" value="${id}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        // Close modal when clicking outside
        document.getElementById('scaleModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
