<?php
/**
 * <PERSON><PERSON><PERSON> to create university_gpa_classifications table and populate with default data
 */

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to database successfully\n";
} catch (PDOException $e) {
    die("❌ Connection failed: " . $e->getMessage() . "\n");
}

echo "\n🔧 Creating university_gpa_classifications table...\n\n";

try {
    // Create the table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS university_gpa_classifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            university_id VARCHAR(50) NOT NULL,
            min_gpa DECIMAL(3,2) NOT NULL,
            max_gpa DECIMAL(3,2) NOT NULL,
            classification_ar VARCHAR(100) NOT NULL,
            classification_en VARCHAR(100) NOT NULL,
            color_code VARCHAR(7) DEFAULT '#3B82F6',
            display_order INT DEFAULT 0,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_university_id (university_id),
            INDEX idx_gpa_range (min_gpa, max_gpa),
            INDEX idx_display_order (display_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "✅ Table 'university_gpa_classifications' created successfully\n";

    // Get all universities
    $stmt = $pdo->query("SELECT id, name_ar FROM universities WHERE id != 'disabled'");
    $universities = $stmt->fetchAll();
    
    echo "📚 Found " . count($universities) . " universities\n";

    // Default classifications for Saudi universities
    $defaultClassifications = [
        [4.00, 3.67, 'ممتاز', 'Excellent', '#10B981', 1],
        [3.66, 3.00, 'جيد جداً', 'Very Good', '#3B82F6', 2],
        [2.99, 2.33, 'جيد', 'Good', '#F59E0B', 3],
        [2.32, 2.00, 'مقبول', 'Fair', '#F97316', 4],
        [1.99, 0.00, 'راسب', 'Fail', '#EF4444', 5]
    ];

    // Insert default classifications for each university
    $stmt = $pdo->prepare("
        INSERT INTO university_gpa_classifications (university_id, min_gpa, max_gpa, classification_ar, classification_en, color_code, display_order)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
        classification_ar = VALUES(classification_ar),
        classification_en = VALUES(classification_en),
        color_code = VALUES(color_code),
        display_order = VALUES(display_order)
    ");

    foreach ($universities as $university) {
        echo "📝 Adding classifications for: " . $university['name_ar'] . "\n";
        
        foreach ($defaultClassifications as $classification) {
            $stmt->execute([
                $university['id'],
                $classification[1], // min_gpa
                $classification[0], // max_gpa
                $classification[2], // classification_ar
                $classification[3], // classification_en
                $classification[4], // color_code
                $classification[5]  // display_order
            ]);
        }
    }

    echo "\n✅ Default classifications added for all universities\n";

    // Verify the data
    $stmt = $pdo->query("
        SELECT u.name_ar, COUNT(ugc.id) as classifications_count 
        FROM universities u 
        LEFT JOIN university_gpa_classifications ugc ON u.id = ugc.university_id 
        WHERE u.id != 'disabled'
        GROUP BY u.id, u.name_ar
        ORDER BY u.name_ar
    ");
    $results = $stmt->fetchAll();

    echo "\n📊 Verification Results:\n";
    echo "========================\n";
    foreach ($results as $result) {
        echo "🏛️  " . $result['name_ar'] . ": " . $result['classifications_count'] . " classifications\n";
    }

    // Show total count
    $totalCount = $pdo->query("SELECT COUNT(*) FROM university_gpa_classifications")->fetchColumn();
    echo "\n📈 Total classifications in database: " . $totalCount . "\n";

    echo "\n🎉 Setup completed successfully!\n";
    echo "\n📋 Next steps:\n";
    echo "1. Visit the admin panel to manage classifications\n";
    echo "2. Customize classifications for each university as needed\n";
    echo "3. Test the GPA calculator to see the new classifications\n";

} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد جداول تصنيف المعدل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl p-8 max-w-2xl w-full">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-green-600 text-2xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">تم إعداد جداول تصنيف المعدل بنجاح!</h1>
                <p class="text-gray-600">تم إنشاء الجداول وإضافة البيانات الافتراضية لجميع الجامعات</p>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h3 class="font-semibold text-blue-800 mb-2">ما تم إنجازه:</h3>
                <ul class="text-blue-700 space-y-1 text-sm">
                    <li>✅ إنشاء جدول university_gpa_classifications</li>
                    <li>✅ إضافة التصنيفات الافتراضية لجميع الجامعات</li>
                    <li>✅ ربط التصنيفات بكل جامعة بشكل منفصل</li>
                    <li>✅ إعداد الألوان والترتيب للعرض</li>
                </ul>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <h3 class="font-semibold text-yellow-800 mb-2">الخطوات التالية:</h3>
                <ul class="text-yellow-700 space-y-1 text-sm">
                    <li>🔧 يمكنك تخصيص التصنيفات من لوحة الإدارة</li>
                    <li>📊 كل جامعة لها تصنيفاتها المستقلة</li>
                    <li>🎨 يمكن تغيير الألوان والأوصاف</li>
                    <li>📈 التصنيفات ستظهر في الصفحة الرئيسية</li>
                </ul>
            </div>

            <div class="flex gap-4">
                <a href="index.php" class="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors text-center">
                    <i class="fas fa-home ml-2"></i>
                    الصفحة الرئيسية
                </a>
                <a href="admin_gpa_classifications.php" class="flex-1 bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors text-center">
                    <i class="fas fa-cog ml-2"></i>
                    إدارة التصنيفات
                </a>
            </div>

            <div class="mt-6 text-center text-sm text-gray-500">
                <p>يمكنك حذف هذا الملف بعد التأكد من عمل النظام بشكل صحيح</p>
            </div>
        </div>
    </div>
</body>
</html>
