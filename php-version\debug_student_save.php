<?php
/**
 * Debug Student Save Process
 * تتبع مشكلة حفظ بيانات الطلاب
 */

session_start();

// Initialize session variables
if (!isset($_SESSION['language'])) {
    $_SESSION['language'] = 'ar';
}
if (!isset($_SESSION['grading_system'])) {
    $_SESSION['grading_system'] = 'aou';
}
if (!isset($_SESSION['university'])) {
    $_SESSION['university'] = 'aou';
}

require_once 'functions_simple.php';

$debug = [];
$results = [];
$errors = [];

// Test AJAX save simulation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_save'])) {
    $debug[] = "🔍 بدء عملية الحفظ";
    
    // Simulate the exact data that would be sent from the frontend
    $testData = [
        'action' => 'save_student_data',
        'name' => trim($_POST['name'] ?? 'طالب تجريبي'),
        'phone' => trim($_POST['phone'] ?? '0501234567'),
        'gpa' => floatval($_POST['gpa'] ?? 3.75),
        'courses' => json_encode([
            ['name' => 'الرياضيات', 'hours' => 3, 'grade' => 'A', 'points' => 4.0],
            ['name' => 'الفيزياء', 'hours' => 4, 'grade' => 'B+', 'points' => 3.5]
        ]),
        'calculation_type' => 'semester',
        'previous_gpa' => 0,
        'previous_hours' => 0
    ];
    
    $debug[] = "📝 البيانات المرسلة: " . json_encode($testData, JSON_UNESCAPED_UNICODE);
    
    // Test database connection first
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $debug[] = "✅ اتصال قاعدة البيانات ناجح";
        
        // Check tables exist
        $tables = ['students', 'courses'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $debug[] = "✅ جدول $table موجود";
                
                // Check table structure
                $stmt = $pdo->query("DESCRIBE $table");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $debug[] = "📋 أعمدة جدول $table: " . implode(', ', $columns);
            } else {
                $errors[] = "❌ جدول $table غير موجود";
            }
        }
        
    } catch (PDOException $e) {
        $errors[] = "❌ خطأ في اتصال قاعدة البيانات: " . $e->getMessage();
    }
    
    // Now simulate the actual save process
    if (empty($errors)) {
        $debug[] = "🚀 بدء محاكاة عملية الحفظ";
        
        // Create a temporary POST array
        $originalPost = $_POST;
        $_POST = $testData;
        
        // Capture the output from index.php
        ob_start();
        
        try {
            // Include the save logic from index.php
            $name = trim($_POST['name'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $gpa = floatval($_POST['gpa'] ?? 0);
            $courses = json_decode($_POST['courses'] ?? '[]', true);
            $calculation_type = $_POST['calculation_type'] ?? 'semester';
            $previous_gpa = floatval($_POST['previous_gpa'] ?? 0);
            $previous_hours = intval($_POST['previous_hours'] ?? 0);

            $debug[] = "📊 البيانات المستخرجة - الاسم: $name، الهاتف: $phone، المعدل: $gpa";
            $debug[] = "📚 عدد المواد: " . count($courses);

            if (empty($name) || empty($phone)) {
                $errors[] = "❌ يرجى ملء جميع الحقول المطلوبة";
            } else {
                $debug[] = "✅ التحقق من البيانات المطلوبة مكتمل";
                
                // Try database save
                $useDatabase = false;
                $pdo = null;
                
                try {
                    $pdo = new PDO("mysql:host=localhost;dbname=gpa_calculator;charset=utf8mb4", 'root', '');
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    $useDatabase = true;
                    $debug[] = "✅ اتصال قاعدة البيانات للحفظ ناجح";
                } catch (PDOException $e) {
                    $useDatabase = false;
                    $debug[] = "❌ فشل اتصال قاعدة البيانات: " . $e->getMessage();
                }

                if ($useDatabase && $pdo) {
                    $debug[] = "💾 بدء الحفظ في قاعدة البيانات";
                    
                    // Generate unique link ID
                    $link_id = uniqid() . '_' . time();
                    $debug[] = "🔗 معرف الرابط: $link_id";

                    // Calculate total hours
                    $total_hours = array_sum(array_column($courses, 'hours'));
                    $debug[] = "⏰ إجمالي الساعات: $total_hours";

                    // Get classification
                    $classification = getGPAClassification($gpa);
                    $debug[] = "🏆 التصنيف: " . ($classification['name'] ?? 'غير محدد');

                    // Insert student data
                    $stmt = $pdo->prepare("
                        INSERT INTO students (name, phone, university, grading_system, semester_gpa, cumulative_gpa, 
                                            total_hours, previous_gpa, previous_hours, classification, ip_address, 
                                            user_agent, share_link, link_expires_at, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                    ");
                    
                    $studentData = [
                        $name,
                        $phone,
                        $_SESSION['university'] ?? 'aou',
                        $_SESSION['grading_system'] ?? 'aou',
                        $gpa,
                        $gpa,
                        $total_hours,
                        $previous_gpa,
                        $previous_hours,
                        $classification['name'] ?? 'غير محدد',
                        $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                        $_SERVER['HTTP_USER_AGENT'] ?? 'Debug Browser',
                        $link_id,
                        date('Y-m-d H:i:s', strtotime('+30 days'))
                    ];
                    
                    $debug[] = "👤 بيانات الطالب للإدراج: " . json_encode($studentData, JSON_UNESCAPED_UNICODE);
                    
                    if ($stmt->execute($studentData)) {
                        $student_id = $pdo->lastInsertId();
                        $debug[] = "✅ تم حفظ بيانات الطالب بنجاح - ID: $student_id";

                        // Insert courses
                        if (!empty($courses)) {
                            $courseStmt = $pdo->prepare("
                                INSERT INTO courses (student_id, course_name, credit_hours, grade, grade_points, created_at) 
                                VALUES (?, ?, ?, ?, ?, NOW())
                            ");
                            
                            $savedCourses = 0;
                            foreach ($courses as $course) {
                                $courseData = [
                                    $student_id,
                                    $course['name'] ?? 'مادة غير محددة',
                                    $course['hours'] ?? 0,
                                    $course['grade'] ?? 'F',
                                    $course['points'] ?? 0
                                ];
                                
                                if ($courseStmt->execute($courseData)) {
                                    $savedCourses++;
                                    $debug[] = "📚 تم حفظ المادة: " . ($course['name'] ?? 'غير محددة');
                                } else {
                                    $errors[] = "❌ فشل في حفظ المادة: " . ($course['name'] ?? 'غير محددة');
                                }
                            }
                            $debug[] = "✅ تم حفظ $savedCourses مادة من أصل " . count($courses);
                        }

                        $results[] = "🎉 تم حفظ البيانات بنجاح!";
                        $results[] = "🔗 رابط المشاركة: $link_id";
                        $results[] = "👤 معرف الطالب: $student_id";
                        
                    } else {
                        $errors[] = "❌ فشل في حفظ بيانات الطالب";
                        $debug[] = "❌ خطأ في تنفيذ استعلام إدراج الطالب";
                    }
                } else {
                    $debug[] = "📁 استخدام نظام الملفات كبديل";
                    
                    // Fallback to file system
                    $link_id = uniqid() . '_' . time();

                    $student_data = [
                        'name' => $name,
                        'phone' => $phone,
                        'gpa' => $gpa,
                        'courses' => $courses,
                        'calculation_type' => $calculation_type,
                        'previous_gpa' => $previous_gpa,
                        'previous_hours' => $previous_hours,
                        'classification' => getGPAClassification($gpa),
                        'university' => $_SESSION['university'] ?? 'aou',
                        'grading_system' => $_SESSION['grading_system'] ?? 'aou',
                        'created_at' => date('Y-m-d H:i:s'),
                        'expires_at' => date('Y-m-d H:i:s', strtotime('+30 days'))
                    ];

                    $data_dir = __DIR__ . '/data/';
                    if (!is_dir($data_dir)) {
                        mkdir($data_dir, 0755, true);
                        $debug[] = "📁 تم إنشاء مجلد البيانات";
                    }

                    $file_path = $data_dir . $link_id . '.json';
                    if (file_put_contents($file_path, json_encode($student_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
                        $results[] = "✅ تم حفظ البيانات في الملف بنجاح";
                        $results[] = "🔗 رابط المشاركة: $link_id";
                        $debug[] = "📁 مسار الملف: $file_path";
                    } else {
                        $errors[] = "❌ فشل في حفظ البيانات في الملف";
                    }
                }
            }
            
        } catch (PDOException $e) {
            $errors[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
            $debug[] = "💥 تفاصيل خطأ PDO: " . $e->getTraceAsString();
        } catch (Exception $e) {
            $errors[] = "❌ خطأ عام: " . $e->getMessage();
            $debug[] = "💥 تفاصيل الخطأ العام: " . $e->getTraceAsString();
        }
        
        $output = ob_get_clean();
        if (!empty($output)) {
            $debug[] = "📤 مخرجات العملية: " . $output;
        }
        
        // Restore original POST
        $_POST = $originalPost;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تتبع مشكلة حفظ بيانات الطلاب</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">
                <i class="fas fa-bug text-red-600 ml-2"></i>
                تتبع مشكلة حفظ بيانات الطلاب
            </h1>
            <p class="text-gray-600">تشخيص مفصل لعملية حفظ البيانات وإنشاء الروابط</p>
        </div>

        <!-- Test Form -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">اختبار حفظ البيانات</h2>
            
            <form method="POST" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم الطالب</label>
                        <input type="text" name="name" value="طالب تجريبي <?php echo date('H:i:s'); ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                        <input type="text" name="phone" value="050<?php echo rand(1000000, 9999999); ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المعدل التراكمي</label>
                        <input type="number" name="gpa" value="3.75" step="0.01" min="0" max="4" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <button type="submit" name="test_save" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-play ml-2"></i>
                    تشغيل اختبار الحفظ
                </button>
            </form>
        </div>

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Debug Info -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-blue-700 mb-4">
                    <i class="fas fa-info-circle ml-2"></i>
                    معلومات التتبع (<?php echo count($debug); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($debug)): ?>
                        <div class="text-gray-500 text-center py-4">لا توجد معلومات تتبع</div>
                    <?php else: ?>
                        <?php foreach ($debug as $item): ?>
                            <div class="bg-blue-100 border border-blue-400 text-blue-700 px-3 py-2 rounded-lg text-sm">
                                <?php echo htmlspecialchars($item); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Success Results -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-green-700 mb-4">
                    <i class="fas fa-check-circle ml-2"></i>
                    النتائج الناجحة (<?php echo count($results); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($results)): ?>
                        <div class="text-gray-500 text-center py-4">لا توجد نتائج</div>
                    <?php else: ?>
                        <?php foreach ($results as $result): ?>
                            <div class="bg-green-100 border border-green-400 text-green-700 px-3 py-2 rounded-lg text-sm">
                                <?php echo htmlspecialchars($result); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Errors -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-red-700 mb-4">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    الأخطاء (<?php echo count($errors); ?>)
                </h2>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php if (empty($errors)): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-3 py-3 rounded-lg">
                            <i class="fas fa-thumbs-up ml-2"></i>
                            لا توجد أخطاء!
                        </div>
                    <?php else: ?>
                        <?php foreach ($errors as $error): ?>
                            <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded-lg text-sm">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Session Info -->
        <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">معلومات الجلسة</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div class="bg-gray-100 p-3 rounded-lg">
                    <span class="font-medium text-gray-700">اللغة:</span>
                    <span class="text-gray-600"><?php echo $_SESSION['language'] ?? 'غير محدد'; ?></span>
                </div>
                <div class="bg-gray-100 p-3 rounded-lg">
                    <span class="font-medium text-gray-700">نظام التقدير:</span>
                    <span class="text-gray-600"><?php echo $_SESSION['grading_system'] ?? 'غير محدد'; ?></span>
                </div>
                <div class="bg-gray-100 p-3 rounded-lg">
                    <span class="font-medium text-gray-700">الجامعة:</span>
                    <span class="text-gray-600"><?php echo $_SESSION['university'] ?? 'غير محدد'; ?></span>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="mt-6 flex gap-4 justify-center">
            <a href="index.php" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-home ml-2"></i>
                الصفحة الرئيسية
            </a>
            
            <a href="test_student_save.php" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-test-tube ml-2"></i>
                اختبار عادي
            </a>
            
            <button onclick="location.reload()" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-redo ml-2"></i>
                إعادة الاختبار
            </button>
        </div>
    </div>
</body>
</html>
