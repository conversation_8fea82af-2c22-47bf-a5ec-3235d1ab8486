<?php
/**
 * Admin Dashboard
 * لوحة الإدارة الرئيسية
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: simple_admin_login.php');
    exit;
}

// Redirect publishers to their own dashboard
if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'publisher') {
    header('Location: publisher_dashboard.php');
    exit;
}

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gpa_calculator';

// Get dashboard data
try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get basic statistics
    $stats = $pdo->query("
        SELECT
            (SELECT COUNT(*) FROM students) as total_students,
            (SELECT COUNT(*) FROM students WHERE DATE(created_at) = CURDATE()) as today_students,
            (SELECT COUNT(*) FROM students WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) as week_students,
            (SELECT COUNT(*) FROM students WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as month_students,
            (SELECT ROUND(AVG(cumulative_gpa), 2) FROM students WHERE cumulative_gpa IS NOT NULL) as avg_gpa,
            (SELECT COUNT(DISTINCT university) FROM students) as total_universities,
            (SELECT COUNT(*) FROM courses) as total_courses
    ")->fetch();

    // Get university statistics
    $universityStats = $pdo->query("
        SELECT
            university,
            COUNT(*) as student_count,
            ROUND(AVG(cumulative_gpa), 2) as avg_gpa
        FROM students
        WHERE cumulative_gpa IS NOT NULL
        GROUP BY university
        ORDER BY student_count DESC
        LIMIT 5
    ")->fetchAll();

    // Get recent students
    $recentStudents = $pdo->query("
        SELECT
            name,
            university,
            cumulative_gpa,
            created_at
        FROM students
        ORDER BY created_at DESC
        LIMIT 10
    ")->fetchAll();

    // Get grade distribution
    $gradeDistribution = $pdo->query("
        SELECT
            CASE
                WHEN cumulative_gpa >= 3.75 THEN 'ممتاز'
                WHEN cumulative_gpa >= 3.25 THEN 'جيد جداً مرتفع'
                WHEN cumulative_gpa >= 2.75 THEN 'جيد جداً'
                WHEN cumulative_gpa >= 2.25 THEN 'جيد مرتفع'
                WHEN cumulative_gpa >= 2.0 THEN 'جيد'
                ELSE 'أقل من جيد'
            END as grade_category,
            COUNT(*) as count
        FROM students
        WHERE cumulative_gpa IS NOT NULL
        GROUP BY grade_category
        ORDER BY count DESC
    ")->fetchAll();

} catch (Exception $e) {
    error_log("Dashboard data error: " . $e->getMessage());
    $stats = [
        'total_students' => 0,
        'today_students' => 0,
        'week_students' => 0,
        'month_students' => 0,
        'avg_gpa' => 0,
        'total_universities' => 0,
        'total_courses' => 0
    ];
    $universityStats = [];
    $recentStudents = [];
    $gradeDistribution = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة - حاسبة المعدل التراكمي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar {
            transition: transform 0.3s ease;
        }
        
        .sidebar.collapsed {
            transform: translateX(100%);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .activity-item {
            transition: background-color 0.3s ease;
        }
        
        .activity-item:hover {
            background-color: #f8fafc;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                z-index: 50;
                height: 100vh;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <?php include 'admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">مرحباً، <?php echo htmlspecialchars($_SESSION['admin_name'] ?? 'مدير النظام'); ?></h1>
                        <p class="text-gray-600">إليك نظرة عامة على النظام اليوم</p>
                    </div>
                    
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <button class="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg">
                            <i class="fas fa-bell"></i>
                        </button>
                        
                        <a href="index.php" target="_blank" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                            <i class="fas fa-external-link-alt ml-2"></i>
                            عرض الموقع
                        </a>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <main class="p-6">
                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="stat-card text-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100">إجمالي الطلاب</p>
                                <p class="text-3xl font-bold"><?php echo number_format($stats['total_students'] ?? 0); ?></p>
                            </div>
                            <i class="fas fa-users text-4xl text-blue-200"></i>
                        </div>
                        <div class="mt-4 text-sm text-blue-100">
                            <i class="fas fa-arrow-up ml-1"></i>
                            +<?php echo $stats['today_students'] ?? 0; ?> اليوم
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100">متوسط المعدل</p>
                                <p class="text-3xl font-bold"><?php echo $stats['avg_gpa'] ?? '0.00'; ?></p>
                            </div>
                            <i class="fas fa-chart-line text-4xl text-green-200"></i>
                        </div>
                        <div class="mt-4 text-sm text-green-100">
                            <i class="fas fa-info-circle ml-1"></i>
                            من 4.00
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-yellow-500 to-orange-500 text-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-yellow-100">الجامعات</p>
                                <p class="text-3xl font-bold"><?php echo $stats['total_universities'] ?? 0; ?></p>
                            </div>
                            <i class="fas fa-university text-4xl text-yellow-200"></i>
                        </div>
                        <div class="mt-4 text-sm text-yellow-100">
                            <i class="fas fa-globe ml-1"></i>
                            جامعات مختلفة
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-purple-500 to-pink-500 text-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100">الأنشطة اليوم</p>
                                <p class="text-3xl font-bold"><?php echo $stats['today_activities'] ?? 0; ?></p>
                            </div>
                            <i class="fas fa-activity text-4xl text-purple-200"></i>
                        </div>
                        <div class="mt-4 text-sm text-purple-100">
                            <i class="fas fa-clock ml-1"></i>
                            آخر 24 ساعة
                        </div>
                    </div>
                </div>

                <!-- Charts and Tables Row -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Grade Distribution Chart -->
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">توزيع التقديرات</h3>
                        <canvas id="gradeChart" width="400" height="200"></canvas>
                    </div>
                    
                    <!-- University Statistics -->
                    <div class="bg-white p-6 rounded-xl shadow-lg">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">إحصائيات الجامعات</h3>
                        <div class="space-y-4">
                            <?php foreach ($universityStats as $uni): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-800"><?php echo htmlspecialchars($uni['university']); ?></p>
                                    <p class="text-sm text-gray-600">معدل: <?php echo $uni['avg_gpa']; ?></p>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-bold text-blue-600"><?php echo number_format($uni['student_count']); ?></p>
                                    <p class="text-xs text-gray-500">طالب</p>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Students -->
                <div class="bg-white rounded-xl shadow-lg">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold text-gray-800">الطلاب الجدد</h3>
                    </div>
                    <div class="divide-y">
                        <?php foreach ($recentStudents as $student): ?>
                        <div class="activity-item p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-user-graduate text-green-600 text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-800">
                                            <?php echo htmlspecialchars($student['name']); ?>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            <?php echo htmlspecialchars($student['university']); ?> - معدل: <?php echo $student['cumulative_gpa']; ?>
                                        </p>
                                    </div>
                                </div>
                                <span class="text-xs text-gray-500">
                                    <?php echo date('H:i', strtotime($student['created_at'])); ?>
                                </span>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <?php if (empty($recentStudents)): ?>
                        <div class="p-8 text-center text-gray-500">
                            <i class="fas fa-users text-4xl mb-4"></i>
                            <p>لا توجد بيانات طلاب حتى الآن</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Grade Distribution Chart
        const gradeCtx = document.getElementById('gradeChart').getContext('2d');
        const gradeData = <?php echo json_encode($gradeDistribution); ?>;
        
        new Chart(gradeCtx, {
            type: 'doughnut',
            data: {
                labels: gradeData.map(item => item.grade_category),
                datasets: [{
                    data: gradeData.map(item => item.count),
                    backgroundColor: [
                        '#10B981', // Green for excellent
                        '#3B82F6', // Blue for very good high
                        '#6366F1', // Indigo for very good
                        '#8B5CF6', // Purple for good high
                        '#F59E0B', // Amber for good
                        '#EF4444'  // Red for below good
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
        
        // Auto-refresh dashboard every 5 minutes
        setInterval(() => {
            location.reload();
        }, 300000);
        
        // Mobile sidebar toggle
        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('collapsed');
        }
    </script>
</body>
</html>
