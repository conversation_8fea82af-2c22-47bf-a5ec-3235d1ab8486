/**
 * GPA Calculator Custom Styles
 * PHP Version - Enhanced with all original features
 */

/* RTL/LTR Support */
.rtl { direction: rtl; }
.ltr { direction: ltr; }

/* Enhanced Course Cards */
.course-item {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.course-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.course-item:hover {
    border-color: #3B82F6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.course-item:hover::before {
    opacity: 1;
}

.course-item.error {
    border-color: #EF4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

/* Enhanced Grade Cards */
.grade-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.grade-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.grade-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.grade-card:hover::before {
    left: 100%;
}

/* Enhanced Navigation */
nav {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

/* Enhanced Buttons */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

/* Enhanced Modal Animations */
.modal-enter {
    animation: modalEnter 0.3s ease-out;
}

@keyframes modalEnter {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Chat Container Enhancements */
#chatContainer {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
}

.chat-message {
    animation: messageSlide 0.3s ease-out;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* GPA Results */
.gpa-result {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Classification Colors */
.classification-excellent { 
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); 
}

.classification-very-good-high { 
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); 
}

.classification-very-good { 
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); 
}

.classification-good-high { 
    background: linear-gradient(135deg, #10b981 0%, #059669 100%); 
}

.classification-good { 
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); 
}

.classification-pass { 
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); 
}

.classification-fail { 
    background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%); 
}

/* Loading States */
.loading {
    display: none;
}

.loading.active {
    display: inline-block;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.course-item {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { 
        opacity: 0; 
        transform: translateX(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateX(0); 
    }
}

/* RTL specific animations */
.rtl .course-item {
    animation: slideInRTL 0.3s ease-out;
}

@keyframes slideInRTL {
    from { 
        opacity: 0; 
        transform: translateX(-20px); 
    }
    to { 
        opacity: 1; 
        transform: translateX(0); 
    }
}

/* Button Hover Effects */
.btn-hover {
    transition: all 0.3s ease;
}

.btn-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Form Focus States */
.form-input:focus {
    outline: none;
    ring: 2px;
    ring-color: #3b82f6;
    border-color: transparent;
}

/* Progress Bar Animation */
.progress-bar {
    transition: width 0.8s ease-in-out;
}

/* Alert Animations */
.alert-enter {
    animation: alertSlideIn 0.3s ease-out;
}

@keyframes alertSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.alert-exit {
    animation: alertSlideOut 0.3s ease-in;
}

@keyframes alertSlideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .course-item {
        padding: 1rem;
    }
    
    .course-item .grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .gpa-result .text-3xl {
        font-size: 2rem;
    }
    
    .classification-card .text-lg {
        font-size: 1rem;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        background: white !important;
    }
    
    .bg-gradient-to-r,
    .bg-gradient-to-br {
        background: #3b82f6 !important;
        color: white !important;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    .dark-mode-auto {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .dark-mode-auto .bg-white {
        background-color: #374151;
    }
    
    .dark-mode-auto .text-gray-800 {
        color: #f9fafb;
    }
    
    .dark-mode-auto .text-gray-600 {
        color: #d1d5db;
    }
    
    .dark-mode-auto .border-gray-300 {
        border-color: #4b5563;
    }
}

/* Accessibility Improvements */
.focus-visible:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }
    
    .card {
        border: 1px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Loading Spinner */
.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Tooltip Styles */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}
